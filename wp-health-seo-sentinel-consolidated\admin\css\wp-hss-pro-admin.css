/**
 * Admin CSS for WP Health & SEO Sentinel Pro
 */

/* Variables */
:root {
    --wp-hss-primary: #2271b1;
    --wp-hss-primary-dark: #135e96;
    --wp-hss-success: #00a32a;
    --wp-hss-warning: #dba617;
    --wp-hss-danger: #d63638;
    --wp-hss-info: #72aee6;
    --wp-hss-light-gray: #f0f0f1;
    --wp-hss-gray: #dcdcde;
    --wp-hss-dark-gray: #50575e;
    --wp-hss-text: #1d2327;
    --wp-hss-border-radius: 4px;
    --wp-hss-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

/* General Styles */
.wp-hss-container {
    max-width: 1200px;
    margin: 20px 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, Oxygen-San<PERSON>, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    color: var(--wp-hss-text);
}

.wp-hss-card {
    background: #fff;
    border: 1px solid var(--wp-hss-gray);
    border-radius: var(--wp-hss-border-radius);
    box-shadow: var(--wp-hss-box-shadow);
    margin-bottom: 20px;
    padding: 20px;
}

.wp-hss-card h2 {
    margin-top: 0;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--wp-hss-light-gray);
    display: flex;
    align-items: center;
}

.wp-hss-card h2 .dashicons {
    margin-right: 8px;
    color: var(--wp-hss-primary);
}

.wp-hss-card-content {
    margin-top: 15px;
}

.wp-hss-card-footer {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--wp-hss-light-gray);
    display: flex;
    justify-content: flex-end;
}

/* Header */
.wp-hss-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--wp-hss-light-gray);
}

.wp-hss-header-content {
    flex: 1;
}

.wp-hss-logo {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.wp-hss-logo h1 {
    margin: 0 0 0 10px;
    font-size: 24px;
    font-weight: 600;
}

.wp-hss-header-actions {
    display: flex;
    gap: 10px;
}

/* Buttons */
.wp-hss-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: var(--wp-hss-border-radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

.wp-hss-button .dashicons {
    margin-right: 6px;
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.wp-hss-button-primary {
    background-color: var(--wp-hss-primary);
    color: #fff;
    border-color: var(--wp-hss-primary);
}

.wp-hss-button-primary:hover {
    background-color: var(--wp-hss-primary-dark);
    border-color: var(--wp-hss-primary-dark);
}

.wp-hss-button-success {
    background-color: var(--wp-hss-success);
    color: #fff;
    border-color: var(--wp-hss-success);
}

.wp-hss-button-warning {
    background-color: var(--wp-hss-warning);
    color: #fff;
    border-color: var(--wp-hss-warning);
}

.wp-hss-button-danger {
    background-color: var(--wp-hss-danger);
    color: #fff;
    border-color: var(--wp-hss-danger);
}

.wp-hss-button-outline {
    background-color: transparent;
    color: var(--wp-hss-primary);
    border-color: var(--wp-hss-primary);
}

.wp-hss-button-outline:hover {
    background-color: var(--wp-hss-primary);
    color: #fff;
}

.wp-hss-button-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.wp-hss-button-lg {
    padding: 12px 24px;
    font-size: 16px;
}

.wp-hss-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Dashboard Grid */
.wp-hss-dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

/* Stat Cards */
.wp-hss-stat-card {
    background: #fff;
    border: 1px solid var(--wp-hss-gray);
    border-radius: var(--wp-hss-border-radius);
    padding: 20px;
    box-shadow: var(--wp-hss-box-shadow);
}

.wp-hss-stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.wp-hss-stat-title {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
}

.wp-hss-stat-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--wp-hss-light-gray);
}

.wp-hss-stat-icon.good {
    background-color: rgba(0, 163, 42, 0.1);
    color: var(--wp-hss-success);
}

.wp-hss-stat-icon.warning {
    background-color: rgba(219, 166, 23, 0.1);
    color: var(--wp-hss-warning);
}

.wp-hss-stat-icon.critical {
    background-color: rgba(214, 54, 56, 0.1);
    color: var(--wp-hss-danger);
}

.wp-hss-stat-icon.info {
    background-color: rgba(114, 174, 230, 0.1);
    color: var(--wp-hss-info);
}

.wp-hss-stat-value {
    font-size: 32px;
    font-weight: 700;
    margin: 0 0 10px 0;
}

.wp-hss-stat-description {
    margin: 0;
    color: var(--wp-hss-dark-gray);
    font-size: 13px;
}

/* Tabs */
.wp-hss-tabs {
    display: flex;
    border-bottom: 1px solid var(--wp-hss-gray);
    margin-bottom: 20px;
}

.wp-hss-tab {
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.wp-hss-tab .dashicons {
    margin-right: 6px;
}

.wp-hss-tab.active {
    border-bottom-color: var(--wp-hss-primary);
    color: var(--wp-hss-primary);
}

.wp-hss-tab-content {
    display: none;
}

.wp-hss-tab-content.active {
    display: block;
}

/* Info Box */
.wp-hss-info-box {
    background-color: rgba(114, 174, 230, 0.1);
    border-left: 4px solid var(--wp-hss-info);
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 0 var(--wp-hss-border-radius) var(--wp-hss-border-radius) 0;
}

.wp-hss-info-box h3 {
    margin-top: 0;
    display: flex;
    align-items: center;
}

.wp-hss-info-box h3 .dashicons {
    margin-right: 6px;
    color: var(--wp-hss-info);
}

.wp-hss-info-box ul {
    margin-bottom: 0;
}

/* Warning Box */
.wp-hss-warning-box {
    background-color: rgba(219, 166, 23, 0.1);
    border-left: 4px solid var(--wp-hss-warning);
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 0 var(--wp-hss-border-radius) var(--wp-hss-border-radius) 0;
}

.wp-hss-warning-box h3 {
    margin-top: 0;
    display: flex;
    align-items: center;
    color: var(--wp-hss-warning);
}

.wp-hss-warning-box h3 .dashicons {
    margin-right: 6px;
    color: var(--wp-hss-warning);
}

/* Success Box */
.wp-hss-success-box {
    background-color: rgba(0, 163, 42, 0.1);
    border-left: 4px solid var(--wp-hss-success);
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 0 var(--wp-hss-border-radius) var(--wp-hss-border-radius) 0;
}

.wp-hss-success-box h3 {
    margin-top: 0;
    display: flex;
    align-items: center;
    color: var(--wp-hss-success);
}

.wp-hss-success-box h3 .dashicons {
    margin-right: 6px;
    color: var(--wp-hss-success);
}

/* Error Box */
.wp-hss-error-box {
    background-color: rgba(214, 54, 56, 0.1);
    border-left: 4px solid var(--wp-hss-danger);
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 0 var(--wp-hss-border-radius) var(--wp-hss-border-radius) 0;
}

.wp-hss-error-box h3 {
    margin-top: 0;
    display: flex;
    align-items: center;
    color: var(--wp-hss-danger);
}

.wp-hss-error-box h3 .dashicons {
    margin-right: 6px;
    color: var(--wp-hss-danger);
}

/* Results Table */
.wp-hss-results-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.wp-hss-results-table th,
.wp-hss-results-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid var(--wp-hss-light-gray);
}

.wp-hss-results-table th {
    background-color: var(--wp-hss-light-gray);
    font-weight: 600;
}

.wp-hss-results-table tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Issues List */
.wp-hss-issues-list {
    margin-top: 20px;
}

.wp-hss-issue-item {
    padding: 15px;
    border: 1px solid var(--wp-hss-gray);
    border-radius: var(--wp-hss-border-radius);
    margin-bottom: 10px;
    background-color: #fff;
}

.wp-hss-issue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.wp-hss-issue-title {
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}

.wp-hss-issue-severity {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 10px;
}

.wp-hss-issue-severity.critical {
    background-color: rgba(214, 54, 56, 0.1);
    color: var(--wp-hss-danger);
}

.wp-hss-issue-severity.warning {
    background-color: rgba(219, 166, 23, 0.1);
    color: var(--wp-hss-warning);
}

.wp-hss-issue-severity.info {
    background-color: rgba(114, 174, 230, 0.1);
    color: var(--wp-hss-info);
}

.wp-hss-issue-content {
    margin-bottom: 15px;
    color: var(--wp-hss-dark-gray);
}

.wp-hss-issue-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Loading Spinner */
.wp-hss-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.wp-hss-spinner {
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 3px solid var(--wp-hss-primary);
    width: 24px;
    height: 24px;
    animation: wp-hss-spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes wp-hss-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress Bar */
.wp-hss-progress-bar {
    height: 8px;
    background-color: var(--wp-hss-light-gray);
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.wp-hss-progress {
    height: 100%;
    background-color: var(--wp-hss-primary);
    border-radius: 4px;
}

/* Status Indicators */
.wp-hss-status-good {
    color: var(--wp-hss-success);
    font-weight: 500;
}

.wp-hss-status-warning {
    color: var(--wp-hss-warning);
    font-weight: 500;
}

.wp-hss-status-critical {
    color: var(--wp-hss-danger);
    font-weight: 500;
}

/* Responsive */
@media screen and (max-width: 782px) {
    .wp-hss-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .wp-hss-header-actions {
        margin-top: 15px;
    }
    
    .wp-hss-dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .wp-hss-tabs {
        flex-wrap: wrap;
    }
    
    .wp-hss-tab {
        flex: 1 0 auto;
        text-align: center;
        padding: 10px;
    }
}
