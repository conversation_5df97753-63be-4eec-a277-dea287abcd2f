<?php
/**
 * Admin functionality for WP Health & SEO Sentinel
 */
class WP_HSS_Admin {
    /**
     * Initialize the admin functionality
     */
    public function init() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Add settings link to plugins page
        add_filter('plugin_action_links_' . WP_HSS_PLUGIN_BASENAME, array($this, 'add_settings_link'));
        
        // Enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        // Register AJAX handlers
        add_action('wp_ajax_wp_hss_get_scan_history', array($this, 'ajax_get_scan_history'));
        add_action('wp_ajax_wp_hss_save_settings', array($this, 'ajax_save_settings'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Add main menu
        add_menu_page(
            'WP Health & SEO Sentinel',
            'SEO Sentinel',
            'manage_options',
            'wp-hss',
            array($this, 'render_dashboard_page'),
            'dashicons-chart-area',
            100
        );
        
        // Add submenu pages
        add_submenu_page(
            'wp-hss',
            'Dashboard',
            'Dashboard',
            'manage_options',
            'wp-hss',
            array($this, 'render_dashboard_page')
        );
        
        add_submenu_page(
            'wp-hss',
            'Settings',
            'Settings',
            'manage_options',
            'wp-hss-settings',
            array($this, 'render_settings_page')
        );
        
        add_submenu_page(
            'wp-hss',
            'Tools',
            'Tools',
            'manage_options',
            'wp-hss-tools',
            array($this, 'render_tools_page')
        );
    }
    
    /**
     * Add settings link to plugins page
     */
    public function add_settings_link($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=wp-hss-settings') . '">' . __('Settings', 'wp-hss') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_scripts($hook) {
        // Only load on our plugin pages
        if (strpos($hook, 'wp-hss') === false) {
            return;
        }
        
        // Enqueue styles
        wp_enqueue_style('wp-hss-admin', WP_HSS_PLUGIN_URL . 'admin/css/wp-hss-admin.css', array(), WP_HSS_VERSION);
        
        // Enqueue scripts
        wp_enqueue_script('wp-hss-admin', WP_HSS_PLUGIN_URL . 'admin/js/wp-hss-admin.js', array('jquery'), WP_HSS_VERSION, true);
        
        // Localize script
        wp_localize_script('wp-hss-admin', 'wp_hss_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wp_hss_nonce'),
            'strings' => array(
                'scanning' => __('Scanning...', 'wp-hss'),
                'fixing' => __('Fixing...', 'wp-hss'),
                'fix' => __('Fix', 'wp-hss'),
                'fixed' => __('Fixed', 'wp-hss'),
                'fix_complete' => __('Fixed', 'wp-hss'),
                'fix_failed' => __('Failed', 'wp-hss'),
                'fix_error' => __('An error occurred while fixing the issue.', 'wp-hss'),
                'fix_applied' => __('Fix Applied', 'wp-hss')
            )
        ));
    }
    
    /**
     * Render dashboard page
     */
    public function render_dashboard_page() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'wp-hss'));
        }
        
        // Include dashboard template
        include WP_HSS_PLUGIN_DIR . 'admin/partials/dashboard.php';
    }
    
    /**
     * Render settings page
     */
    public function render_settings_page() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'wp-hss'));
        }
        
        // Save settings if form is submitted
        if (isset($_POST['wp_hss_save_settings'])) {
            $this->save_settings();
        }
        
        // Get current settings
        $settings = get_option('wp_hss_settings', array());
        
        // Include settings template
        include WP_HSS_PLUGIN_DIR . 'admin/partials/settings.php';
    }
    
    /**
     * Render tools page
     */
    public function render_tools_page() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'wp-hss'));
        }
        
        // Include tools template
        include WP_HSS_PLUGIN_DIR . 'admin/partials/tools.php';
    }
    
    /**
     * Save settings
     */
    private function save_settings() {
        // Check nonce
        if (!isset($_POST['wp_hss_settings_nonce']) || !wp_verify_nonce($_POST['wp_hss_settings_nonce'], 'wp_hss_settings')) {
            add_settings_error('wp_hss_settings', 'wp_hss_settings_nonce', __('Security check failed.', 'wp-hss'), 'error');
            return;
        }
        
        // Get current settings
        $settings = get_option('wp_hss_settings', array());
        
        // Update settings
        $settings['enable_scanning'] = isset($_POST['enable_scanning']) ? true : false;
        $settings['scan_frequency'] = isset($_POST['scan_frequency']) ? sanitize_text_field($_POST['scan_frequency']) : 'weekly';
        $settings['scan_post_types'] = isset($_POST['scan_post_types']) ? array_map('sanitize_text_field', $_POST['scan_post_types']) : array('post', 'page');
        $settings['ai_enabled'] = isset($_POST['ai_enabled']) ? true : false;
        $settings['ai_provider'] = isset($_POST['ai_provider']) ? sanitize_text_field($_POST['ai_provider']) : 'openai';
        $settings['ai_api_key'] = isset($_POST['ai_api_key']) ? sanitize_text_field($_POST['ai_api_key']) : '';
        $settings['title_min_length'] = isset($_POST['title_min_length']) ? intval($_POST['title_min_length']) : 30;
        $settings['title_max_length'] = isset($_POST['title_max_length']) ? intval($_POST['title_max_length']) : 60;
        $settings['meta_min_length'] = isset($_POST['meta_min_length']) ? intval($_POST['meta_min_length']) : 120;
        $settings['meta_max_length'] = isset($_POST['meta_max_length']) ? intval($_POST['meta_max_length']) : 160;
        $settings['content_min_length'] = isset($_POST['content_min_length']) ? intval($_POST['content_min_length']) : 300;
        
        // Save settings
        update_option('wp_hss_settings', $settings);
        
        // Add success message
        add_settings_error('wp_hss_settings', 'wp_hss_settings_updated', __('Settings saved successfully.', 'wp-hss'), 'success');
    }
    
    /**
     * AJAX handler for getting scan history
     */
    public function ajax_get_scan_history() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed.'));
        }
        
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
        }
        
        // Get scan history
        $scan_history = get_option('wp_hss_scan_history', array());
        
        // Generate HTML
        $html = $this->generate_scan_history_html($scan_history);
        
        // Send response
        wp_send_json_success(array('html' => $html));
    }
    
    /**
     * Generate scan history HTML
     */
    private function generate_scan_history_html($scan_history) {
        ob_start();
        
        if (empty($scan_history)) {
            echo '<div class="wp-hss-info-box">';
            echo '<h3><i class="dashicons dashicons-info"></i> ' . __('No Scan History', 'wp-hss') . '</h3>';
            echo '<p>' . __('You haven\'t performed any scans yet. Run your first scan to see results here.', 'wp-hss') . '</p>';
            echo '</div>';
        } else {
            echo '<table class="wp-hss-results-table">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>' . __('Date', 'wp-hss') . '</th>';
            echo '<th>' . __('Issues Found', 'wp-hss') . '</th>';
            echo '<th>' . __('Health Score', 'wp-hss') . '</th>';
            echo '<th>' . __('Actions', 'wp-hss') . '</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';
            
            foreach ($scan_history as $scan) {
                echo '<tr>';
                echo '<td>' . date('M j, Y g:i a', $scan['time']) . '</td>';
                echo '<td>' . $scan['issues_count'] . '</td>';
                echo '<td>' . $scan['health_score'] . '</td>';
                echo '<td>';
                echo '<button class="wp-hss-button wp-hss-button-sm wp-hss-button-outline wp-hss-view-scan" data-scan-id="' . esc_attr($scan['id']) . '">';
                echo '<i class="dashicons dashicons-visibility"></i> ' . __('View', 'wp-hss');
                echo '</button>';
                echo '</td>';
                echo '</tr>';
            }
            
            echo '</tbody>';
            echo '</table>';
        }
        
        return ob_get_clean();
    }
    
    /**
     * AJAX handler for saving settings
     */
    public function ajax_save_settings() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed.'));
        }
        
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
        }
        
        // Get settings
        $settings = array();
        $settings['enable_scanning'] = isset($_POST['enable_scanning']) ? (bool) $_POST['enable_scanning'] : false;
        $settings['scan_frequency'] = isset($_POST['scan_frequency']) ? sanitize_text_field($_POST['scan_frequency']) : 'weekly';
        $settings['scan_post_types'] = isset($_POST['scan_post_types']) ? array_map('sanitize_text_field', $_POST['scan_post_types']) : array('post', 'page');
        $settings['ai_enabled'] = isset($_POST['ai_enabled']) ? (bool) $_POST['ai_enabled'] : false;
        $settings['ai_provider'] = isset($_POST['ai_provider']) ? sanitize_text_field($_POST['ai_provider']) : 'openai';
        $settings['ai_api_key'] = isset($_POST['ai_api_key']) ? sanitize_text_field($_POST['ai_api_key']) : '';
        $settings['title_min_length'] = isset($_POST['title_min_length']) ? intval($_POST['title_min_length']) : 30;
        $settings['title_max_length'] = isset($_POST['title_max_length']) ? intval($_POST['title_max_length']) : 60;
        $settings['meta_min_length'] = isset($_POST['meta_min_length']) ? intval($_POST['meta_min_length']) : 120;
        $settings['meta_max_length'] = isset($_POST['meta_max_length']) ? intval($_POST['meta_max_length']) : 160;
        $settings['content_min_length'] = isset($_POST['content_min_length']) ? intval($_POST['content_min_length']) : 300;
        
        // Save settings
        update_option('wp_hss_settings', $settings);
        
        // Send response
        wp_send_json_success(array('message' => 'Settings saved successfully.'));
    }
}
