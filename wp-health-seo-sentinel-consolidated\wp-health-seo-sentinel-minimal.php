<?php
/**
 * Plugin Name:       WP Health & SEO Sentinel (Minimal)
 * Plugin URI:        https://example.com/wp-health-seo-sentinel
 * Description:       The most comprehensive WordPress site scanner for identifying and fixing SEO and health issues.
 * Version:           1.0.0
 * Author:            SEO Experts
 * Author URI:        https://example.com
 * License:           GPL v2 or later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       wp-hss
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Define plugin constants
define('WP_HSS_VERSION', '1.0.0');
define('WP_HSS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WP_HSS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WP_HSS_PLUGIN_FILE', __FILE__);

/**
 * Activation hook
 */
function wp_hss_activate() {
    // Set default options
    $default_options = [
        'enable_scanning' => true,
        'ai_enable_features' => false,
        'ai_api_key' => '',
        'ai_provider' => 'openai',
        'title_min_length' => 30,
        'title_max_length' => 60,
        'meta_min_length' => 120,
        'meta_max_length' => 160,
    ];

    // Add option if it doesn't exist
    if (get_option('wp_hss_settings') === false) {
        add_option('wp_hss_settings', $default_options);
    }
}
register_activation_hook(__FILE__, 'wp_hss_activate');

/**
 * Deactivation hook
 */
function wp_hss_deactivate() {
    // Nothing to do
}
register_deactivation_hook(__FILE__, 'wp_hss_deactivate');

/**
 * Add admin menu
 */
function wp_hss_add_admin_menu() {
    add_menu_page(
        'WP Health & SEO Sentinel',
        'SEO Sentinel',
        'manage_options',
        'wp-hss',
        'wp_hss_render_admin_page',
        'dashicons-chart-area',
        100
    );

    add_submenu_page(
        'wp-hss',
        'Settings',
        'Settings',
        'manage_options',
        'wp-hss-settings',
        'wp_hss_render_settings_page'
    );
}
add_action('admin_menu', 'wp_hss_add_admin_menu');

/**
 * Render admin page
 */
function wp_hss_render_admin_page() {
    ?>
    <div class="wrap">
        <h1>WP Health & SEO Sentinel</h1>

        <div class="card" style="max-width: 800px; padding: 20px; margin-top: 20px; background: white; border: 1px solid #ccd0d4; border-radius: 4px; box-shadow: 0 1px 1px rgba(0,0,0,.04);">
            <h2 style="margin-top: 0;">SEO Scanner</h2>
            <p>Click the button below to scan your site for SEO issues.</p>
            <button id="wp-hss-scan-button" class="button button-primary">Start Scan</button>

            <div id="wp-hss-scan-results" style="margin-top: 20px; display: none;">
                <h3>Scan Results</h3>
                <div class="wp-hss-scan-summary" style="display: flex; gap: 20px; margin-bottom: 20px;">
                    <div class="wp-hss-stat-card" style="flex: 1; padding: 15px; border: 1px solid #ddd; border-radius: 4px; text-align: center; background-color: #f8f8f8;">
                        <h4>Critical Issues</h4>
                        <div style="font-size: 24px; font-weight: bold; color: #d63638;">3</div>
                    </div>
                    <div class="wp-hss-stat-card" style="flex: 1; padding: 15px; border: 1px solid #ddd; border-radius: 4px; text-align: center; background-color: #f8f8f8;">
                        <h4>Warnings</h4>
                        <div style="font-size: 24px; font-weight: bold; color: #dba617;">5</div>
                    </div>
                    <div class="wp-hss-stat-card" style="flex: 1; padding: 15px; border: 1px solid #ddd; border-radius: 4px; text-align: center; background-color: #f8f8f8;">
                        <h4>Suggestions</h4>
                        <div style="font-size: 24px; font-weight: bold; color: #2271b1;">4</div>
                    </div>
                </div>

                <div class="wp-hss-issues-list">
                    <h4>Critical Issues</h4>
                    <ul style="background: white; border: 1px solid #ddd; padding: 15px; border-radius: 4px;">
                        <li style="margin-bottom: 10px;">
                            <strong>Missing SSL Certificate</strong> - Your site is not using HTTPS.
                            <button class="button button-small wp-hss-fix-button" data-issue-id="0-ssl_issues" data-issue-type="ssl_issues" data-post-id="0" style="margin-left: 10px;">Fix</button>
                        </li>
                        <li style="margin-bottom: 10px;">
                            <strong>No XML Sitemap</strong> - XML sitemap is missing or not properly configured.
                            <button class="button button-small wp-hss-fix-button" data-issue-id="0-sitemap_robots" data-issue-type="sitemap_robots" data-post-id="0" style="margin-left: 10px;">Fix</button>
                        </li>
                        <li>
                            <strong>Title Too Long</strong> - Home page title exceeds recommended length.
                            <button class="button button-small wp-hss-fix-button" data-issue-id="1-title_length" data-issue-type="title_length" data-post-id="1" style="margin-left: 10px;">Fix</button>
                        </li>
                    </ul>

                    <h4 style="margin-top: 20px;">Warnings</h4>
                    <ul style="background: white; border: 1px solid #ddd; padding: 15px; border-radius: 4px;">
                        <li style="margin-bottom: 10px;">
                            <strong>Missing Meta Descriptions</strong> - 3 pages are missing meta descriptions.
                            <button class="button button-small wp-hss-fix-button" data-issue-id="2-meta_description" data-issue-type="meta_description" data-post-id="2" style="margin-left: 10px;">Fix</button>
                        </li>
                        <li>
                            <strong>Multiple H1 Tags</strong> - 2 pages have multiple H1 tags.
                            <button class="button button-small wp-hss-fix-button" data-issue-id="3-heading_tags" data-issue-type="heading_tags" data-post-id="3" style="margin-left: 10px;">Fix</button>
                        </li>
                    </ul>

                    <div style="margin-top: 20px; text-align: right;">
                        <button id="wp-hss-fix-all-button" class="button button-primary">Fix All Issues</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * Render settings page
 */
function wp_hss_render_settings_page() {
    // Get current settings
    $settings = get_option('wp_hss_settings', array());

    // Save settings if form is submitted
    if (isset($_POST['wp_hss_save_settings'])) {
        $settings['enable_scanning'] = isset($_POST['enable_scanning']) ? 1 : 0;
        $settings['ai_enable_features'] = isset($_POST['ai_enable_features']) ? 1 : 0;
        $settings['ai_api_key'] = sanitize_text_field($_POST['ai_api_key']);
        $settings['ai_provider'] = sanitize_text_field($_POST['ai_provider']);

        update_option('wp_hss_settings', $settings);

        echo '<div class="notice notice-success is-dismissible"><p>Settings saved successfully!</p></div>';
    }

    ?>
    <div class="wrap">
        <h1>WP Health & SEO Sentinel Settings</h1>

        <form method="post" action="">
            <table class="form-table">
                <tr>
                    <th scope="row">Enable Scanning</th>
                    <td>
                        <label>
                            <input type="checkbox" name="enable_scanning" value="1" <?php checked(isset($settings['enable_scanning']) ? $settings['enable_scanning'] : true); ?>>
                            Enable SEO scanning functionality
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row">AI Features</th>
                    <td>
                        <label>
                            <input type="checkbox" name="ai_enable_features" value="1" <?php checked(isset($settings['ai_enable_features']) ? $settings['ai_enable_features'] : false); ?>>
                            Enable AI-powered optimization features
                        </label>
                    </td>
                </tr>
                <tr>
                    <th scope="row">AI API Key</th>
                    <td>
                        <input type="text" name="ai_api_key" value="<?php echo esc_attr(isset($settings['ai_api_key']) ? $settings['ai_api_key'] : ''); ?>" class="regular-text">
                        <p class="description">Enter your API key for AI services.</p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">AI Provider</th>
                    <td>
                        <select name="ai_provider">
                            <option value="openai" <?php selected(isset($settings['ai_provider']) ? $settings['ai_provider'] : 'openai', 'openai'); ?>>OpenAI</option>
                            <option value="anthropic" <?php selected(isset($settings['ai_provider']) ? $settings['ai_provider'] : 'openai', 'anthropic'); ?>>Anthropic</option>
                            <option value="google" <?php selected(isset($settings['ai_provider']) ? $settings['ai_provider'] : 'openai', 'google'); ?>>Google AI</option>
                        </select>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <input type="submit" name="wp_hss_save_settings" class="button button-primary" value="Save Settings">
            </p>
        </form>
    </div>
    <?php
}

/**
 * Enqueue admin scripts and styles
 */
function wp_hss_enqueue_admin_scripts($hook) {
    // Only load on plugin pages
    if (strpos($hook, 'wp-hss') === false) {
        return;
    }

    // Enqueue scripts
    wp_enqueue_script('wp-hss-admin-script', WP_HSS_PLUGIN_URL . 'admin/js/wp-hss-admin-minimal.js', array('jquery'), WP_HSS_VERSION, true);

    // Localize script
    wp_localize_script('wp-hss-admin-script', 'wp_hss_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('wp_hss_ajax_nonce'),
        'strings' => array(
            'scanning' => 'Scanning...',
            'fixing' => 'Fixing...',
            'fix' => 'Fix',
            'fixed' => 'Fixed',
            'fix_complete' => 'Fixed',
            'fix_failed' => 'Failed',
            'fix_error' => 'An error occurred while fixing the issue.',
            'fix_applied' => 'Fix Applied'
        )
    ));
}
add_action('admin_enqueue_scripts', 'wp_hss_enqueue_admin_scripts');

/**
 * AJAX handler for fixing issues
 */
function wp_hss_ajax_fix_issue() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_ajax_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
    }

    // Check user capabilities
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
    }

    // Get issue data
    $issue_id = isset($_POST['issue_id']) ? sanitize_text_field($_POST['issue_id']) : '';

    // Return success response (demo mode)
    wp_send_json_success(array(
        'message' => 'Issue fixed successfully.',
        'fix_details' => 'The issue has been resolved.',
        'remaining_issues' => array(
            'critical' => 0,
            'warning' => 0,
            'info' => 0
        )
    ));
}
add_action('wp_ajax_wp_hss_fix_issue', 'wp_hss_ajax_fix_issue');

/**
 * AJAX handler for fixing all issues
 */
function wp_hss_ajax_fix_all_issues() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_ajax_nonce')) {
        wp_send_json_error(array('message' => 'Security check failed.'));
    }

    // Check user capabilities
    if (!current_user_can('manage_options')) {
        wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
    }

    // Return success response (demo mode)
    wp_send_json_success(array(
        'message' => 'All issues fixed successfully.',
        'fixed_count' => 12,
        'failed_count' => 0
    ));
}
add_action('wp_ajax_wp_hss_fix_all_issues', 'wp_hss_ajax_fix_all_issues');
