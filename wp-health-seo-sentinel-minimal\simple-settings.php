<?php
/**
 * ULTRA SIMPLE SETTINGS - G<PERSON><PERSON>ANTEED TO WORK
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to access this page.');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_settings'])) {
    
    // Simple direct save - no fancy WordPress API
    $gemini_key = isset($_POST['gemini_key']) ? sanitize_text_field($_POST['gemini_key']) : '';
    $enable_gemini = isset($_POST['enable_gemini']) ? 1 : 0;
    
    // Save directly to WordPress options
    $result1 = update_option('wp_hss_gemini_api_key', $gemini_key);
    $result2 = update_option('wp_hss_enable_gemini', $enable_gemini);
    
    // Show success message
    if ($result1 !== false || $result2 !== false) {
        echo '<div style="background: #4CAF50; color: white; padding: 20px; margin: 20px 0; border-radius: 8px; font-size: 16px; font-weight: bold;">
            🎉 SUCCESS! Your Gemini API key has been saved!
        </div>';
    } else {
        echo '<div style="background: #f44336; color: white; padding: 20px; margin: 20px 0; border-radius: 8px; font-size: 16px; font-weight: bold;">
            ❌ ERROR: Could not save settings. Please try again.
        </div>';
    }
}

// Get current values
$current_gemini_key = get_option('wp_hss_gemini_api_key', '');
$current_enable_gemini = get_option('wp_hss_enable_gemini', 0);
?>

<div style="max-width: 900px; margin: 30px auto; padding: 30px; background: white; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
    
    <h1 style="color: #007cba; margin-bottom: 30px; text-align: center;">
        🚀 Ultra Simple Settings - Google Gemini Setup
    </h1>
    
    <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin-bottom: 30px;">
        <h3 style="margin-top: 0; color: #1976d2;">📋 Current Status:</h3>
        <ul style="font-size: 16px; line-height: 1.6;">
            <li><strong>Gemini Enabled:</strong> <?php echo $current_enable_gemini ? '✅ YES' : '❌ NO'; ?></li>
            <li><strong>API Key Status:</strong> <?php echo $current_gemini_key ? '✅ Set (' . strlen($current_gemini_key) . ' characters)' : '❌ Not set'; ?></li>
            <?php if ($current_gemini_key): ?>
            <li><strong>Key Preview:</strong> <?php echo substr($current_gemini_key, 0, 8) . '...' . substr($current_gemini_key, -4); ?></li>
            <?php endif; ?>
        </ul>
    </div>
    
    <form method="POST" action="" style="background: #f8f9fa; padding: 30px; border-radius: 8px; border: 2px solid #007cba;">
        
        <h2 style="color: #007cba; margin-top: 0;">🔑 Enter Your Google Gemini API Key</h2>
        
        <div style="margin-bottom: 25px;">
            <label style="display: block; font-weight: bold; margin-bottom: 10px; font-size: 16px;">
                <input type="checkbox" name="enable_gemini" value="1" <?php checked($current_enable_gemini, 1); ?> style="margin-right: 10px;">
                🔥 Enable Google Gemini AI
            </label>
        </div>
        
        <div style="margin-bottom: 25px;">
            <label style="display: block; font-weight: bold; margin-bottom: 10px; font-size: 16px;">
                Google Gemini API Key:
            </label>
            <input type="text" 
                   name="gemini_key" 
                   value="<?php echo esc_attr($current_gemini_key); ?>" 
                   placeholder="AIzaSy..." 
                   style="width: 100%; padding: 12px; font-size: 16px; border: 2px solid #007cba; border-radius: 5px; box-sizing: border-box;">
            <small style="color: #666; display: block; margin-top: 5px;">
                Get your free API key from: <a href="https://makersuite.google.com/app/apikey" target="_blank" style="color: #007cba;">Google AI Studio</a>
            </small>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <input type="submit" 
                   name="submit_settings" 
                   value="💾 Save Gemini Settings" 
                   style="background: #007cba; color: white; padding: 15px 30px; font-size: 18px; font-weight: bold; border: none; border-radius: 8px; cursor: pointer; box-shadow: 0 2px 4px rgba(0,0,0,0.2);">
        </div>
        
    </form>
    
    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; margin-top: 30px; border-radius: 8px;">
        <h3 style="margin-top: 0; color: #856404;">📝 Instructions:</h3>
        <ol style="font-size: 16px; line-height: 1.8;">
            <li>Visit <a href="https://makersuite.google.com/app/apikey" target="_blank" style="color: #007cba;">Google AI Studio</a></li>
            <li>Click "Create API Key"</li>
            <li>Copy the generated key (starts with "AIzaSy...")</li>
            <li>Paste it in the field above</li>
            <li>Check "Enable Google Gemini AI"</li>
            <li>Click "Save Gemini Settings"</li>
        </ol>
    </div>
    
</div>

<script>
// Auto-refresh status after save
if (window.location.search.includes('saved=1')) {
    setTimeout(function() {
        window.location.href = window.location.href.split('?')[0];
    }, 3000);
}
</script>
