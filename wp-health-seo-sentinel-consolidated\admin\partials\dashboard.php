<?php
/**
 * Dashboard template for WP Health & SEO Sentinel
 */

// Get stats
$settings = get_option('wp_hss_settings', array());
$issues = get_option('wp_hss_issues', array());

// Count issues by severity
$critical_count = 0;
$warning_count = 0;
$info_count = 0;

foreach ($issues as $issue) {
    if (isset($issue['severity'])) {
        if ($issue['severity'] === 'critical') {
            $critical_count++;
        } elseif ($issue['severity'] === 'warning') {
            $warning_count++;
        } elseif ($issue['severity'] === 'info') {
            $info_count++;
        }
    }
}

// Calculate health score
$total_issues = $critical_count + $warning_count + $info_count;
$health_score = $total_issues > 0 ? max(0, 100 - ($critical_count * 10) - ($warning_count * 5) - ($info_count * 2)) : 100;
?>

<div class="wrap">
    <div class="wp-hss-container">
        <div class="wp-hss-header">
            <div class="wp-hss-header-content">
                <div class="wp-hss-logo">
                    <span class="dashicons dashicons-chart-area" style="font-size: 32px; color: #2271b1;"></span>
                    <h1><?php _e('WP Health & SEO Sentinel', 'wp-hss'); ?></h1>
                </div>
                <p><?php _e('Comprehensive SEO analysis and optimization for WordPress', 'wp-hss'); ?></p>
            </div>
            <div class="wp-hss-header-actions">
                <a href="<?php echo admin_url('admin.php?page=wp-hss-settings'); ?>" class="wp-hss-button wp-hss-button-outline">
                    <i class="dashicons dashicons-admin-settings"></i>
                    <?php _e('Settings', 'wp-hss'); ?>
                </a>
                <button id="wp-hss-scan-button" class="wp-hss-button wp-hss-button-primary">
                    <i class="dashicons dashicons-search"></i>
                    <?php _e('Start Scan', 'wp-hss'); ?>
                </button>
            </div>
        </div>

        <!-- Dashboard Overview -->
        <div class="wp-hss-overview">
            <div class="wp-hss-dashboard-grid">
                <!-- Health Score -->
                <div class="wp-hss-stat-card">
                    <div class="wp-hss-stat-header">
                        <h3 class="wp-hss-stat-title"><?php _e('SEO Health Score', 'wp-hss'); ?></h3>
                        <div class="wp-hss-stat-icon <?php echo $health_score >= 80 ? 'good' : ($health_score >= 50 ? 'warning' : 'critical'); ?>">
                            <i class="dashicons dashicons-chart-line"></i>
                        </div>
                    </div>
                    <p id="wp-hss-health-score" class="wp-hss-stat-value" style="color: <?php echo $health_score >= 80 ? '#00a32a' : ($health_score >= 50 ? '#dba617' : '#d63638'); ?>">
                        <?php echo $health_score; ?>
                    </p>
                    <p class="wp-hss-stat-description">
                        <?php
                        if ($health_score >= 80) {
                            _e('Your site is performing well!', 'wp-hss');
                        } elseif ($health_score >= 50) {
                            _e('Your site needs some improvements.', 'wp-hss');
                        } else {
                            _e('Your site needs significant SEO work.', 'wp-hss');
                        }
                        ?>
                    </p>
                </div>

                <!-- Critical Issues -->
                <div class="wp-hss-stat-card">
                    <div class="wp-hss-stat-header">
                        <h3 class="wp-hss-stat-title"><?php _e('Critical Issues', 'wp-hss'); ?></h3>
                        <div class="wp-hss-stat-icon critical">
                            <i class="dashicons dashicons-warning"></i>
                        </div>
                    </div>
                    <p id="wp-hss-stat-critical" class="wp-hss-stat-value" style="color: #d63638;"><?php echo $critical_count; ?></p>
                    <p class="wp-hss-stat-description">
                        <?php _e('Issues that need immediate attention', 'wp-hss'); ?>
                    </p>
                </div>

                <!-- Warnings -->
                <div class="wp-hss-stat-card">
                    <div class="wp-hss-stat-header">
                        <h3 class="wp-hss-stat-title"><?php _e('Warnings', 'wp-hss'); ?></h3>
                        <div class="wp-hss-stat-icon warning">
                            <i class="dashicons dashicons-flag"></i>
                        </div>
                    </div>
                    <p id="wp-hss-stat-warning" class="wp-hss-stat-value" style="color: #dba617;"><?php echo $warning_count; ?></p>
                    <p class="wp-hss-stat-description">
                        <?php _e('Issues that should be addressed', 'wp-hss'); ?>
                    </p>
                </div>

                <!-- Info/Suggestions -->
                <div class="wp-hss-stat-card">
                    <div class="wp-hss-stat-header">
                        <h3 class="wp-hss-stat-title"><?php _e('Suggestions', 'wp-hss'); ?></h3>
                        <div class="wp-hss-stat-icon info">
                            <i class="dashicons dashicons-info"></i>
                        </div>
                    </div>
                    <p id="wp-hss-stat-info" class="wp-hss-stat-value" style="color: #72aee6;"><?php echo $info_count; ?></p>
                    <p class="wp-hss-stat-description">
                        <?php _e('Opportunities for improvement', 'wp-hss'); ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- Scanner Section -->
        <div class="wp-hss-card">
            <h2><i class="dashicons dashicons-search"></i> <?php _e('SEO Scanner', 'wp-hss'); ?></h2>
            <div class="wp-hss-card-content">
                <p><?php _e('The SEO Scanner analyzes your website for common SEO issues and provides actionable recommendations to improve your search engine rankings.', 'wp-hss'); ?></p>

                <div class="wp-hss-info-box">
                    <h3><i class="dashicons dashicons-info"></i> <?php _e('What We Check', 'wp-hss'); ?></h3>
                    <ul>
                        <li><?php _e('Meta titles and descriptions', 'wp-hss'); ?></li>
                        <li><?php _e('Content quality and keyword optimization', 'wp-hss'); ?></li>
                        <li><?php _e('Image alt text and accessibility', 'wp-hss'); ?></li>
                        <li><?php _e('Heading structure and internal linking', 'wp-hss'); ?></li>
                        <li><?php _e('Schema markup and technical SEO factors', 'wp-hss'); ?></li>
                        <li><?php _e('Mobile-friendliness and page speed', 'wp-hss'); ?></li>
                    </ul>
                </div>
            </div>
            <div class="wp-hss-card-footer">
                <button id="wp-hss-scan-button-card" class="wp-hss-button wp-hss-button-primary wp-hss-button-lg">
                    <i class="dashicons dashicons-search"></i>
                    <?php _e('Start Comprehensive Scan', 'wp-hss'); ?>
                </button>
            </div>
        </div>

        <!-- Scan Results -->
        <div id="wp-hss-scan-results">
            <?php
            // Show sample results if no scan has been run
            if (empty($issues)) {
                ?>
                <div class="wp-hss-info-box">
                    <h3><i class="dashicons dashicons-info"></i> <?php _e('No Scan Results', 'wp-hss'); ?></h3>
                    <p><?php _e('Run your first scan to see results here.', 'wp-hss'); ?></p>
                </div>
                <?php
            }
            ?>
        </div>
    </div>
</div>

<style>
/* Add some basic styles for the dashboard */
.wp-hss-spin {
    animation: wp-hss-spin 2s linear infinite;
}

@keyframes wp-hss-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
