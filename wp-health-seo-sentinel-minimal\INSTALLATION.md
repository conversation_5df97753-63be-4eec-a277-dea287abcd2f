# Installation Guide - WP Health SEO Sentinel

## Quick Installation

### Method 1: Direct Upload
1. Download the `wp-health-seo-sentinel-minimal` folder
2. Upload it to your WordPress site's `/wp-content/plugins/` directory
3. Go to WordPress Admin > Plugins
4. Find "WP Health SEO Sentinel" and click "Activate"

### Method 2: ZIP Upload
1. Create a ZIP file of the `wp-health-seo-sentinel-minimal` folder
2. Go to WordPress Admin > Plugins > Add New
3. Click "Upload Plugin"
4. Choose your ZIP file and click "Install Now"
5. Click "Activate Plugin"

## First Time Setup

### Step 1: Access the Plugin
After activation, you'll see "SEO Health" in your WordPress admin menu.

### Step 2: Test Settings (Recommended)
1. Go to **SEO Health > Settings Test**
2. This provides a simple interface to configure basic settings
3. Test saving settings to ensure everything works

### Step 3: Configure AI (Optional but Recommended)
1. Choose an AI provider (OpenAI is recommended for beginners)
2. Get an API key from your chosen provider:
   - **OpenAI**: https://platform.openai.com/api-keys
   - **Anthropic**: https://console.anthropic.com/
3. Enter your API key in the Settings Test page
4. Enable the AI provider checkbox

### Step 4: Run Your First Scan
1. Go to **SEO Health > Scanner**
2. Click "Run Full SEO Scan"
3. Wait for the scan to complete
4. Review the results

## Troubleshooting

### Plugin Won't Activate
- Check that you're using WordPress 5.0+ and PHP 7.4+
- Ensure the plugin folder is named `wp-health-seo-sentinel-minimal`
- Check file permissions (folders: 755, files: 644)

### Settings Won't Save
- Try the **Settings Test** page first - it uses a simpler approach
- Check if your hosting provider allows option updates
- Verify you have admin privileges

### Scans Don't Work
- Check if your site can make external HTTP requests
- Verify SSL certificate is working
- Try running a scan on a simple page first

### AI Features Not Working
- Verify your API key is correct
- Check that the AI provider checkbox is enabled
- Ensure your hosting allows external API calls
- Check API usage limits with your provider

## File Permissions

Set these permissions for optimal security:
- Folders: 755 (rwxr-xr-x)
- PHP files: 644 (rw-r--r--)
- CSS/JS files: 644 (rw-r--r--)

## Server Requirements

### Minimum Requirements
- WordPress 5.0+
- PHP 7.4+
- MySQL 5.6+ or MariaDB 10.1+
- 64MB PHP memory limit
- cURL extension enabled

### Recommended Requirements
- WordPress 6.0+
- PHP 8.0+
- MySQL 8.0+ or MariaDB 10.4+
- 128MB+ PHP memory limit
- SSL certificate
- Fast hosting with SSD storage

## Security Notes

### API Keys
- Never share your AI provider API keys
- Store them securely in your WordPress database
- Monitor API usage to prevent unexpected charges
- Rotate keys periodically for security

### Permissions
- Only give admin access to trusted users
- The plugin requires `manage_options` capability
- Settings are restricted to administrators only

## Getting Help

### Common Issues
1. **"Plugin couldn't be activated"**: Check WordPress and PHP versions
2. **"Settings not saving"**: Try the Settings Test page
3. **"Scan failed"**: Check internet connection and SSL certificate
4. **"AI not working"**: Verify API key and provider settings

### Debug Mode
To enable debug mode:
1. Add this to your wp-config.php: `define('WP_DEBUG', true);`
2. Check error logs for detailed information
3. Disable debug mode after troubleshooting

### Support Resources
- Check the README.md file for detailed documentation
- Test basic functionality with test-plugin.php
- Use the Settings Test page for simplified configuration

## Next Steps

After successful installation:
1. Run a comprehensive SEO scan
2. Review and fix critical issues first
3. Set up automated scanning if desired
4. Configure AI features for enhanced optimization
5. Monitor your SEO health score regularly

## Uninstallation

To remove the plugin:
1. Deactivate the plugin in WordPress Admin > Plugins
2. Delete the plugin files
3. Note: Plugin settings will remain in your database unless manually removed
