<?php
/**
 * Scanner template for WP Health & SEO Sentinel Pro
 */

// Get stats
$health_score = $this->get_health_score();
$issue_counts = $this->get_issue_counts();
?>

<div class="wrap">
    <div class="wp-hss-container">
        <div class="wp-hss-header">
            <div class="wp-hss-header-content">
                <div class="wp-hss-logo">
                    <span class="dashicons dashicons-search" style="font-size: 32px; color: #2271b1;"></span>
                    <h1><?php _e('SEO Scanner', 'wp-hss-pro'); ?></h1>
                </div>
                <p><?php _e('Scan your site for SEO issues and get actionable recommendations', 'wp-hss-pro'); ?></p>
            </div>
        </div>

        <!-- Scanner Options -->
        <div class="wp-hss-card">
            <h2><i class="dashicons dashicons-admin-settings"></i> <?php _e('Scanner Options', 'wp-hss-pro'); ?></h2>
            <div class="wp-hss-card-content">
                <div style="display: flex; gap: 20px; flex-wrap: wrap; align-items: center;">
                    <div>
                        <label for="wp-hss-scan-type"><?php _e('Scan Type:', 'wp-hss-pro'); ?></label>
                        <select id="wp-hss-scan-type" style="margin-left: 10px;">
                            <option value="full"><?php _e('Full Scan', 'wp-hss-pro'); ?></option>
                            <option value="quick"><?php _e('Quick Scan', 'wp-hss-pro'); ?></option>
                            <option value="content"><?php _e('Content Only', 'wp-hss-pro'); ?></option>
                            <option value="technical"><?php _e('Technical Only', 'wp-hss-pro'); ?></option>
                        </select>
                    </div>
                    
                    <button id="wp-hss-scan-button" class="wp-hss-button wp-hss-button-primary">
                        <i class="dashicons dashicons-search"></i> <?php _e('Start Scan', 'wp-hss-pro'); ?>
                    </button>
                </div>
                
                <div style="margin-top: 20px;">
                    <h4><?php _e('What We Check', 'wp-hss-pro'); ?></h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 10px;">
                        <div>
                            <p><strong><?php _e('Technical SEO', 'wp-hss-pro'); ?></strong></p>
                            <ul>
                                <li><?php _e('SSL Configuration', 'wp-hss-pro'); ?></li>
                                <li><?php _e('XML Sitemap', 'wp-hss-pro'); ?></li>
                                <li><?php _e('Robots.txt', 'wp-hss-pro'); ?></li>
                                <li><?php _e('Mobile Friendliness', 'wp-hss-pro'); ?></li>
                            </ul>
                        </div>
                        
                        <div>
                            <p><strong><?php _e('On-Page SEO', 'wp-hss-pro'); ?></strong></p>
                            <ul>
                                <li><?php _e('Title Tags', 'wp-hss-pro'); ?></li>
                                <li><?php _e('Meta Descriptions', 'wp-hss-pro'); ?></li>
                                <li><?php _e('Heading Structure', 'wp-hss-pro'); ?></li>
                                <li><?php _e('Image Alt Text', 'wp-hss-pro'); ?></li>
                            </ul>
                        </div>
                        
                        <div>
                            <p><strong><?php _e('Content Quality', 'wp-hss-pro'); ?></strong></p>
                            <ul>
                                <li><?php _e('Content Length', 'wp-hss-pro'); ?></li>
                                <li><?php _e('Keyword Usage', 'wp-hss-pro'); ?></li>
                                <li><?php _e('Internal Linking', 'wp-hss-pro'); ?></li>
                                <li><?php _e('Readability', 'wp-hss-pro'); ?></li>
                            </ul>
                        </div>
                        
                        <div>
                            <p><strong><?php _e('Advanced SEO', 'wp-hss-pro'); ?></strong></p>
                            <ul>
                                <li><?php _e('Schema Markup', 'wp-hss-pro'); ?></li>
                                <li><?php _e('Social Media Integration', 'wp-hss-pro'); ?></li>
                                <li><?php _e('Performance Factors', 'wp-hss-pro'); ?></li>
                                <li><?php _e('Security Issues', 'wp-hss-pro'); ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Stats -->
        <div class="wp-hss-card">
            <h2><i class="dashicons dashicons-chart-area"></i> <?php _e('Current SEO Health', 'wp-hss-pro'); ?></h2>
            <div class="wp-hss-card-content">
                <div class="wp-hss-dashboard-grid">
                    <!-- Health Score -->
                    <div class="wp-hss-stat-card">
                        <div class="wp-hss-stat-header">
                            <h3 class="wp-hss-stat-title"><?php _e('SEO Health Score', 'wp-hss-pro'); ?></h3>
                            <div class="wp-hss-stat-icon <?php echo $health_score >= 80 ? 'good' : ($health_score >= 50 ? 'warning' : 'critical'); ?>">
                                <i class="dashicons dashicons-chart-line"></i>
                            </div>
                        </div>
                        <p id="wp-hss-health-score" class="wp-hss-stat-value" style="color: <?php echo $health_score >= 80 ? 'var(--wp-hss-success)' : ($health_score >= 50 ? 'var(--wp-hss-warning)' : 'var(--wp-hss-danger)'); ?>">
                            <?php echo $health_score; ?>
                        </p>
                        <p class="wp-hss-stat-description">
                            <?php
                            if ($health_score >= 80) {
                                _e('Your site is performing well!', 'wp-hss-pro');
                            } elseif ($health_score >= 50) {
                                _e('Your site needs some improvements.', 'wp-hss-pro');
                            } else {
                                _e('Your site needs significant SEO work.', 'wp-hss-pro');
                            }
                            ?>
                        </p>
                    </div>

                    <!-- Critical Issues -->
                    <div class="wp-hss-stat-card">
                        <div class="wp-hss-stat-header">
                            <h3 class="wp-hss-stat-title"><?php _e('Critical Issues', 'wp-hss-pro'); ?></h3>
                            <div class="wp-hss-stat-icon critical">
                                <i class="dashicons dashicons-warning"></i>
                            </div>
                        </div>
                        <p id="wp-hss-stat-critical" class="wp-hss-stat-value" style="color: var(--wp-hss-danger);"><?php echo $issue_counts['critical']; ?></p>
                        <p class="wp-hss-stat-description">
                            <?php _e('Issues that need immediate attention', 'wp-hss-pro'); ?>
                        </p>
                    </div>

                    <!-- Warnings -->
                    <div class="wp-hss-stat-card">
                        <div class="wp-hss-stat-header">
                            <h3 class="wp-hss-stat-title"><?php _e('Warnings', 'wp-hss-pro'); ?></h3>
                            <div class="wp-hss-stat-icon warning">
                                <i class="dashicons dashicons-flag"></i>
                            </div>
                        </div>
                        <p id="wp-hss-stat-warning" class="wp-hss-stat-value" style="color: var(--wp-hss-warning);"><?php echo $issue_counts['warning']; ?></p>
                        <p class="wp-hss-stat-description">
                            <?php _e('Issues that should be addressed', 'wp-hss-pro'); ?>
                        </p>
                    </div>

                    <!-- Info/Suggestions -->
                    <div class="wp-hss-stat-card">
                        <div class="wp-hss-stat-header">
                            <h3 class="wp-hss-stat-title"><?php _e('Suggestions', 'wp-hss-pro'); ?></h3>
                            <div class="wp-hss-stat-icon info">
                                <i class="dashicons dashicons-info"></i>
                            </div>
                        </div>
                        <p id="wp-hss-stat-info" class="wp-hss-stat-value" style="color: var(--wp-hss-info);"><?php echo $issue_counts['info']; ?></p>
                        <p class="wp-hss-stat-description">
                            <?php _e('Opportunities for improvement', 'wp-hss-pro'); ?>
                        </p>
                    </div>
                    
                    <!-- Total Issues -->
                    <div class="wp-hss-stat-card">
                        <div class="wp-hss-stat-header">
                            <h3 class="wp-hss-stat-title"><?php _e('Total Issues', 'wp-hss-pro'); ?></h3>
                            <div class="wp-hss-stat-icon <?php echo $issue_counts['total'] > 0 ? 'warning' : 'good'; ?>">
                                <i class="dashicons dashicons-list-view"></i>
                            </div>
                        </div>
                        <p id="wp-hss-stat-total" class="wp-hss-stat-value" style="color: <?php echo $issue_counts['total'] > 0 ? 'var(--wp-hss-warning)' : 'var(--wp-hss-success)'; ?>">
                            <?php echo $issue_counts['total']; ?>
                        </p>
                        <p class="wp-hss-stat-description">
                            <?php _e('Total issues found on your site', 'wp-hss-pro'); ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scan Results -->
        <div id="wp-hss-scan-results">
            <?php if ($issue_counts['total'] === 0) : ?>
                <div class="wp-hss-info-box">
                    <h3><i class="dashicons dashicons-info"></i> <?php _e('No Scan Results', 'wp-hss-pro'); ?></h3>
                    <p><?php _e('Run a scan to see results here.', 'wp-hss-pro'); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
