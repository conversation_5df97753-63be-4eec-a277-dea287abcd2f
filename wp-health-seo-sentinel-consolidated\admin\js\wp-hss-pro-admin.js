/**
 * Admin JavaScript for WP Health & SEO Sentinel Pro
 */
jQuery(document).ready(function($) {
    // Tab navigation
    $('.wp-hss-tab').on('click', function() {
        var tabId = $(this).data('tab');
        
        // Update active tab
        $('.wp-hss-tab').removeClass('active');
        $(this).addClass('active');
        
        // Show active tab content
        $('.wp-hss-tab-content').removeClass('active');
        $('#' + tabId).addClass('active');
    });
    
    // Handle scan button click
    $('#wp-hss-scan-button').on('click', function() {
        var $button = $(this);
        var $results = $('#wp-hss-scan-results');
        var scanType = $('#wp-hss-scan-type').val() || 'full';
        
        // Disable button and show loading state
        $button.prop('disabled', true).html('<span class="wp-hss-spinner"></span> ' + wp_hss_pro.strings.scanning);
        
        // Show loading indicator
        $results.html('<div class="wp-hss-loading"><span class="wp-hss-spinner"></span> ' + wp_hss_pro.strings.scanning + '</div>');
        
        // Send AJAX request
        $.ajax({
            url: wp_hss_pro.ajax_url,
            type: 'POST',
            data: {
                action: 'wp_hss_pro_scan_site',
                nonce: wp_hss_pro.nonce,
                scan_type: scanType
            },
            success: function(response) {
                if (response.success) {
                    // Update stats
                    updateStats(response.data.counts, response.data.health_score);
                    
                    // Generate results HTML
                    var html = generateResultsHtml(response.data.results);
                    
                    // Show results
                    $results.html(html);
                    
                    // Initialize fix buttons
                    initFixButtons();
                } else {
                    // Show error
                    $results.html('<div class="wp-hss-error-box"><h3><i class="dashicons dashicons-warning"></i> ' + wp_hss_pro.strings.scan_error + '</h3><p>' + response.data.message + '</p></div>');
                }
                
                // Re-enable button
                $button.prop('disabled', false).html('<i class="dashicons dashicons-search"></i> ' + wp_hss_pro.strings.scan_complete);
                
                // Scroll to results
                $('html, body').animate({
                    scrollTop: $results.offset().top - 50
                }, 500);
            },
            error: function() {
                // Show error
                $results.html('<div class="wp-hss-error-box"><h3><i class="dashicons dashicons-warning"></i> ' + wp_hss_pro.strings.scan_error + '</h3><p>An error occurred while scanning. Please try again.</p></div>');
                
                // Re-enable button
                $button.prop('disabled', false).html('<i class="dashicons dashicons-search"></i> Start Scan');
            }
        });
    });
    
    // Generate results HTML
    function generateResultsHtml(results) {
        var html = '';
        
        // Add summary
        var totalIssues = results.critical.length + results.warning.length + results.info.length;
        
        if (totalIssues === 0) {
            html += '<div class="wp-hss-success-box">';
            html += '<h3><i class="dashicons dashicons-yes-alt"></i> No Issues Found</h3>';
            html += '<p>Great job! Your site has no SEO issues.</p>';
            html += '</div>';
            return html;
        }
        
        html += '<div class="wp-hss-tabs">';
        html += '<div class="wp-hss-tab active" data-tab="wp-hss-all-issues"><i class="dashicons dashicons-list-view"></i> All Issues (' + totalIssues + ')</div>';
        
        if (results.critical.length > 0) {
            html += '<div class="wp-hss-tab" data-tab="wp-hss-critical-issues"><i class="dashicons dashicons-warning"></i> Critical (' + results.critical.length + ')</div>';
        }
        
        if (results.warning.length > 0) {
            html += '<div class="wp-hss-tab" data-tab="wp-hss-warning-issues"><i class="dashicons dashicons-flag"></i> Warnings (' + results.warning.length + ')</div>';
        }
        
        if (results.info.length > 0) {
            html += '<div class="wp-hss-tab" data-tab="wp-hss-info-issues"><i class="dashicons dashicons-info"></i> Suggestions (' + results.info.length + ')</div>';
        }
        
        html += '</div>';
        
        // Add fix all button
        html += '<div class="wp-hss-fix-all-container" style="text-align: right; margin-bottom: 20px;">';
        html += '<button id="wp-hss-fix-all-button" class="wp-hss-button wp-hss-button-primary"><i class="dashicons dashicons-admin-tools"></i> ' + wp_hss_pro.strings.fix_all + '</button>';
        html += '</div>';
        
        // Add tab content
        html += '<div id="wp-hss-all-issues" class="wp-hss-tab-content active">';
        html += generateIssuesListHtml(results.critical, 'critical');
        html += generateIssuesListHtml(results.warning, 'warning');
        html += generateIssuesListHtml(results.info, 'info');
        html += '</div>';
        
        if (results.critical.length > 0) {
            html += '<div id="wp-hss-critical-issues" class="wp-hss-tab-content">';
            html += generateIssuesListHtml(results.critical, 'critical');
            html += '</div>';
        }
        
        if (results.warning.length > 0) {
            html += '<div id="wp-hss-warning-issues" class="wp-hss-tab-content">';
            html += generateIssuesListHtml(results.warning, 'warning');
            html += '</div>';
        }
        
        if (results.info.length > 0) {
            html += '<div id="wp-hss-info-issues" class="wp-hss-tab-content">';
            html += generateIssuesListHtml(results.info, 'info');
            html += '</div>';
        }
        
        return html;
    }
    
    // Generate issues list HTML
    function generateIssuesListHtml(issues, severity) {
        if (issues.length === 0) {
            return '';
        }
        
        var html = '<div class="wp-hss-issues-list">';
        
        // Add heading
        var headingText = '';
        var headingIcon = '';
        
        switch (severity) {
            case 'critical':
                headingText = 'Critical Issues';
                headingIcon = 'warning';
                break;
            case 'warning':
                headingText = 'Warnings';
                headingIcon = 'flag';
                break;
            case 'info':
                headingText = 'Suggestions';
                headingIcon = 'info';
                break;
        }
        
        html += '<h3><i class="dashicons dashicons-' + headingIcon + '"></i> ' + headingText + '</h3>';
        
        // Add issues
        for (var i = 0; i < issues.length; i++) {
            var issue = issues[i];
            
            html += '<div class="wp-hss-issue-item" data-issue-id="' + issue.id + '" data-issue-type="' + issue.type + '" data-post-id="' + (issue.post_id || 0) + '">';
            html += '<div class="wp-hss-issue-header">';
            html += '<h4 class="wp-hss-issue-title"><span class="wp-hss-issue-severity ' + severity + '">' + severity.charAt(0).toUpperCase() + severity.slice(1) + '</span> ' + issue.title + '</h4>';
            html += '</div>';
            html += '<div class="wp-hss-issue-content">';
            html += '<p>' + issue.description + '</p>';
            
            if (issue.details) {
                html += '<p><strong>Details:</strong> ' + issue.details + '</p>';
            }
            
            html += '</div>';
            
            html += '<div class="wp-hss-issue-actions">';
            
            if (issue.fixable) {
                html += '<button class="wp-hss-button wp-hss-button-primary wp-hss-fix-button" data-issue-id="' + issue.id + '" data-issue-type="' + issue.type + '" data-post-id="' + (issue.post_id || 0) + '"><i class="dashicons dashicons-admin-tools"></i> Fix</button>';
            } else {
                html += '<button class="wp-hss-button wp-hss-button-outline" disabled><i class="dashicons dashicons-info"></i> Manual Fix Required</button>';
            }
            
            html += '</div>';
            html += '</div>';
        }
        
        html += '</div>';
        
        return html;
    }
    
    // Initialize fix buttons
    function initFixButtons() {
        // Handle fix button click
        $('.wp-hss-fix-button').on('click', function() {
            var $button = $(this);
            var issueId = $button.data('issue-id');
            var issueType = $button.data('issue-type');
            var postId = $button.data('post-id') || 0;
            
            // Disable button and show loading state
            $button.prop('disabled', true).html('<span class="wp-hss-spinner"></span> ' + wp_hss_pro.strings.fixing);
            
            // Send AJAX request
            $.ajax({
                url: wp_hss_pro.ajax_url,
                type: 'POST',
                data: {
                    action: 'wp_hss_pro_fix_issue',
                    nonce: wp_hss_pro.nonce,
                    issue_id: issueId,
                    issue_type: issueType,
                    post_id: postId
                },
                success: function(response) {
                    if (response.success) {
                        // Show success state
                        $button.html('<i class="dashicons dashicons-yes"></i> ' + wp_hss_pro.strings.fix_complete);
                        
                        // Add success message
                        var $message = $('<div class="wp-hss-success-box" style="margin-top: 10px;"><p><i class="dashicons dashicons-yes"></i> ' + response.data.message + '</p></div>');
                        
                        if (response.data.details) {
                            $message.append('<p>' + response.data.details + '</p>');
                        }
                        
                        $button.closest('.wp-hss-issue-actions').after($message);
                        
                        // Update issue count
                        updateIssueCount($button.closest('.wp-hss-issue-item').find('.wp-hss-issue-severity').text().toLowerCase());
                    } else {
                        // Show error state
                        $button.html('<i class="dashicons dashicons-no"></i> ' + wp_hss_pro.strings.fix_error);
                        
                        // Add error message
                        var $message = $('<div class="wp-hss-error-box" style="margin-top: 10px;"><p><i class="dashicons dashicons-warning"></i> ' + response.data.message + '</p></div>');
                        $button.closest('.wp-hss-issue-actions').after($message);
                        
                        // Reset button after delay
                        setTimeout(function() {
                            $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> Fix');
                        }, 3000);
                    }
                },
                error: function() {
                    // Show error state
                    $button.html('<i class="dashicons dashicons-no"></i> ' + wp_hss_pro.strings.fix_error);
                    
                    // Add error message
                    var $message = $('<div class="wp-hss-error-box" style="margin-top: 10px;"><p><i class="dashicons dashicons-warning"></i> An error occurred while fixing the issue. Please try again.</p></div>');
                    $button.closest('.wp-hss-issue-actions').after($message);
                    
                    // Reset button after delay
                    setTimeout(function() {
                        $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> Fix');
                    }, 3000);
                }
            });
        });
        
        // Handle fix all button
        $('#wp-hss-fix-all-button').on('click', function() {
            var $button = $(this);
            var $fixButtons = $('.wp-hss-fix-button:not(:disabled)');
            
            // If no issues to fix, do nothing
            if ($fixButtons.length === 0) {
                return;
            }
            
            // Confirm fix all
            if (!confirm(wp_hss_pro.strings.confirm_fix_all)) {
                return;
            }
            
            // Disable button and show loading state
            $button.prop('disabled', true).html('<span class="wp-hss-spinner"></span> ' + wp_hss_pro.strings.fixing_all);
            
            // Disable all fix buttons
            $fixButtons.prop('disabled', true);
            
            // Collect issues to fix
            var issues = [];
            
            $fixButtons.each(function() {
                var $btn = $(this);
                issues.push({
                    id: $btn.data('issue-id'),
                    type: $btn.data('issue-type'),
                    post_id: $btn.data('post-id') || 0
                });
            });
            
            // Send AJAX request
            $.ajax({
                url: wp_hss_pro.ajax_url,
                type: 'POST',
                data: {
                    action: 'wp_hss_pro_fix_all_issues',
                    nonce: wp_hss_pro.nonce,
                    issues: issues
                },
                success: function(response) {
                    if (response.success) {
                        // Update all fix buttons to show fixed state
                        $fixButtons.each(function() {
                            $(this).html('<i class="dashicons dashicons-yes"></i> ' + wp_hss_pro.strings.fix_complete);
                        });
                        
                        // Update fix all button
                        $button.html('<i class="dashicons dashicons-yes"></i> ' + wp_hss_pro.strings.all_fixed);
                        
                        // Add success message
                        var $message = $('<div class="wp-hss-success-box" style="margin-top: 20px;"><h3><i class="dashicons dashicons-yes"></i> All Issues Fixed</h3><p>' + response.data.message + '</p></div>');
                        $('#wp-hss-scan-results').prepend($message);
                        
                        // Update stats
                        updateStats({
                            critical: 0,
                            warning: 0,
                            info: 0,
                            total: 0
                        }, 100);
                        
                        // Scroll to message
                        $('html, body').animate({
                            scrollTop: $message.offset().top - 50
                        }, 500);
                    } else {
                        // Reset fix buttons
                        $fixButtons.prop('disabled', false);
                        
                        // Reset fix all button
                        $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + wp_hss_pro.strings.fix_all);
                        
                        // Add error message
                        var $message = $('<div class="wp-hss-error-box" style="margin-top: 20px;"><h3><i class="dashicons dashicons-warning"></i> Error</h3><p>' + response.data.message + '</p></div>');
                        $('#wp-hss-scan-results').prepend($message);
                        
                        // Scroll to message
                        $('html, body').animate({
                            scrollTop: $message.offset().top - 50
                        }, 500);
                    }
                },
                error: function() {
                    // Reset fix buttons
                    $fixButtons.prop('disabled', false);
                    
                    // Reset fix all button
                    $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + wp_hss_pro.strings.fix_all);
                    
                    // Add error message
                    var $message = $('<div class="wp-hss-error-box" style="margin-top: 20px;"><h3><i class="dashicons dashicons-warning"></i> Error</h3><p>An error occurred while fixing issues. Please try again.</p></div>');
                    $('#wp-hss-scan-results').prepend($message);
                    
                    // Scroll to message
                    $('html, body').animate({
                        scrollTop: $message.offset().top - 50
                    }, 500);
                }
            });
        });
    }
    
    // Update stats
    function updateStats(counts, healthScore) {
        $('#wp-hss-stat-critical').text(counts.critical);
        $('#wp-hss-stat-warning').text(counts.warning);
        $('#wp-hss-stat-info').text(counts.info);
        $('#wp-hss-stat-total').text(counts.total);
        
        // Update health score
        $('#wp-hss-health-score').text(healthScore);
        
        // Update health score color
        if (healthScore >= 80) {
            $('#wp-hss-health-score').css('color', 'var(--wp-hss-success)');
        } else if (healthScore >= 50) {
            $('#wp-hss-health-score').css('color', 'var(--wp-hss-warning)');
        } else {
            $('#wp-hss-health-score').css('color', 'var(--wp-hss-danger)');
        }
    }
    
    // Update issue count
    function updateIssueCount(severity) {
        // Get current count
        var count = parseInt($('#wp-hss-stat-' + severity).text());
        
        // Update count
        if (count > 0) {
            $('#wp-hss-stat-' + severity).text(count - 1);
        }
        
        // Update total count
        var total = parseInt($('#wp-hss-stat-total').text());
        if (total > 0) {
            $('#wp-hss-stat-total').text(total - 1);
        }
        
        // Update health score
        var critical = parseInt($('#wp-hss-stat-critical').text());
        var warning = parseInt($('#wp-hss-stat-warning').text());
        var info = parseInt($('#wp-hss-stat-info').text());
        var newTotal = critical + warning + info;
        
        // Calculate health score
        var healthScore = 100;
        healthScore -= critical * 10;
        healthScore -= warning * 5;
        healthScore -= info * 1;
        healthScore = Math.max(0, Math.min(100, healthScore));
        
        // Update health score
        $('#wp-hss-health-score').text(healthScore);
        
        // Update health score color
        if (healthScore >= 80) {
            $('#wp-hss-health-score').css('color', 'var(--wp-hss-success)');
        } else if (healthScore >= 50) {
            $('#wp-hss-health-score').css('color', 'var(--wp-hss-warning)');
        } else {
            $('#wp-hss-health-score').css('color', 'var(--wp-hss-danger)');
        }
    }
    
    // Initialize tooltips
    $('.wp-hss-tooltip').hover(
        function() {
            var $tooltip = $(this);
            var tooltipText = $tooltip.data('tooltip');
            
            $tooltip.append('<span class="wp-hss-tooltip-text">' + tooltipText + '</span>');
        },
        function() {
            $(this).find('.wp-hss-tooltip-text').remove();
        }
    );
    
    // Initialize on page load
    initFixButtons();
});
