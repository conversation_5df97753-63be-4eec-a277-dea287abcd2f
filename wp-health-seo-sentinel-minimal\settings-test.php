<?php
/**
 * Simple Settings Test
 */

// Test if we can save settings
if (isset($_POST['save_settings'])) {
    // Save each setting individually
    if (isset($_POST['openai_api_key'])) {
        update_option('wp_hss_openai_api_key', sanitize_text_field($_POST['openai_api_key']));
    }
    if (isset($_POST['anthropic_api_key'])) {
        update_option('wp_hss_anthropic_api_key', sanitize_text_field($_POST['anthropic_api_key']));
    }
    if (isset($_POST['enable_openai'])) {
        update_option('wp_hss_enable_openai', 1);
    } else {
        update_option('wp_hss_enable_openai', 0);
    }
    
    echo '<div style="background: #4CAF50; color: white; padding: 10px; margin: 10px 0;">Settings saved successfully!</div>';
}

// Get current values
$openai_key = get_option('wp_hss_openai_api_key', '');
$anthropic_key = get_option('wp_hss_anthropic_api_key', '');
$enable_openai = get_option('wp_hss_enable_openai', 0);
?>

<div style="max-width: 800px; margin: 20px; padding: 20px; border: 1px solid #ccc;">
    <h2>Settings Test</h2>
    
    <form method="post" action="">
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd; width: 200px;"><strong>Enable OpenAI</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <input type="checkbox" name="enable_openai" value="1" <?php checked($enable_openai, 1); ?>>
                </td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>OpenAI API Key</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <input type="text" name="openai_api_key" value="<?php echo esc_attr($openai_key); ?>" style="width: 100%; padding: 5px;">
                </td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>Anthropic API Key</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <input type="text" name="anthropic_api_key" value="<?php echo esc_attr($anthropic_key); ?>" style="width: 100%; padding: 5px;">
                </td>
            </tr>
        </table>
        
        <p style="margin-top: 20px;">
            <input type="submit" name="save_settings" value="Save Settings" style="background: #0073aa; color: white; padding: 10px 20px; border: none; cursor: pointer;">
        </p>
    </form>
    
    <h3>Current Values:</h3>
    <ul>
        <li>Enable OpenAI: <?php echo $enable_openai ? 'Yes' : 'No'; ?></li>
        <li>OpenAI Key: <?php echo $openai_key ? '***' . substr($openai_key, -4) : 'Not set'; ?></li>
        <li>Anthropic Key: <?php echo $anthropic_key ? '***' . substr($anthropic_key, -4) : 'Not set'; ?></li>
    </ul>
</div>
