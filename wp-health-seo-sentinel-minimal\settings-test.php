<?php
/**
 * Simple Settings Test - FIXED VERSION
 */

// Security check
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to access this page.');
}

// Test if we can save settings
if (isset($_POST['save_settings']) && wp_verify_nonce($_POST['settings_nonce'], 'wp_hss_settings')) {

    $saved_count = 0;

    // Save Gemini API key
    if (isset($_POST['gemini_api_key'])) {
        $result = update_option('wp_hss_gemini_api_key', sanitize_text_field($_POST['gemini_api_key']));
        if ($result) $saved_count++;
    }

    // Save OpenAI API key
    if (isset($_POST['openai_api_key'])) {
        $result = update_option('wp_hss_openai_api_key', sanitize_text_field($_POST['openai_api_key']));
        if ($result) $saved_count++;
    }

    // Save Anthropic API key
    if (isset($_POST['anthropic_api_key'])) {
        $result = update_option('wp_hss_anthropic_api_key', sanitize_text_field($_POST['anthropic_api_key']));
        if ($result) $saved_count++;
    }

    // Save checkboxes
    update_option('wp_hss_enable_gemini', isset($_POST['enable_gemini']) ? 1 : 0);
    update_option('wp_hss_enable_openai', isset($_POST['enable_openai']) ? 1 : 0);
    update_option('wp_hss_enable_anthropic', isset($_POST['enable_anthropic']) ? 1 : 0);

    echo '<div style="background: #4CAF50; color: white; padding: 15px; margin: 20px 0; border-radius: 5px; font-weight: bold;">
        ✅ Settings saved successfully! (' . $saved_count . ' API keys updated)
    </div>';
}

// Get current values
$gemini_key = get_option('wp_hss_gemini_api_key', '');
$openai_key = get_option('wp_hss_openai_api_key', '');
$anthropic_key = get_option('wp_hss_anthropic_api_key', '');
$enable_gemini = get_option('wp_hss_enable_gemini', 0);
$enable_openai = get_option('wp_hss_enable_openai', 0);
$enable_anthropic = get_option('wp_hss_enable_anthropic', 0);
?>

<div style="max-width: 800px; margin: 20px; padding: 20px; border: 1px solid #ccc;">
    <h2>Settings Test</h2>

    <form method="post" action="">
        <?php wp_nonce_field('wp_hss_settings', 'settings_nonce'); ?>

        <table style="width: 100%; border-collapse: collapse;">
            <tr style="background: #f0f8ff;">
                <td style="padding: 15px; border: 2px solid #007cba; width: 200px;"><strong>🔥 Enable Google Gemini</strong></td>
                <td style="padding: 15px; border: 2px solid #007cba;">
                    <input type="checkbox" name="enable_gemini" value="1" <?php checked($enable_gemini, 1); ?>>
                    <strong>Recommended for best results!</strong>
                </td>
            </tr>
            <tr style="background: #f0f8ff;">
                <td style="padding: 15px; border: 2px solid #007cba;"><strong>🔑 Google Gemini API Key</strong></td>
                <td style="padding: 15px; border: 2px solid #007cba;">
                    <input type="text" name="gemini_api_key" value="<?php echo esc_attr($gemini_key); ?>"
                           style="width: 100%; padding: 8px; border: 1px solid #007cba;"
                           placeholder="Enter your Google Gemini API key here">
                    <br><small>Get your key from: <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></small>
                </td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd; width: 200px;"><strong>Enable OpenAI</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <input type="checkbox" name="enable_openai" value="1" <?php checked($enable_openai, 1); ?>>
                </td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>OpenAI API Key</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <input type="text" name="openai_api_key" value="<?php echo esc_attr($openai_key); ?>" style="width: 100%; padding: 5px;">
                </td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>Enable Anthropic</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <input type="checkbox" name="enable_anthropic" value="1" <?php checked($enable_anthropic, 1); ?>>
                </td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;"><strong>Anthropic API Key</strong></td>
                <td style="padding: 10px; border: 1px solid #ddd;">
                    <input type="text" name="anthropic_api_key" value="<?php echo esc_attr($anthropic_key); ?>" style="width: 100%; padding: 5px;">
                </td>
            </tr>
        </table>

        <p style="margin-top: 20px;">
            <input type="submit" name="save_settings" value="Save Settings" style="background: #0073aa; color: white; padding: 10px 20px; border: none; cursor: pointer;">
        </p>
    </form>

    <h3>Current Values:</h3>
    <ul style="background: #f9f9f9; padding: 15px; border-left: 4px solid #007cba;">
        <li><strong>🔥 Google Gemini Enabled:</strong> <?php echo $enable_gemini ? '✅ YES' : '❌ NO'; ?></li>
        <li><strong>🔑 Gemini API Key:</strong> <?php echo $gemini_key ? '✅ Set (***' . substr($gemini_key, -4) . ')' : '❌ Not set'; ?></li>
        <li><strong>OpenAI Enabled:</strong> <?php echo $enable_openai ? '✅ YES' : '❌ NO'; ?></li>
        <li><strong>OpenAI Key:</strong> <?php echo $openai_key ? '✅ Set (***' . substr($openai_key, -4) . ')' : '❌ Not set'; ?></li>
        <li><strong>Anthropic Enabled:</strong> <?php echo $enable_anthropic ? '✅ YES' : '❌ NO'; ?></li>
        <li><strong>Anthropic Key:</strong> <?php echo $anthropic_key ? '✅ Set (***' . substr($anthropic_key, -4) . ')' : '❌ Not set'; ?></li>
    </ul>

    <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;">
        <h4>🚀 Quick Setup for Google Gemini:</h4>
        <ol>
            <li>Go to <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></li>
            <li>Create a new API key</li>
            <li>Copy the key and paste it above</li>
            <li>Check "Enable Google Gemini"</li>
            <li>Click "Save Settings"</li>
        </ol>
    </div>
</div>
