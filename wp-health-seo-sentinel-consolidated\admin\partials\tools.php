<?php
/**
 * Tools template for WP Health & SEO Sentinel
 */
?>

<div class="wrap">
    <div class="wp-hss-container">
        <div class="wp-hss-header">
            <div class="wp-hss-header-content">
                <div class="wp-hss-logo">
                    <span class="dashicons dashicons-admin-tools" style="font-size: 32px; color: #2271b1;"></span>
                    <h1><?php _e('SEO Tools', 'wp-hss'); ?></h1>
                </div>
                <p><?php _e('Advanced tools for optimizing your website SEO', 'wp-hss'); ?></p>
            </div>
        </div>

        <!-- Tools Grid -->
        <div class="wp-hss-dashboard-grid">
            <!-- Bulk Title Optimizer -->
            <div class="wp-hss-card">
                <h2><i class="dashicons dashicons-editor-textcolor"></i> <?php _e('Bulk Title Optimizer', 'wp-hss'); ?></h2>
                <div class="wp-hss-card-content">
                    <p><?php _e('Optimize all your page titles at once using AI-powered suggestions.', 'wp-hss'); ?></p>
                    <p><?php _e('This tool will analyze your content and generate SEO-friendly titles that are the perfect length and include relevant keywords.', 'wp-hss'); ?></p>
                </div>
                <div class="wp-hss-card-footer">
                    <button class="wp-hss-button wp-hss-button-primary wp-hss-tool-button" data-tool="title-optimizer">
                        <?php _e('Open Tool', 'wp-hss'); ?>
                    </button>
                </div>
            </div>

            <!-- Meta Description Generator -->
            <div class="wp-hss-card">
                <h2><i class="dashicons dashicons-editor-paragraph"></i> <?php _e('Meta Description Generator', 'wp-hss'); ?></h2>
                <div class="wp-hss-card-content">
                    <p><?php _e('Generate optimized meta descriptions for your content using AI.', 'wp-hss'); ?></p>
                    <p><?php _e('Create compelling meta descriptions that encourage clicks while staying within the recommended length.', 'wp-hss'); ?></p>
                </div>
                <div class="wp-hss-card-footer">
                    <button class="wp-hss-button wp-hss-button-primary wp-hss-tool-button" data-tool="meta-generator">
                        <?php _e('Open Tool', 'wp-hss'); ?>
                    </button>
                </div>
            </div>

            <!-- Image Alt Text Generator -->
            <div class="wp-hss-card">
                <h2><i class="dashicons dashicons-format-image"></i> <?php _e('Image Alt Text Generator', 'wp-hss'); ?></h2>
                <div class="wp-hss-card-content">
                    <p><?php _e('Generate SEO-friendly alt text for all your images automatically.', 'wp-hss'); ?></p>
                    <p><?php _e('Improve accessibility and SEO by adding descriptive alt text to images that lack it.', 'wp-hss'); ?></p>
                </div>
                <div class="wp-hss-card-footer">
                    <button class="wp-hss-button wp-hss-button-primary wp-hss-tool-button" data-tool="alt-text-generator">
                        <?php _e('Open Tool', 'wp-hss'); ?>
                    </button>
                </div>
            </div>

            <!-- Schema Markup Generator -->
            <div class="wp-hss-card">
                <h2><i class="dashicons dashicons-editor-code"></i> <?php _e('Schema Markup Generator', 'wp-hss'); ?></h2>
                <div class="wp-hss-card-content">
                    <p><?php _e('Create and validate schema markup for your pages to improve rich snippets.', 'wp-hss'); ?></p>
                    <p><?php _e('Generate structured data for various content types to help search engines better understand your content.', 'wp-hss'); ?></p>
                </div>
                <div class="wp-hss-card-footer">
                    <button class="wp-hss-button wp-hss-button-primary wp-hss-tool-button" data-tool="schema-generator">
                        <?php _e('Open Tool', 'wp-hss'); ?>
                    </button>
                </div>
            </div>

            <!-- Content Optimizer -->
            <div class="wp-hss-card">
                <h2><i class="dashicons dashicons-edit"></i> <?php _e('Content Optimizer', 'wp-hss'); ?></h2>
                <div class="wp-hss-card-content">
                    <p><?php _e('Analyze and optimize your content for better SEO performance.', 'wp-hss'); ?></p>
                    <p><?php _e('Get suggestions for improving readability, keyword usage, and overall content quality.', 'wp-hss'); ?></p>
                </div>
                <div class="wp-hss-card-footer">
                    <button class="wp-hss-button wp-hss-button-primary wp-hss-tool-button" data-tool="content-optimizer">
                        <?php _e('Open Tool', 'wp-hss'); ?>
                    </button>
                </div>
            </div>

            <!-- Internal Link Analyzer -->
            <div class="wp-hss-card">
                <h2><i class="dashicons dashicons-admin-links"></i> <?php _e('Internal Link Analyzer', 'wp-hss'); ?></h2>
                <div class="wp-hss-card-content">
                    <p><?php _e('Analyze your internal linking structure and get suggestions for improvement.', 'wp-hss'); ?></p>
                    <p><?php _e('Identify pages with few internal links and get recommendations for better link distribution.', 'wp-hss'); ?></p>
                </div>
                <div class="wp-hss-card-footer">
                    <button class="wp-hss-button wp-hss-button-primary wp-hss-tool-button" data-tool="link-analyzer">
                        <?php _e('Open Tool', 'wp-hss'); ?>
                    </button>
                </div>
            </div>
        </div>

        <!-- Tool Content Area -->
        <div id="wp-hss-tool-content" class="wp-hss-card" style="display: none;">
            <div class="wp-hss-card-header">
                <h2 id="wp-hss-tool-title"></h2>
                <button id="wp-hss-close-tool" class="wp-hss-button wp-hss-button-outline">
                    <i class="dashicons dashicons-no-alt"></i> <?php _e('Close', 'wp-hss'); ?>
                </button>
            </div>
            <div id="wp-hss-tool-body" class="wp-hss-card-content">
                <!-- Tool content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Handle tool button click
    $('.wp-hss-tool-button').on('click', function() {
        var tool = $(this).data('tool');
        var toolTitle = $(this).closest('.wp-hss-card').find('h2').text();
        
        // Show tool content area
        $('#wp-hss-tool-content').show();
        
        // Set tool title
        $('#wp-hss-tool-title').html('<i class="dashicons dashicons-admin-tools"></i> ' + toolTitle);
        
        // Load tool content
        $('#wp-hss-tool-body').html('<div class="wp-hss-loading"><i class="dashicons dashicons-update wp-hss-spin"></i> Loading...</div>');
        
        // Scroll to tool content
        $('html, body').animate({
            scrollTop: $('#wp-hss-tool-content').offset().top - 50
        }, 500);
        
        // Simulate loading tool content
        setTimeout(function() {
            var content = '';
            
            switch (tool) {
                case 'title-optimizer':
                    content = getTitleOptimizerContent();
                    break;
                case 'meta-generator':
                    content = getMetaGeneratorContent();
                    break;
                case 'alt-text-generator':
                    content = getAltTextGeneratorContent();
                    break;
                case 'schema-generator':
                    content = getSchemaGeneratorContent();
                    break;
                case 'content-optimizer':
                    content = getContentOptimizerContent();
                    break;
                case 'link-analyzer':
                    content = getLinkAnalyzerContent();
                    break;
                default:
                    content = '<div class="wp-hss-info-box"><h3><i class="dashicons dashicons-info"></i> Tool Coming Soon</h3><p>This tool is currently under development and will be available in a future update.</p></div>';
            }
            
            $('#wp-hss-tool-body').html(content);
        }, 1000);
    });
    
    // Handle close tool button click
    $('#wp-hss-close-tool').on('click', function() {
        $('#wp-hss-tool-content').hide();
    });
    
    // Tool content generators
    function getTitleOptimizerContent() {
        return `
            <p>Select the post types you want to optimize titles for:</p>
            <div class="wp-hss-form-row">
                <label><input type="checkbox" checked> Posts</label>
                <label><input type="checkbox" checked> Pages</label>
                <label><input type="checkbox"> Products</label>
            </div>
            <button class="wp-hss-button wp-hss-button-primary">Generate Optimized Titles</button>
            <div class="wp-hss-info-box" style="margin-top: 20px;">
                <h3><i class="dashicons dashicons-info"></i> How It Works</h3>
                <p>This tool will analyze your content and generate SEO-friendly titles using AI. The optimization process considers:</p>
                <ul>
                    <li>Keyword relevance and placement</li>
                    <li>Optimal title length (30-60 characters)</li>
                    <li>Click-through rate optimization</li>
                    <li>Brand inclusion when appropriate</li>
                </ul>
            </div>
        `;
    }
    
    function getMetaGeneratorContent() {
        return `
            <p>Select the post types you want to generate meta descriptions for:</p>
            <div class="wp-hss-form-row">
                <label><input type="checkbox" checked> Posts</label>
                <label><input type="checkbox" checked> Pages</label>
                <label><input type="checkbox"> Products</label>
            </div>
            <button class="wp-hss-button wp-hss-button-primary">Generate Meta Descriptions</button>
            <div class="wp-hss-info-box" style="margin-top: 20px;">
                <h3><i class="dashicons dashicons-info"></i> How It Works</h3>
                <p>This tool will create compelling meta descriptions that encourage clicks while staying within the recommended length (120-160 characters).</p>
            </div>
        `;
    }
    
    function getAltTextGeneratorContent() {
        return `
            <p>This tool will scan your media library for images without alt text and generate descriptive alt text using AI.</p>
            <button class="wp-hss-button wp-hss-button-primary">Scan Images</button>
            <div class="wp-hss-info-box" style="margin-top: 20px;">
                <h3><i class="dashicons dashicons-info"></i> Why Alt Text Matters</h3>
                <p>Alt text improves accessibility for users with screen readers and helps search engines understand your images, which can improve your SEO rankings.</p>
            </div>
        `;
    }
    
    function getSchemaGeneratorContent() {
        return `
            <p>Select a schema type to generate:</p>
            <div class="wp-hss-form-row">
                <select>
                    <option>Article</option>
                    <option>Product</option>
                    <option>Local Business</option>
                    <option>FAQ</option>
                    <option>How-to</option>
                    <option>Review</option>
                </select>
            </div>
            <button class="wp-hss-button wp-hss-button-primary">Generate Schema</button>
            <div class="wp-hss-info-box" style="margin-top: 20px;">
                <h3><i class="dashicons dashicons-info"></i> About Schema Markup</h3>
                <p>Schema markup helps search engines understand your content better, which can result in rich snippets in search results and potentially higher click-through rates.</p>
            </div>
        `;
    }
    
    function getContentOptimizerContent() {
        return `
            <p>Enter a URL or select a post to analyze:</p>
            <div class="wp-hss-form-row">
                <input type="text" placeholder="https://example.com/page-to-analyze" style="width: 100%;">
            </div>
            <p>Or select from recent posts:</p>
            <div class="wp-hss-form-row">
                <select>
                    <option>-- Select a post --</option>
                    <option>How to Improve Your SEO in 2023</option>
                    <option>10 Tips for Better Content Marketing</option>
                    <option>The Ultimate Guide to WordPress</option>
                </select>
            </div>
            <button class="wp-hss-button wp-hss-button-primary">Analyze Content</button>
            <div class="wp-hss-info-box" style="margin-top: 20px;">
                <h3><i class="dashicons dashicons-info"></i> Content Analysis</h3>
                <p>Our content optimizer analyzes readability, keyword usage, content structure, and more to provide actionable recommendations for improvement.</p>
            </div>
        `;
    }
    
    function getLinkAnalyzerContent() {
        return `
            <p>Analyze your internal linking structure to identify opportunities for improvement.</p>
            <button class="wp-hss-button wp-hss-button-primary">Start Analysis</button>
            <div class="wp-hss-info-box" style="margin-top: 20px;">
                <h3><i class="dashicons dashicons-info"></i> Why Internal Linking Matters</h3>
                <p>A good internal linking structure helps search engines discover and understand your content, distributes page authority throughout your site, and helps users navigate to related content.</p>
            </div>
        `;
    }
});
</script>

<style>
.wp-hss-spin {
    animation: wp-hss-spin 2s linear infinite;
}

@keyframes wp-hss-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.wp-hss-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #f0f0f1;
}

.wp-hss-card-header h2 {
    margin: 0;
    padding: 0;
    border: none;
}

.wp-hss-loading {
    text-align: center;
    padding: 30px;
    font-size: 16px;
}

.wp-hss-loading .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
    margin-right: 10px;
}
</style>
