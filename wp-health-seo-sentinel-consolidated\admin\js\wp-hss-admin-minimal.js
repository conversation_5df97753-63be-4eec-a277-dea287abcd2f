/**
 * Minimal admin JavaScript for WP Health & SEO Sentinel
 */
jQuery(document).ready(function($) {
    // Handle scan button click
    $('#wp-hss-scan-button').on('click', function() {
        var $button = $(this);
        var $results = $('#wp-hss-scan-results');
        
        // Disable button and show loading state
        $button.prop('disabled', true).text('Scanning...');
        
        // Simulate scanning process
        setTimeout(function() {
            // Show results
            $results.slideDown(300);
            
            // Re-enable button
            $button.prop('disabled', false).text('Start Scan');
            
            // Scroll to results
            $('html, body').animate({
                scrollTop: $results.offset().top - 50
            }, 500);
        }, 2000);
    });
    
    // Handle fix buttons
    $('.wp-hss-fix-button').on('click', function() {
        var $button = $(this);
        var issueId = $button.data('issue-id');
        
        // Disable button and show loading state
        $button.prop('disabled', true).html('<i class="dashicons dashicons-update-alt wp-hss-spin"></i> ' + (wp_hss_ajax.strings.fixing || 'Fixing...'));
        
        // Send AJAX request
        $.ajax({
            url: wp_hss_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'wp_hss_fix_issue',
                nonce: wp_hss_ajax.nonce,
                issue_id: issueId
            },
            success: function(response) {
                if (response.success) {
                    // Show success icon and text
                    $button.html('<i class="dashicons dashicons-yes"></i> ' + (wp_hss_ajax.strings.fix_complete || 'Fixed'));
                    
                    // Keep success state for fixed issues
                    $button.prop('disabled', true);
                } else {
                    // Show error icon and text
                    $button.html('<i class="dashicons dashicons-no"></i> ' + (wp_hss_ajax.strings.fix_failed || 'Failed'));
                    
                    // Reset button for failed fixes after a delay
                    setTimeout(function() {
                        $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + (wp_hss_ajax.strings.fix || 'Fix'));
                    }, 2000);
                }
            },
            error: function() {
                // Show error icon and text
                $button.html('<i class="dashicons dashicons-no"></i> ' + (wp_hss_ajax.strings.fix_failed || 'Failed'));
                
                // Reset button for failed fixes after a delay
                setTimeout(function() {
                    $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + (wp_hss_ajax.strings.fix || 'Fix'));
                }, 2000);
            }
        });
    });
    
    // Handle fix all button
    $('#wp-hss-fix-all-button').on('click', function() {
        var $button = $(this);
        var $fixButtons = $('.wp-hss-fix-button:not(:disabled)');
        
        // If no issues to fix, do nothing
        if ($fixButtons.length === 0) {
            return;
        }
        
        // Disable button and show loading state
        $button.prop('disabled', true).text('Fixing All Issues...');
        
        // Disable all fix buttons
        $fixButtons.prop('disabled', true);
        
        // Send AJAX request
        $.ajax({
            url: wp_hss_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'wp_hss_fix_all_issues',
                nonce: wp_hss_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Update all fix buttons to show fixed state
                    $fixButtons.each(function() {
                        $(this).html('<i class="dashicons dashicons-yes"></i> ' + (wp_hss_ajax.strings.fix_complete || 'Fixed'));
                    });
                    
                    // Update fix all button
                    $button.text('All Issues Fixed');
                } else {
                    // Reset fix buttons
                    $fixButtons.prop('disabled', false);
                    
                    // Reset fix all button
                    $button.prop('disabled', false).text('Fix All Issues');
                }
            },
            error: function() {
                // Reset fix buttons
                $fixButtons.prop('disabled', false);
                
                // Reset fix all button
                $button.prop('disabled', false).text('Fix All Issues');
            }
        });
    });
});
