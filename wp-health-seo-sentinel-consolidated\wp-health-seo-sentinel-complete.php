<?php
/**
 * Plugin Name: WP Health & SEO Sentinel (Complete)
 * Description: Comprehensive SEO scanner and fixer for WordPress with AI optimization
 * Version: 1.0.0
 * Author: SEO Experts
 */

// If this file is called directly, abort
if (!defined('WPINC')) {
    die;
}

// Define plugin constants
define('WP_HSS_VERSION', '1.0.0');
define('WP_HSS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WP_HSS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WP_HSS_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Include required files
require_once WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-scanner.php';
require_once WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-fixer.php';
require_once WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-ai.php';
require_once WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-admin.php';

/**
 * Main plugin class
 */
class WP_Health_SEO_Sentinel {
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Scanner instance
     */
    public $scanner = null;
    
    /**
     * Fixer instance
     */
    public $fixer = null;
    
    /**
     * AI instance
     */
    public $ai = null;
    
    /**
     * Admin instance
     */
    public $admin = null;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Initialize components
        $this->scanner = new WP_HSS_Scanner();
        $this->fixer = new WP_HSS_Fixer();
        $this->ai = new WP_HSS_AI();
        $this->admin = new WP_HSS_Admin();
        
        // Register activation/deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // Initialize plugin
        add_action('plugins_loaded', array($this, 'init'));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Set default options
        $default_options = array(
            'enable_scanning' => true,
            'scan_frequency' => 'weekly',
            'scan_post_types' => array('post', 'page'),
            'ai_enabled' => false,
            'ai_provider' => 'openai',
            'ai_api_key' => '',
            'title_min_length' => 30,
            'title_max_length' => 60,
            'meta_min_length' => 120,
            'meta_max_length' => 160,
            'content_min_length' => 300,
            'last_scan' => 0,
            'version' => WP_HSS_VERSION
        );
        
        // Add options if they don't exist
        if (false === get_option('wp_hss_settings')) {
            add_option('wp_hss_settings', $default_options);
        }
        
        // Create issues option
        if (false === get_option('wp_hss_issues')) {
            add_option('wp_hss_issues', array());
        }
        
        // Create scan history option
        if (false === get_option('wp_hss_scan_history')) {
            add_option('wp_hss_scan_history', array());
        }
        
        // Schedule scan if enabled
        $this->schedule_scan();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled scans
        wp_clear_scheduled_hook('wp_hss_scheduled_scan');
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain
        load_plugin_textdomain('wp-hss', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Check if we need to update
        $this->check_for_update();
        
        // Initialize components
        $this->scanner->init();
        $this->fixer->init();
        $this->ai->init();
        $this->admin->init();
        
        // Register AJAX handlers
        add_action('wp_ajax_wp_hss_start_scan', array($this->scanner, 'ajax_start_scan'));
        add_action('wp_ajax_wp_hss_fix_issue', array($this->fixer, 'ajax_fix_issue'));
        add_action('wp_ajax_wp_hss_fix_all_issues', array($this->fixer, 'ajax_fix_all_issues'));
        add_action('wp_ajax_wp_hss_get_scan_history', array($this->admin, 'ajax_get_scan_history'));
        add_action('wp_ajax_wp_hss_test_ai_connection', array($this->ai, 'ajax_test_connection'));
        
        // Register scheduled scan hook
        add_action('wp_hss_scheduled_scan', array($this->scanner, 'run_scheduled_scan'));
    }
    
    /**
     * Check if we need to update
     */
    private function check_for_update() {
        $settings = get_option('wp_hss_settings');
        
        if ($settings && isset($settings['version']) && $settings['version'] !== WP_HSS_VERSION) {
            // Update version
            $settings['version'] = WP_HSS_VERSION;
            
            // Update settings
            update_option('wp_hss_settings', $settings);
            
            // Reschedule scan
            $this->schedule_scan();
        }
    }
    
    /**
     * Schedule scan
     */
    private function schedule_scan() {
        $settings = get_option('wp_hss_settings');
        
        // Clear existing schedule
        wp_clear_scheduled_hook('wp_hss_scheduled_scan');
        
        // Schedule new scan if enabled
        if ($settings && isset($settings['enable_scanning']) && $settings['enable_scanning']) {
            $frequency = isset($settings['scan_frequency']) ? $settings['scan_frequency'] : 'weekly';
            
            switch ($frequency) {
                case 'daily':
                    $interval = DAY_IN_SECONDS;
                    break;
                case 'weekly':
                    $interval = WEEK_IN_SECONDS;
                    break;
                case 'monthly':
                    $interval = MONTH_IN_SECONDS;
                    break;
                default:
                    $interval = WEEK_IN_SECONDS;
            }
            
            wp_schedule_event(time() + $interval, $frequency, 'wp_hss_scheduled_scan');
        }
    }
}

// Initialize plugin
function wp_health_seo_sentinel() {
    return WP_Health_SEO_Sentinel::get_instance();
}

// Start the plugin
wp_health_seo_sentinel();
