<?php
/**
 * Settings template for WP Health & SEO Sentinel
 */

// Get current settings
$settings = get_option('wp_hss_settings', array());

// Set default values
$enable_scanning = isset($settings['enable_scanning']) ? $settings['enable_scanning'] : true;
$scan_frequency = isset($settings['scan_frequency']) ? $settings['scan_frequency'] : 'weekly';
$scan_post_types = isset($settings['scan_post_types']) ? $settings['scan_post_types'] : array('post', 'page');
$ai_enabled = isset($settings['ai_enabled']) ? $settings['ai_enabled'] : false;
$ai_provider = isset($settings['ai_provider']) ? $settings['ai_provider'] : 'openai';
$ai_api_key = isset($settings['ai_api_key']) ? $settings['ai_api_key'] : '';
$title_min_length = isset($settings['title_min_length']) ? $settings['title_min_length'] : 30;
$title_max_length = isset($settings['title_max_length']) ? $settings['title_max_length'] : 60;
$meta_min_length = isset($settings['meta_min_length']) ? $settings['meta_min_length'] : 120;
$meta_max_length = isset($settings['meta_max_length']) ? $settings['meta_max_length'] : 160;
$content_min_length = isset($settings['content_min_length']) ? $settings['content_min_length'] : 300;

// Get available post types
$post_types = get_post_types(array('public' => true), 'objects');
?>

<div class="wrap">
    <div class="wp-hss-container">
        <div class="wp-hss-header">
            <div class="wp-hss-header-content">
                <div class="wp-hss-logo">
                    <span class="dashicons dashicons-admin-settings" style="font-size: 32px; color: #2271b1;"></span>
                    <h1><?php _e('Settings', 'wp-hss'); ?></h1>
                </div>
                <p><?php _e('Configure WP Health & SEO Sentinel settings', 'wp-hss'); ?></p>
            </div>
        </div>

        <?php settings_errors('wp_hss_settings'); ?>

        <form method="post" action="" class="wp-hss-settings-form">
            <?php wp_nonce_field('wp_hss_settings', 'wp_hss_settings_nonce'); ?>

            <!-- General Settings -->
            <div class="wp-hss-card">
                <h2><i class="dashicons dashicons-admin-generic"></i> <?php _e('General Settings', 'wp-hss'); ?></h2>
                <div class="wp-hss-card-content">
                    <div class="wp-hss-settings-section">
                        <h3><?php _e('Scanning Options', 'wp-hss'); ?></h3>
                        
                        <div class="wp-hss-form-row">
                            <label>
                                <input type="checkbox" name="enable_scanning" value="1" <?php checked($enable_scanning); ?>>
                                <?php _e('Enable automatic scanning', 'wp-hss'); ?>
                            </label>
                            <p class="description"><?php _e('When enabled, the plugin will automatically scan your site based on the frequency below.', 'wp-hss'); ?></p>
                        </div>
                        
                        <div class="wp-hss-form-row">
                            <label for="scan_frequency"><?php _e('Scan Frequency', 'wp-hss'); ?></label>
                            <select name="scan_frequency" id="scan_frequency">
                                <option value="daily" <?php selected($scan_frequency, 'daily'); ?>><?php _e('Daily', 'wp-hss'); ?></option>
                                <option value="weekly" <?php selected($scan_frequency, 'weekly'); ?>><?php _e('Weekly', 'wp-hss'); ?></option>
                                <option value="monthly" <?php selected($scan_frequency, 'monthly'); ?>><?php _e('Monthly', 'wp-hss'); ?></option>
                            </select>
                            <p class="description"><?php _e('How often should the plugin automatically scan your site.', 'wp-hss'); ?></p>
                        </div>
                        
                        <div class="wp-hss-form-row">
                            <label><?php _e('Post Types to Scan', 'wp-hss'); ?></label>
                            <div>
                                <?php foreach ($post_types as $post_type) : ?>
                                    <label style="display: inline-block; margin-right: 20px;">
                                        <input type="checkbox" name="scan_post_types[]" value="<?php echo esc_attr($post_type->name); ?>" <?php checked(in_array($post_type->name, $scan_post_types)); ?>>
                                        <?php echo esc_html($post_type->label); ?>
                                    </label>
                                <?php endforeach; ?>
                            </div>
                            <p class="description"><?php _e('Select which post types should be included in the scan.', 'wp-hss'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- AI Settings -->
            <div class="wp-hss-card">
                <h2><i class="dashicons dashicons-admin-plugins"></i> <?php _e('AI Integration', 'wp-hss'); ?></h2>
                <div class="wp-hss-card-content">
                    <div class="wp-hss-settings-section">
                        <h3><?php _e('AI Settings', 'wp-hss'); ?></h3>
                        
                        <div class="wp-hss-form-row">
                            <label>
                                <input type="checkbox" name="ai_enabled" value="1" <?php checked($ai_enabled); ?>>
                                <?php _e('Enable AI-powered optimization', 'wp-hss'); ?>
                            </label>
                            <p class="description"><?php _e('When enabled, the plugin will use AI to optimize titles, meta descriptions, and content.', 'wp-hss'); ?></p>
                        </div>
                        
                        <div class="wp-hss-form-row">
                            <label for="ai_provider"><?php _e('AI Provider', 'wp-hss'); ?></label>
                            <select name="ai_provider" id="ai_provider">
                                <option value="openai" <?php selected($ai_provider, 'openai'); ?>><?php _e('OpenAI', 'wp-hss'); ?></option>
                                <option value="anthropic" <?php selected($ai_provider, 'anthropic'); ?>><?php _e('Anthropic', 'wp-hss'); ?></option>
                                <option value="google" <?php selected($ai_provider, 'google'); ?>><?php _e('Google AI', 'wp-hss'); ?></option>
                            </select>
                            <p class="description"><?php _e('Select which AI provider to use for optimization.', 'wp-hss'); ?></p>
                        </div>
                        
                        <div class="wp-hss-form-row">
                            <label for="ai_api_key"><?php _e('API Key', 'wp-hss'); ?></label>
                            <input type="password" name="ai_api_key" id="ai_api_key" value="<?php echo esc_attr($ai_api_key); ?>" class="regular-text">
                            <button type="button" id="wp-hss-test-ai-connection" class="button button-secondary" style="margin-left: 10px;">
                                <?php _e('Test Connection', 'wp-hss'); ?>
                            </button>
                            <p class="description"><?php _e('Enter your API key for the selected AI provider.', 'wp-hss'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- SEO Settings -->
            <div class="wp-hss-card">
                <h2><i class="dashicons dashicons-chart-area"></i> <?php _e('SEO Settings', 'wp-hss'); ?></h2>
                <div class="wp-hss-card-content">
                    <div class="wp-hss-settings-section">
                        <h3><?php _e('Title & Meta Settings', 'wp-hss'); ?></h3>
                        
                        <div class="wp-hss-form-row">
                            <label for="title_min_length"><?php _e('Minimum Title Length', 'wp-hss'); ?></label>
                            <input type="number" name="title_min_length" id="title_min_length" value="<?php echo esc_attr($title_min_length); ?>" min="10" max="100">
                            <p class="description"><?php _e('Minimum recommended length for page titles (in characters).', 'wp-hss'); ?></p>
                        </div>
                        
                        <div class="wp-hss-form-row">
                            <label for="title_max_length"><?php _e('Maximum Title Length', 'wp-hss'); ?></label>
                            <input type="number" name="title_max_length" id="title_max_length" value="<?php echo esc_attr($title_max_length); ?>" min="30" max="120">
                            <p class="description"><?php _e('Maximum recommended length for page titles (in characters).', 'wp-hss'); ?></p>
                        </div>
                        
                        <div class="wp-hss-form-row">
                            <label for="meta_min_length"><?php _e('Minimum Meta Description Length', 'wp-hss'); ?></label>
                            <input type="number" name="meta_min_length" id="meta_min_length" value="<?php echo esc_attr($meta_min_length); ?>" min="50" max="300">
                            <p class="description"><?php _e('Minimum recommended length for meta descriptions (in characters).', 'wp-hss'); ?></p>
                        </div>
                        
                        <div class="wp-hss-form-row">
                            <label for="meta_max_length"><?php _e('Maximum Meta Description Length', 'wp-hss'); ?></label>
                            <input type="number" name="meta_max_length" id="meta_max_length" value="<?php echo esc_attr($meta_max_length); ?>" min="100" max="320">
                            <p class="description"><?php _e('Maximum recommended length for meta descriptions (in characters).', 'wp-hss'); ?></p>
                        </div>
                        
                        <div class="wp-hss-form-row">
                            <label for="content_min_length"><?php _e('Minimum Content Length', 'wp-hss'); ?></label>
                            <input type="number" name="content_min_length" id="content_min_length" value="<?php echo esc_attr($content_min_length); ?>" min="100" max="1000">
                            <p class="description"><?php _e('Minimum recommended length for content (in words).', 'wp-hss'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <p class="submit">
                <input type="submit" name="wp_hss_save_settings" class="button button-primary" value="<?php _e('Save Settings', 'wp-hss'); ?>">
            </p>
        </form>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Test AI connection
    $('#wp-hss-test-ai-connection').on('click', function() {
        var $button = $(this);
        var apiKey = $('#ai_api_key').val();
        var provider = $('#ai_provider').val();
        
        if (!apiKey) {
            alert('Please enter an API key.');
            return;
        }
        
        $button.prop('disabled', true).text('Testing...');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'wp_hss_test_ai_connection',
                nonce: '<?php echo wp_create_nonce('wp_hss_nonce'); ?>',
                api_key: apiKey,
                provider: provider
            },
            success: function(response) {
                if (response.success) {
                    alert('Success: ' + response.data.message);
                } else {
                    alert('Error: ' + response.data.message);
                }
                $button.prop('disabled', false).text('Test Connection');
            },
            error: function() {
                alert('An error occurred while testing the connection. Please try again.');
                $button.prop('disabled', false).text('Test Connection');
            }
        });
    });
});
</script>
