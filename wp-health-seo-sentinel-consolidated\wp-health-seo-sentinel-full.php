<?php
/**
 * Plugin Name:       WP Health & SEO Sentinel
 * Plugin URI:        https://example.com/wp-health-seo-sentinel
 * Description:       The most comprehensive WordPress site scanner for identifying and fixing SEO and health issues.
 * Version:           1.0.0
 * Author:            SEO Experts
 * Author URI:        https://example.com
 * License:           GPL v2 or later
 * License URI:       https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:       wp-hss
 * Domain Path:       /languages
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Define plugin constants
define('WP_HSS_VERSION', '1.0.0');
define('WP_HSS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WP_HSS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WP_HSS_PLUGIN_FILE', __FILE__);

/**
 * Activation hook - CONSOLIDATED VERSION
 * This function properly handles database operations with error checking
 */
function wp_hss_activate() {
    // Set a flag to prevent database creation during activation
    if (!defined('WP_HSS_ACTIVATING')) {
        define('WP_HSS_ACTIVATING', true);
    }
    
    // Set default options with error handling
    try {
        $default_options = [
            'enable_caching' => true,
            'enable_incremental_scanning' => true,
            'enable_background_scanning' => true,
            'ai_enable_features' => false,
            'ai_api_key' => '',
            'ai_provider' => 'openai',
            'title_min_length' => 30,
            'title_max_length' => 60,
            'meta_min_length' => 120,
            'meta_max_length' => 160,
            'min_content_length' => 300,
            'scan_post_types' => ['post', 'page'],
            'issue_types_to_check' => [
                'title_length',
                'meta_description',
                'heading_tags',
                'broken_links',
                'image_alt_text',
                'image_file_name',
                'internal_links',
                'schema_markup',
                'missing_images',
                'featured_image',
                'low_quality_content',
                'page_speed',
                'mobile_friendly',
                'content_duplication',
                'sitemap_robots'
            ],
        ];
        
        // Check if option already exists before adding
        if (get_option('wp_hss_settings') === false) {
            add_option('wp_hss_settings', $default_options);
        } else {
            // Update existing settings with any new defaults
            $current_settings = get_option('wp_hss_settings');
            $updated_settings = array_merge($default_options, $current_settings);
            update_option('wp_hss_settings', $updated_settings);
        }
        
        // Schedule database creation for after plugin is fully loaded
        if (!wp_next_scheduled('wp_hss_delayed_db_setup')) {
            wp_schedule_single_event(time() + 30, 'wp_hss_delayed_db_setup');
        }
        
        // Log successful activation
        if (function_exists('error_log')) {
            error_log('WP Health SEO Sentinel: Plugin activated successfully');
        }
    } catch (Exception $e) {
        // Log activation error but don't cause fatal error
        if (function_exists('error_log')) {
            error_log('WP Health SEO Sentinel Activation Error: ' . $e->getMessage());
        }
        
        // Store activation error for admin notice
        update_option('wp_hss_activation_error', $e->getMessage());
    }
}
register_activation_hook(__FILE__, 'wp_hss_activate');

/**
 * Deactivation hook - CONSOLIDATED VERSION
 * This function properly handles cleanup with error checking
 */
function wp_hss_deactivate() {
    try {
        // Clear scheduled tasks if any
        if (function_exists('wp_clear_scheduled_hook')) {
            wp_clear_scheduled_hook('wp_hss_background_scan_cron');
            wp_clear_scheduled_hook('wp_hss_delayed_db_setup');
        }
        
        // Log successful deactivation
        if (function_exists('error_log')) {
            error_log('WP Health SEO Sentinel: Plugin deactivated successfully');
        }
    } catch (Exception $e) {
        // Log deactivation error but don't cause fatal error
        if (function_exists('error_log')) {
            error_log('WP Health SEO Sentinel Deactivation Error: ' . $e->getMessage());
        }
    }
}
register_deactivation_hook(__FILE__, 'wp_hss_deactivate');

/**
 * Handle delayed database setup
 */
function wp_hss_handle_delayed_db_setup() {
    if (class_exists('WP_HSS_DB')) {
        WP_HSS_DB::create_tables();
    }
}
add_action('wp_hss_delayed_db_setup', 'wp_hss_handle_delayed_db_setup');

/**
 * Load plugin functionality
 */
function wp_hss_init() {
    // Check for activation errors and display admin notice if any
    if (get_option('wp_hss_activation_error')) {
        add_action('admin_notices', 'wp_hss_activation_error_notice');
    }
    
    // Include required files with error handling
    try {
        // Core class
        $core_file = WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-core.php';
        if (file_exists($core_file)) {
            require_once $core_file;
        } else {
            throw new Exception("Core file not found: $core_file");
        }
        
        // Database class
        $db_file = WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-db.php';
        if (file_exists($db_file)) {
            require_once $db_file;
        } else {
            throw new Exception("Database file not found: $db_file");
        }
        
        // Admin UI class
        $admin_ui_file = WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-admin-ui.php';
        if (file_exists($admin_ui_file)) {
            require_once $admin_ui_file;
        }
        
        // Scanner class
        $scanner_file = WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-scanner.php';
        if (file_exists($scanner_file)) {
            require_once $scanner_file;
        }
        
        // Fixer class
        $fixer_file = WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-fixer.php';
        if (file_exists($fixer_file)) {
            require_once $fixer_file;
        }
        
        // Initialize the plugin
        $plugin = new WP_HSS_Core();
        $plugin->run();
        
        // Create database tables safely after WordPress is fully loaded
        add_action('admin_init', 'wp_hss_create_tables_safely');
        
    } catch (Exception $e) {
        // Log error but don't cause fatal error
        if (function_exists('error_log')) {
            error_log('WP Health SEO Sentinel Init Error: ' . $e->getMessage());
        }
        
        // Store error for admin notice
        update_option('wp_hss_init_error', $e->getMessage());
        
        // Display admin notice about error
        add_action('admin_notices', 'wp_hss_init_error_notice');
    }
}
add_action('plugins_loaded', 'wp_hss_init');

/**
 * Create database tables safely after WordPress is fully loaded
 */
function wp_hss_create_tables_safely() {
    // Only run this on admin pages
    if (!is_admin()) {
        return;
    }
    
    // Check if we need to create tables
    if (class_exists('WP_HSS_DB')) {
        WP_HSS_DB::verify_tables();
    }
}

/**
 * Display activation error notice
 */
function wp_hss_activation_error_notice() {
    $error = get_option('wp_hss_activation_error');
    if ($error) {
        ?>
        <div class="notice notice-error is-dismissible">
            <p><strong>WP Health & SEO Sentinel Activation Error:</strong> <?php echo esc_html($error); ?></p>
            <p>The plugin may not function correctly. Please contact support for assistance.</p>
        </div>
        <?php
        // Clear the error after displaying it
        delete_option('wp_hss_activation_error');
    }
}

/**
 * Display initialization error notice
 */
function wp_hss_init_error_notice() {
    $error = get_option('wp_hss_init_error');
    if ($error) {
        ?>
        <div class="notice notice-error is-dismissible">
            <p><strong>WP Health & SEO Sentinel Initialization Error:</strong> <?php echo esc_html($error); ?></p>
            <p>The plugin may not function correctly. Please contact support for assistance.</p>
        </div>
        <?php
        // Clear the error after displaying it
        delete_option('wp_hss_init_error');
    }
}
