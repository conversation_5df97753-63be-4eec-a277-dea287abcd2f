/**
 * Admin CSS for WP Health & SEO Sentinel
 */

/* Card styling */
.card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
    margin-top: 20px;
    padding: 20px;
}

.card h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* Stats Grid */
.wp-hss-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.wp-hss-stat-card {
    background-color: #f9f9f9;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
}

.wp-hss-stat-value {
    font-size: 36px;
    font-weight: bold;
    margin: 10px 0;
}

.wp-hss-stat-label {
    color: #50575e;
    font-size: 14px;
    font-weight: bold;
}

.wp-hss-stat-description {
    color: #50575e;
    font-size: 12px;
}

/* Issue Items */
.wp-hss-issue-item {
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 15px;
    background-color: #fff;
}

.wp-hss-issue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.wp-hss-issue-title {
    margin: 0;
    font-size: 16px;
}

.wp-hss-issue-severity {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
}

.wp-hss-issue-content {
    margin-bottom: 15px;
}

.wp-hss-issue-actions {
    text-align: right;
}

/* Tabs */
.wp-hss-tabs {
    display: flex;
    border-bottom: 1px solid #ccd0d4;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.wp-hss-tab {
    padding: 10px 15px;
    cursor: pointer;
    margin-right: 5px;
    border: 1px solid #ccd0d4;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    background-color: #f0f0f1;
    position: relative;
    z-index: 1;
}

.wp-hss-tab.active {
    background-color: #fff;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
    font-weight: bold;
    z-index: 2;
}

.wp-hss-tab-content {
    display: none;
    width: 100%;
}

.wp-hss-tab-content.active {
    display: block;
}

/* Subtabs */
.wp-hss-subtabs {
    margin-top: 0;
    margin-bottom: 15px;
    border-bottom: 1px solid #e2e4e7;
}

.wp-hss-subtabs .wp-hss-tab {
    padding: 8px 12px;
    font-size: 13px;
    background-color: #f8f9fa;
}

.wp-hss-subtabs .wp-hss-tab.active {
    background-color: #fff;
    border-bottom-color: #fff;
}

/* Success and error boxes */
.wp-hss-success-box {
    background-color: #f0f6e4;
    border-left: 4px solid #00a32a;
    padding: 12px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.wp-hss-success-box h3 {
    margin-top: 0;
    color: #00a32a;
}

.wp-hss-error-box {
    background-color: #fcf0f1;
    border-left: 4px solid #d63638;
    padding: 12px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.wp-hss-error-box h3 {
    margin-top: 0;
    color: #d63638;
}

/* Tools Grid */
.wp-hss-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

/* AI Provider Selector */
.wp-hss-ai-provider-selector {
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
}

.wp-hss-ai-provider-selector h3 {
    margin-top: 0;
}

.ai-provider-settings {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e2e4e7;
}

/* Scan Options Grid */
.wp-hss-scan-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.wp-hss-scan-option-group {
    background-color: #f8f9fa;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
    padding: 15px;
}

.wp-hss-scan-option-group h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
}

.wp-hss-scan-option-group label {
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
}

.wp-hss-scan-option-group .description {
    color: #666;
    font-size: 12px;
    margin-top: 5px;
}

/* Loading spinner */
.wp-hss-loading {
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    padding: 20px;
}

.wp-hss-spinner {
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 3px solid #2271b1;
    width: 24px;
    height: 24px;
    animation: wp-hss-spin 1s linear infinite;
    margin-right: 15px;
    margin-top: 3px;
}

@keyframes wp-hss-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scan Progress */
.wp-hss-scan-progress {
    margin-top: 15px;
    width: 100%;
}

#scan-progress-text {
    font-size: 13px;
    color: #666;
    margin-bottom: 5px;
}

.wp-hss-progress-bar {
    height: 10px;
    background-color: #f0f0f1;
    border-radius: 5px;
    overflow: hidden;
    width: 100%;
    max-width: 500px;
}

.wp-hss-progress-bar-inner {
    height: 100%;
    background-color: #2271b1;
    width: 0;
    transition: width 0.3s ease-in-out;
}
