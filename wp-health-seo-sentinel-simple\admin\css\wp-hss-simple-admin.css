/**
 * Admin CSS for WP Health & SEO Sentinel Simple
 */

/* Card styling */
.card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
    margin-top: 20px;
    padding: 20px;
}

.card h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

/* Button styling */
.wp-hss-button {
    display: inline-block;
    padding: 8px 16px;
    background-color: #2271b1;
    color: #fff;
    border-radius: 4px;
    text-decoration: none;
    border: none;
    cursor: pointer;
}

.wp-hss-button:hover {
    background-color: #135e96;
}

/* Status indicators */
.wp-hss-status-good {
    color: #00a32a;
    font-weight: bold;
}

.wp-hss-status-warning {
    color: #dba617;
    font-weight: bold;
}

.wp-hss-status-critical {
    color: #d63638;
    font-weight: bold;
}

/* Loading spinner */
.wp-hss-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.wp-hss-spinner {
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 3px solid #2271b1;
    width: 24px;
    height: 24px;
    animation: wp-hss-spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes wp-hss-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tabs */
.wp-hss-tabs {
    display: flex;
    border-bottom: 1px solid #ccd0d4;
    margin-bottom: 20px;
}

.wp-hss-tab {
    padding: 10px 15px;
    cursor: pointer;
    margin-right: 5px;
    border: 1px solid #ccd0d4;
    border-bottom: none;
    border-radius: 4px 4px 0 0;
    background-color: #f0f0f1;
}

.wp-hss-tab.active {
    background-color: #fff;
    border-bottom: 1px solid #fff;
    margin-bottom: -1px;
    font-weight: bold;
}

.wp-hss-tab-content {
    display: none;
}

.wp-hss-tab-content.active {
    display: block;
}

/* Issues list */
.wp-hss-issues-list {
    margin-bottom: 20px;
}

.wp-hss-issue-item {
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 15px;
    background-color: #fff;
}

.wp-hss-issue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.wp-hss-issue-title {
    margin: 0;
    font-size: 16px;
}

.wp-hss-issue-severity {
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 12px;
}

.wp-hss-issue-content {
    margin-bottom: 15px;
}

.wp-hss-issue-actions {
    text-align: right;
}

/* Success and error boxes */
.wp-hss-success-box {
    background-color: #f0f6e4;
    border-left: 4px solid #00a32a;
    padding: 12px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.wp-hss-success-box h3 {
    margin-top: 0;
    color: #00a32a;
}

.wp-hss-error-box {
    background-color: #fcf0f1;
    border-left: 4px solid #d63638;
    padding: 12px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.wp-hss-error-box h3 {
    margin-top: 0;
    color: #d63638;
}

/* Dashboard stats */
.wp-hss-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.wp-hss-stat-card {
    background-color: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
}

.wp-hss-stat-value {
    font-size: 36px;
    font-weight: bold;
    margin: 10px 0;
}

.wp-hss-stat-label {
    color: #50575e;
    font-size: 14px;
}
