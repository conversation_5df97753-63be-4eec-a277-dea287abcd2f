<?php
/**
 * Plugin Name: WP Health & SEO Sentinel
 * Plugin URI: https://example.com/wp-health-seo-sentinel
 * Description: Simple SEO analysis and optimization for WordPress
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * Text Domain: wp-hss
 * Domain Path: /languages
 * License: GPL-2.0+
 * License URI: http://www.gnu.org/licenses/gpl-2.0.txt
 */

// If this file is called directly, abort.
if (!defined('WPINC')) {
    die;
}

// Define plugin constants
define('WP_HSS_VERSION', '1.0.0');
define('WP_HSS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WP_HSS_PLUGIN_URL', plugin_dir_url(__FILE__));

// Register activation/deactivation hooks
register_activation_hook(__FILE__, 'wp_hss_activate');
register_deactivation_hook(__FILE__, 'wp_hss_deactivate');

/**
 * Plugin activation
 */
function wp_hss_activate() {
    // Flush rewrite rules
    flush_rewrite_rules();
}

/**
 * Plugin deactivation
 */
function wp_hss_deactivate() {
    // Flush rewrite rules
    flush_rewrite_rules();
}

/**
 * Main plugin class
 */
class WP_Health_SEO_Sentinel {
    /**
     * Initialize the plugin
     */
    public function __construct() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Add settings link to plugins page
        add_filter('plugin_action_links_' . plugin_basename(__FILE__), array($this, 'add_settings_link'));

        // Enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Register AJAX handlers
        add_action('wp_ajax_wp_hss_scan_site', array($this, 'ajax_scan_site'));
        add_action('wp_ajax_wp_hss_fix_issue', array($this, 'ajax_fix_issue'));

        // Register settings
        add_action('admin_init', array($this, 'register_settings'));

        // APPLY REAL LIVE FIXES ON EVERY PAGE LOAD
        add_action('init', array($this, 'apply_active_fixes'));
    }

    /**
     * AJAX handler for scanning the site
     */
    public function ajax_scan_site() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed.'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
        }

        // Get scan results
        $results = $this->scan_site();

        // Calculate health score
        $critical_count = count($results['critical']);
        $warning_count = count($results['warning']);
        $info_count = count($results['info']);

        $health_score = 100;
        $health_score -= $critical_count * 10;
        $health_score -= $warning_count * 5;
        $health_score -= $info_count * 1;
        $health_score = max(0, min(100, $health_score));

        // Send response
        wp_send_json_success(array(
            'results' => $results,
            'counts' => array(
                'critical' => $critical_count,
                'warning' => $warning_count,
                'info' => $info_count,
                'total' => $critical_count + $warning_count + $info_count
            ),
            'health_score' => $health_score
        ));
    }

    /**
     * AJAX handler for fixing an issue
     */
    public function ajax_fix_issue() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed.'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
        }

        // Get issue data
        $issue_id = isset($_POST['issue_id']) ? sanitize_text_field($_POST['issue_id']) : '';
        $issue_type = isset($_POST['issue_type']) ? sanitize_text_field($_POST['issue_type']) : '';

        if (empty($issue_id) || empty($issue_type)) {
            wp_send_json_error(array('message' => 'Invalid issue data.'));
        }

        // Fix the issue
        $result = $this->fix_issue($issue_id, $issue_type);

        if ($result['success']) {
            wp_send_json_success($result);
        } else {
            wp_send_json_error($result);
        }
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Add main menu
        add_menu_page(
            'WP Health & SEO Sentinel',
            'SEO Sentinel',
            'manage_options',
            'wp-hss',
            array($this, 'render_dashboard_page'),
            'dashicons-chart-area',
            100
        );

        // Add submenu pages
        add_submenu_page(
            'wp-hss',
            'Dashboard',
            'Dashboard',
            'manage_options',
            'wp-hss',
            array($this, 'render_dashboard_page')
        );

        add_submenu_page(
            'wp-hss',
            'Scanner',
            'Scanner',
            'manage_options',
            'wp-hss-scanner',
            array($this, 'render_scanner_page')
        );

        add_submenu_page(
            'wp-hss',
            'SEO Tools',
            'SEO Tools',
            'manage_options',
            'wp-hss-tools',
            array($this, 'render_tools_page')
        );

        add_submenu_page(
            'wp-hss',
            'Settings',
            'Settings',
            'manage_options',
            'wp-hss-settings',
            array($this, 'render_settings_page')
        );

        add_submenu_page(
            'wp-hss',
            'Settings Test',
            'Settings Test',
            'manage_options',
            'wp-hss-settings-test',
            array($this, 'render_settings_test_page')
        );

        add_submenu_page(
            'wp-hss',
            'Simple Settings',
            '🔥 Simple Settings',
            'manage_options',
            'wp-hss-simple-settings',
            array($this, 'render_simple_settings_page')
        );
    }

    /**
     * Add settings link to plugins page
     */
    public function add_settings_link($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=wp-hss-settings') . '">' . __('Settings', 'wp-hss') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_scripts($hook) {
        // Only load on our plugin pages
        if (strpos($hook, 'wp-hss') === false) {
            return;
        }

        // Enqueue styles
        wp_enqueue_style('wp-hss-admin', WP_HSS_PLUGIN_URL . 'css/admin.css', array(), WP_HSS_VERSION);

        // Enqueue scripts
        wp_enqueue_script('jquery');
        wp_enqueue_script('wp-hss-admin', WP_HSS_PLUGIN_URL . 'js/admin.js', array('jquery'), WP_HSS_VERSION, true);

        // Localize script
        wp_localize_script('wp-hss-admin', 'wp_hss', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wp_hss_nonce'),
            'strings' => array(
                'scanning' => __('Scanning...', 'wp-hss'),
                'scan_complete' => __('Scan Complete', 'wp-hss'),
                'scan_error' => __('Scan Error', 'wp-hss'),
                'fixing' => __('Fixing...', 'wp-hss'),
                'fix_complete' => __('Fixed', 'wp-hss'),
                'fix_error' => __('Fix Error', 'wp-hss')
            )
        ));
    }

    /**
     * APPLY ALL ACTIVE FIXES ON EVERY PAGE LOAD
     */
    public function apply_active_fixes() {
        $active_fixes = get_option('wp_hss_active_fixes', array());

        if (!empty($active_fixes)) {
            // Apply viewport meta tag fix
            if (isset($active_fixes['viewport_meta']) && $active_fixes['viewport_meta']) {
                add_action('wp_head', function() {
                    echo '<meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />' . "\n";
                }, 0);
            }

            // Apply canonical tag fix
            if (isset($active_fixes['canonical_tag']) && $active_fixes['canonical_tag']) {
                add_action('wp_head', function() {
                    if (is_singular()) {
                        echo '<link rel="canonical" href="' . get_permalink() . '" />' . "\n";
                    } elseif (is_home() || is_front_page()) {
                        echo '<link rel="canonical" href="' . home_url('/') . '" />' . "\n";
                    } elseif (is_category()) {
                        echo '<link rel="canonical" href="' . get_category_link(get_queried_object_id()) . '" />' . "\n";
                    } elseif (is_tag()) {
                        echo '<link rel="canonical" href="' . get_tag_link(get_queried_object_id()) . '" />' . "\n";
                    } elseif (is_author()) {
                        echo '<link rel="canonical" href="' . get_author_posts_url(get_queried_object_id()) . '" />' . "\n";
                    }
                }, 1);
            }

            // Apply robots meta tag fix
            if (isset($active_fixes['robots_meta']) && $active_fixes['robots_meta']) {
                add_action('wp_head', function() {
                    if (is_admin() || is_feed() || is_robots() || is_trackback()) {
                        return;
                    }

                    $robots_content = 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1';

                    if (is_search() || is_404()) {
                        $robots_content = 'noindex, follow';
                    } elseif (is_attachment()) {
                        $robots_content = 'noindex, follow';
                    }

                    echo '<meta name="robots" content="' . esc_attr($robots_content) . '" />' . "\n";
                }, 1);
            }
        }
    }

    /**
     * REAL LIVE SEO SCAN - ANALYZES YOUR ACTUAL WEBSITE
     */
    private function scan_site() {
        $results = array(
            'critical' => array(),
            'warning' => array(),
            'info' => array()
        );

        // REAL LIVE SEO ANALYSIS - NO FAKE DEMO SHIT
        $this->scan_live_canonical_tags($results);
        $this->scan_live_meta_title($results);
        $this->scan_live_meta_description($results);
        $this->scan_live_robots_meta($results);
        $this->scan_live_image_alt_text($results);
        $this->scan_live_h1_tags($results);
        $this->scan_live_schema_markup($results);
        $this->scan_live_open_graph($results);
        $this->scan_live_internal_links($results);
        $this->scan_live_external_links($results);
        $this->scan_live_headings_hierarchy($results);
        $this->scan_live_ssl($results);
        $this->scan_live_sitemap($results);
        $this->scan_live_robots_txt($results);
        $this->scan_live_mobile_viewport($results);

        return $results;
    }

    /**
     * REAL LIVE CANONICAL TAG SCAN
     */
    private function scan_live_canonical_tags(&$results) {
        // Get the actual website URL and scan it
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        // Use cURL to get the real page content
        $content = $this->get_page_content($site_url);

        if ($content) {
            // Check for canonical tag
            if (preg_match('/<link[^>]+rel=["\']canonical["\'][^>]*href=["\']([^"\']+)["\'][^>]*>/i', $content, $matches)) {
                $canonical_url = $matches[1];
                $results['info'][] = array(
                    'id' => 'canonical_found',
                    'type' => 'canonical',
                    'title' => 'Canonical Tag Found',
                    'description' => "Canonical URL: $canonical_url",
                    'details' => 'Canonical tags help prevent duplicate content issues.',
                    'fixable' => false,
                    'score_impact' => 5.0
                );
            } else {
                $results['critical'][] = array(
                    'id' => 'canonical_missing',
                    'type' => 'canonical',
                    'title' => 'Canonical Tag Missing',
                    'description' => 'No canonical tag found on your homepage.',
                    'details' => 'Canonical tags are essential for preventing duplicate content issues.',
                    'fixable' => true,
                    'score_impact' => -8.0
                );
            }
        }
    }

    /**
     * REAL LIVE META TITLE SCAN
     */
    private function scan_live_meta_title(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        $content = $this->get_page_content($site_url);

        if ($content) {
            if (preg_match('/<title[^>]*>(.*?)<\/title>/i', $content, $matches)) {
                $title = trim($matches[1]);
                $length = strlen($title);

                if ($length < 30) {
                    $results['critical'][] = array(
                        'id' => 'title_too_short',
                        'type' => 'meta',
                        'title' => 'Title Too Short',
                        'description' => "Title is only $length characters: \"$title\"",
                        'details' => 'Titles should be 30-60 characters for optimal SEO.',
                        'fixable' => true,
                        'score_impact' => -6.0
                    );
                } elseif ($length > 60) {
                    $results['warning'][] = array(
                        'id' => 'title_too_long',
                        'type' => 'meta',
                        'title' => 'Title Too Long',
                        'description' => "Title is $length characters: \"$title\"",
                        'details' => 'Long titles may be truncated in search results.',
                        'fixable' => true,
                        'score_impact' => -3.0
                    );
                } else {
                    $results['info'][] = array(
                        'id' => 'title_good',
                        'type' => 'meta',
                        'title' => 'Title Length Good',
                        'description' => "Title is $length characters: \"$title\"",
                        'details' => 'Title length is within optimal range.',
                        'fixable' => false,
                        'score_impact' => 4.0
                    );
                }
            } else {
                $results['critical'][] = array(
                    'id' => 'title_missing',
                    'type' => 'meta',
                    'title' => 'Title Tag Missing',
                    'description' => 'No title tag found on your homepage.',
                    'details' => 'Title tags are critical for SEO and user experience.',
                    'fixable' => true,
                    'score_impact' => -10.0
                );
            }
        }
    }

    /**
     * REAL LIVE META DESCRIPTION SCAN
     */
    private function scan_live_meta_description(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        $content = $this->get_page_content($site_url);

        if ($content) {
            if (preg_match('/<meta[^>]+name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $content, $matches)) {
                $description = trim($matches[1]);
                $length = strlen($description);

                if ($length < 120) {
                    $results['warning'][] = array(
                        'id' => 'description_too_short',
                        'type' => 'meta',
                        'title' => 'Meta Description Too Short',
                        'description' => "Description is only $length characters: \"$description\"",
                        'details' => 'Meta descriptions should be 120-160 characters for best results.',
                        'fixable' => true,
                        'score_impact' => -4.0
                    );
                } elseif ($length > 160) {
                    $results['warning'][] = array(
                        'id' => 'description_too_long',
                        'type' => 'meta',
                        'title' => 'Meta Description Too Long',
                        'description' => "Description is $length characters: \"$description\"",
                        'details' => 'Long descriptions may be truncated in search results.',
                        'fixable' => true,
                        'score_impact' => -2.0
                    );
                } else {
                    $results['info'][] = array(
                        'id' => 'description_good',
                        'type' => 'meta',
                        'title' => 'Meta Description Good',
                        'description' => "Description is $length characters: \"$description\"",
                        'details' => 'Meta description length is optimal.',
                        'fixable' => false,
                        'score_impact' => 3.0
                    );
                }
            } else {
                $results['critical'][] = array(
                    'id' => 'description_missing',
                    'type' => 'meta',
                    'title' => 'Meta Description Missing',
                    'description' => 'No meta description found on your homepage.',
                    'details' => 'Meta descriptions are crucial for click-through rates from search results.',
                    'fixable' => true,
                    'score_impact' => -7.0
                );
            }
        }
    }

    /**
     * GET REAL PAGE CONTENT USING CURL
     */
    private function get_page_content($url) {
        // Try cURL first
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_USERAGENT, 'SEO Scanner Bot');
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $content = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($http_code === 200 && $content) {
                return $content;
            }
        }

        // Fallback to file_get_contents
        if (function_exists('file_get_contents')) {
            $context = stream_context_create(array(
                'http' => array(
                    'timeout' => 10,
                    'user_agent' => 'SEO Scanner Bot'
                )
            ));

            $content = @file_get_contents($url, false, $context);
            if ($content) {
                return $content;
            }
        }

        return false;
    }

    /**
     * REAL LIVE ROBOTS META SCAN
     */
    private function scan_live_robots_meta(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        $content = $this->get_page_content($site_url);

        if ($content) {
            if (preg_match('/<meta[^>]+name=["\']robots["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $content, $matches)) {
                $robots_content = trim($matches[1]);
                $results['info'][] = array(
                    'id' => 'robots_meta_found',
                    'type' => 'robots',
                    'title' => 'Robots Meta Tag Found',
                    'description' => "Robots directive: $robots_content",
                    'details' => 'Robots meta tags control how search engines crawl and index your pages.',
                    'fixable' => false,
                    'score_impact' => 3.0
                );
            } else {
                $results['warning'][] = array(
                    'id' => 'robots_meta_missing',
                    'type' => 'robots',
                    'title' => 'Robots Meta Tag Missing',
                    'description' => 'No robots meta tag found on your homepage.',
                    'details' => 'Robots meta tags help control search engine crawling and indexing.',
                    'fixable' => true,
                    'score_impact' => -3.0
                );
            }
        }
    }

    /**
     * REAL LIVE IMAGE ALT TEXT SCAN
     */
    private function scan_live_image_alt_text(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        $content = $this->get_page_content($site_url);

        if ($content) {
            preg_match_all('/<img[^>]*>/i', $content, $img_matches);
            $total_images = count($img_matches[0]);
            $images_without_alt = 0;

            foreach ($img_matches[0] as $img_tag) {
                if (strpos($img_tag, 'alt=') === false || preg_match('/alt=["\']["\']/', $img_tag)) {
                    $images_without_alt++;
                }
            }

            if ($images_without_alt > 0) {
                $results['critical'][] = array(
                    'id' => 'images_missing_alt_live',
                    'type' => 'images',
                    'title' => 'Images Missing Alt Text',
                    'description' => "$images_without_alt out of $total_images images are missing alt text.",
                    'details' => 'Alt text is crucial for accessibility and SEO.',
                    'fixable' => true,
                    'score_impact' => -5.0
                );
            } else if ($total_images > 0) {
                $results['info'][] = array(
                    'id' => 'images_alt_text_good_live',
                    'type' => 'images',
                    'title' => 'All Images Have Alt Text',
                    'description' => "All $total_images images have alt text.",
                    'details' => 'Great job! Alt text improves accessibility and SEO.',
                    'fixable' => false,
                    'score_impact' => 4.0
                );
            }
        }
    }

    /**
     * REAL LIVE H1 TAGS SCAN
     */
    private function scan_live_h1_tags(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        $content = $this->get_page_content($site_url);

        if ($content) {
            preg_match_all('/<h1[^>]*>(.*?)<\/h1>/i', $content, $h1_matches);
            $h1_count = count($h1_matches[0]);

            if ($h1_count === 0) {
                $results['critical'][] = array(
                    'id' => 'h1_missing_live',
                    'type' => 'headings',
                    'title' => 'H1 Tag Missing',
                    'description' => 'No H1 tag found on your homepage.',
                    'details' => 'H1 tags are essential for SEO and content structure.',
                    'fixable' => true,
                    'score_impact' => -8.0
                );
            } elseif ($h1_count === 1) {
                $h1_text = strip_tags($h1_matches[1][0]);
                $results['info'][] = array(
                    'id' => 'h1_perfect_live',
                    'type' => 'headings',
                    'title' => 'Perfect H1 Structure',
                    'description' => "One H1 tag found: \"$h1_text\"",
                    'details' => 'Perfect! One H1 tag provides clear page structure.',
                    'fixable' => false,
                    'score_impact' => 6.0
                );
            } else {
                $results['warning'][] = array(
                    'id' => 'h1_multiple_live',
                    'type' => 'headings',
                    'title' => 'Multiple H1 Tags',
                    'description' => "$h1_count H1 tags found. Should have only one.",
                    'details' => 'Multiple H1 tags can confuse search engines about page topic.',
                    'fixable' => true,
                    'score_impact' => -4.0
                );
            }
        }
    }

    /**
     * REAL LIVE SCHEMA MARKUP SCAN
     */
    private function scan_live_schema_markup(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        $content = $this->get_page_content($site_url);

        if ($content) {
            $schema_types = array();

            // Check for JSON-LD schema
            if (preg_match_all('/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/is', $content, $json_matches)) {
                foreach ($json_matches[1] as $json_content) {
                    $data = json_decode(trim($json_content), true);
                    if ($data && isset($data['@type'])) {
                        $schema_types[] = $data['@type'];
                    }
                }
            }

            // Check for microdata
            if (preg_match_all('/itemtype=["\']([^"\']*)["\']/', $content, $microdata_matches)) {
                foreach ($microdata_matches[1] as $type) {
                    $schema_types[] = basename($type);
                }
            }

            if (!empty($schema_types)) {
                $unique_types = array_unique($schema_types);
                $results['info'][] = array(
                    'id' => 'schema_found_live',
                    'type' => 'schema',
                    'title' => 'Schema Markup Found',
                    'description' => 'Schema types: ' . implode(', ', $unique_types),
                    'details' => 'Schema markup helps search engines understand your content.',
                    'fixable' => false,
                    'score_impact' => 5.0
                );
            } else {
                $results['warning'][] = array(
                    'id' => 'schema_missing_live',
                    'type' => 'schema',
                    'title' => 'Schema Markup Missing',
                    'description' => 'No schema markup detected on your homepage.',
                    'details' => 'Schema markup can improve search result appearance with rich snippets.',
                    'fixable' => true,
                    'score_impact' => -4.0
                );
            }
        }
    }

    /**
     * REAL LIVE OPEN GRAPH SCAN
     */
    private function scan_live_open_graph(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        $content = $this->get_page_content($site_url);

        if ($content) {
            preg_match_all('/<meta[^>]+property=["\']og:([^"\']*)["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $content, $og_matches);
            $og_count = count($og_matches[0]);

            if ($og_count > 0) {
                $og_properties = array_combine($og_matches[1], $og_matches[2]);
                $results['info'][] = array(
                    'id' => 'open_graph_found_live',
                    'type' => 'social',
                    'title' => 'Open Graph Tags Found',
                    'description' => "$og_count Open Graph tags detected.",
                    'details' => 'Open Graph tags improve social media sharing appearance.',
                    'fixable' => false,
                    'score_impact' => 4.0
                );
            } else {
                $results['warning'][] = array(
                    'id' => 'open_graph_missing_live',
                    'type' => 'social',
                    'title' => 'Open Graph Tags Missing',
                    'description' => 'No Open Graph tags found on your homepage.',
                    'details' => 'Open Graph tags control how your content appears on social media.',
                    'fixable' => true,
                    'score_impact' => -3.0
                );
            }
        }
    }

    /**
     * REAL LIVE INTERNAL LINKS SCAN
     */
    private function scan_live_internal_links(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        $content = $this->get_page_content($site_url);

        if ($content) {
            preg_match_all('/<a[^>]+href=["\']([^"\']*)["\'][^>]*>/i', $content, $link_matches);
            $total_links = count($link_matches[0]);
            $internal_links = 0;

            foreach ($link_matches[1] as $url) {
                if (strpos($url, $site_url) !== false || strpos($url, '/') === 0) {
                    $internal_links++;
                }
            }

            if ($internal_links > 0) {
                $results['info'][] = array(
                    'id' => 'internal_links_found_live',
                    'type' => 'links',
                    'title' => 'Internal Links Found',
                    'description' => "$internal_links internal links out of $total_links total links.",
                    'details' => 'Internal links help with site navigation and SEO.',
                    'fixable' => false,
                    'score_impact' => 3.0
                );
            } else {
                $results['warning'][] = array(
                    'id' => 'internal_links_missing_live',
                    'type' => 'links',
                    'title' => 'No Internal Links',
                    'description' => 'No internal links found on your homepage.',
                    'details' => 'Internal links improve navigation and help distribute page authority.',
                    'fixable' => true,
                    'score_impact' => -2.0
                );
            }
        }
    }

    /**
     * REAL LIVE EXTERNAL LINKS SCAN
     */
    private function scan_live_external_links(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        $content = $this->get_page_content($site_url);

        if ($content) {
            preg_match_all('/<a[^>]+href=["\']([^"\']*)["\'][^>]*>/i', $content, $link_matches);
            $external_links = 0;

            foreach ($link_matches[1] as $url) {
                if (strpos($url, 'http') === 0 && strpos($url, $site_url) === false) {
                    $external_links++;
                }
            }

            if ($external_links > 0) {
                $results['info'][] = array(
                    'id' => 'external_links_found_live',
                    'type' => 'links',
                    'title' => 'External Links Found',
                    'description' => "$external_links external links detected.",
                    'details' => 'External links to authoritative sources can add value.',
                    'fixable' => false,
                    'score_impact' => 2.0
                );
            }
        }
    }

    /**
     * REAL LIVE HEADINGS HIERARCHY SCAN
     */
    private function scan_live_headings_hierarchy(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        $content = $this->get_page_content($site_url);

        if ($content) {
            $headings = array();
            for ($i = 1; $i <= 6; $i++) {
                preg_match_all("/<h{$i}[^>]*>(.*?)<\/h{$i}>/i", $content, $matches);
                $headings["h$i"] = count($matches[0]);
            }

            $total_headings = array_sum($headings);

            if ($total_headings > 0) {
                $heading_summary = array();
                foreach ($headings as $tag => $count) {
                    if ($count > 0) {
                        $heading_summary[] = "$count $tag";
                    }
                }

                $results['info'][] = array(
                    'id' => 'headings_structure_live',
                    'type' => 'headings',
                    'title' => 'Heading Structure Found',
                    'description' => 'Headings: ' . implode(', ', $heading_summary),
                    'details' => 'Good heading structure improves content organization and SEO.',
                    'fixable' => false,
                    'score_impact' => 3.0
                );
            } else {
                $results['warning'][] = array(
                    'id' => 'headings_missing_live',
                    'type' => 'headings',
                    'title' => 'No Heading Structure',
                    'description' => 'No heading tags (H1-H6) found on your homepage.',
                    'details' => 'Proper heading structure is important for SEO and accessibility.',
                    'fixable' => true,
                    'score_impact' => -4.0
                );
            }
        }
    }

    /**
     * REAL LIVE SSL SCAN
     */
    private function scan_live_ssl(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        if (strpos($site_url, 'https://') === 0) {
            $results['info'][] = array(
                'id' => 'ssl_enabled_live',
                'type' => 'security',
                'title' => 'SSL Certificate Active',
                'description' => 'Your site is using HTTPS.',
                'details' => 'SSL certificates are essential for security and SEO.',
                'fixable' => false,
                'score_impact' => 5.0
            );
        } else {
            $results['critical'][] = array(
                'id' => 'ssl_missing_live',
                'type' => 'security',
                'title' => 'SSL Certificate Missing',
                'description' => 'Your site is not using HTTPS.',
                'details' => 'SSL certificates are required for security and better search rankings.',
                'fixable' => true,
                'score_impact' => -8.0
            );
        }
    }

    /**
     * REAL LIVE SITEMAP SCAN
     */
    private function scan_live_sitemap(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        $sitemap_url = $site_url . '/sitemap.xml';
        $content = $this->get_page_content($sitemap_url);

        if ($content && strpos($content, '<urlset') !== false) {
            $results['info'][] = array(
                'id' => 'sitemap_found_live',
                'type' => 'sitemap',
                'title' => 'XML Sitemap Found',
                'description' => 'XML sitemap is accessible at /sitemap.xml',
                'details' => 'Sitemaps help search engines discover and index your content.',
                'fixable' => false,
                'score_impact' => 4.0
            );
        } else {
            $results['warning'][] = array(
                'id' => 'sitemap_missing_live',
                'type' => 'sitemap',
                'title' => 'XML Sitemap Missing',
                'description' => 'No XML sitemap found at /sitemap.xml',
                'details' => 'XML sitemaps help search engines find and index your content.',
                'fixable' => true,
                'score_impact' => -3.0
            );
        }
    }

    /**
     * REAL LIVE ROBOTS.TXT SCAN
     */
    private function scan_live_robots_txt(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        $robots_url = $site_url . '/robots.txt';
        $content = $this->get_page_content($robots_url);

        if ($content && strlen(trim($content)) > 0) {
            $results['info'][] = array(
                'id' => 'robots_txt_found_live',
                'type' => 'robots',
                'title' => 'Robots.txt Found',
                'description' => 'Robots.txt file is accessible.',
                'details' => 'Robots.txt helps control search engine crawling.',
                'fixable' => false,
                'score_impact' => 2.0
            );
        } else {
            $results['warning'][] = array(
                'id' => 'robots_txt_missing_live',
                'type' => 'robots',
                'title' => 'Robots.txt Missing',
                'description' => 'No robots.txt file found.',
                'details' => 'Robots.txt files help guide search engine crawlers.',
                'fixable' => true,
                'score_impact' => -2.0
            );
        }
    }

    /**
     * REAL LIVE MOBILE VIEWPORT SCAN
     */
    private function scan_live_mobile_viewport(&$results) {
        $site_url = get_site_url();
        if (!$site_url) $site_url = 'http://localhost';

        $content = $this->get_page_content($site_url);

        if ($content) {
            if (preg_match('/<meta[^>]+name=["\']viewport["\'][^>]*>/i', $content)) {
                $results['info'][] = array(
                    'id' => 'viewport_found_live',
                    'type' => 'mobile',
                    'title' => 'Viewport Meta Tag Found',
                    'description' => 'Mobile viewport meta tag is present.',
                    'details' => 'Viewport tags ensure proper mobile display.',
                    'fixable' => false,
                    'score_impact' => 4.0
                );
            } else {
                $results['critical'][] = array(
                    'id' => 'viewport_missing_live',
                    'type' => 'mobile',
                    'title' => 'Viewport Meta Tag Missing',
                    'description' => 'No viewport meta tag found.',
                    'details' => 'Viewport meta tags are essential for mobile responsiveness.',
                    'fixable' => true,
                    'score_impact' => -6.0
                );
            }
        }
    }

    /**
     * COMPREHENSIVE CANONICAL TAG ANALYSIS
     */
    private function check_canonical_tags(&$results) {
        // Check if WordPress functions are available
        if (!function_exists('home_url') || !function_exists('wp_remote_get')) {
            return;
        }

        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && function_exists('wp_remote_retrieve_response_code') && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            // Check for canonical tag
            preg_match('/<link[^>]+rel=["\']canonical["\'][^>]*href=["\']([^"\']+)["\'][^>]*>/i', $homepage_content, $canonical_matches);

            if (empty($canonical_matches)) {
                $results['critical'][] = array(
                    'id' => 'canonical_missing',
                    'type' => 'canonical',
                    'title' => 'Canonical Tag Missing',
                    'description' => 'Your homepage does not have a canonical tag.',
                    'details' => 'Canonical tags help avoid duplicate content issues by specifying the preferred version of a page.',
                    'fixable' => true,
                    'score_impact' => -7.5
                );
            } else {
                $canonical_url = $canonical_matches[1];
                $results['info'][] = array(
                    'id' => 'canonical_present',
                    'type' => 'canonical',
                    'title' => 'Canonical Tag Present',
                    'description' => "Canonical tag is present: $canonical_url",
                    'details' => 'Helps avoid duplicate content issues by specifying the preferred version of a page.',
                    'fixable' => false,
                    'score_impact' => 7.5
                );
            }
        }
    }

    /**
     * ADVANCED META TITLE ANALYSIS
     */
    private function check_meta_title_advanced(&$results) {
        if (!function_exists('home_url') || !function_exists('wp_remote_get')) {
            return;
        }

        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            preg_match('/<title>(.*?)<\/title>/i', $homepage_content, $title_matches);

            if (empty($title_matches)) {
                $results['critical'][] = array(
                    'id' => 'meta_title_missing',
                    'type' => 'meta',
                    'title' => 'Meta Title Missing',
                    'description' => 'Your homepage does not have a meta title.',
                    'details' => 'The title tag is critical for SEO and appears in search engine results and browser tabs.',
                    'fixable' => true,
                    'score_impact' => -7.5
                );
            } else {
                $title = trim($title_matches[1]);
                $results['info'][] = array(
                    'id' => 'meta_title_present',
                    'type' => 'meta',
                    'title' => 'Meta Title Present',
                    'description' => "Meta title is present: \"$title\"",
                    'details' => 'The title tag is critical for SEO and appears in search engine results and browser tabs.',
                    'fixable' => false,
                    'score_impact' => 7.5
                );
            }
        }
    }

    /**
     * ADVANCED META DESCRIPTION ANALYSIS
     */
    private function check_meta_description_advanced(&$results) {
        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            preg_match('/<meta[^>]+name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $homepage_content, $desc_matches);

            if (empty($desc_matches)) {
                $results['critical'][] = array(
                    'id' => 'meta_description_missing_advanced',
                    'type' => 'meta',
                    'title' => 'Meta Description Missing',
                    'description' => 'Your homepage does not have a meta description.',
                    'details' => 'Meta descriptions appear in search results and help improve click-through rates.',
                    'fixable' => true,
                    'score_impact' => -7.5
                );
            } else {
                $description = trim($desc_matches[1]);
                $results['info'][] = array(
                    'id' => 'meta_description_present',
                    'type' => 'meta',
                    'title' => 'Meta Description Present',
                    'description' => "Meta description is present: \"$description\"",
                    'details' => 'Meta descriptions appear in search results and help improve click-through rates.',
                    'fixable' => false,
                    'score_impact' => 7.5
                );
            }
        }
    }

    /**
     * SEO FRIENDLY TITLE ANALYSIS
     */
    private function check_seo_friendly_title(&$results) {
        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            preg_match('/<title>(.*?)<\/title>/i', $homepage_content, $title_matches);

            if (!empty($title_matches)) {
                $title = trim($title_matches[1]);

                // Define target keywords (you can make this configurable)
                $target_keywords = array('Personalized Workout Plans', 'Fitness Transformation', 'Weight Loss');
                $missing_keywords = array();

                foreach ($target_keywords as $keyword) {
                    if (stripos($title, $keyword) === false) {
                        $missing_keywords[] = $keyword;
                    }
                }

                if (!empty($missing_keywords)) {
                    $results['warning'][] = array(
                        'id' => 'seo_title_keywords_missing',
                        'type' => 'seo',
                        'title' => 'SEO Friendly Title Warning',
                        'description' => 'Some keywords are missing from the title: ' . implode(', ', $missing_keywords),
                        'details' => 'SEO-friendly titles should include top keywords to improve relevance and ranking.',
                        'fixable' => true,
                        'score_impact' => -1.5
                    );
                }
            }
        }
    }

    /**
     * SEO FRIENDLY DESCRIPTION ANALYSIS
     */
    private function check_seo_friendly_description(&$results) {
        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            preg_match('/<meta[^>]+name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $homepage_content, $desc_matches);

            if (!empty($desc_matches)) {
                $description = trim($desc_matches[1]);

                // Define target keywords for description
                $target_keywords = array('Fitness Transformation', 'Personalized Workout Plans', 'Nutrition Advice', 'Expert Support', '360° Body Transformation', 'Health Tools');
                $missing_keywords = array();

                foreach ($target_keywords as $keyword) {
                    if (stripos($description, $keyword) === false) {
                        $missing_keywords[] = $keyword;
                    }
                }

                if (!empty($missing_keywords)) {
                    $results['warning'][] = array(
                        'id' => 'seo_description_keywords_missing',
                        'type' => 'seo',
                        'title' => 'SEO Friendly Description Warning',
                        'description' => 'Some keywords are missing from the description: ' . implode(', ', $missing_keywords),
                        'details' => 'A keyword-optimized description helps target the right search queries.',
                        'fixable' => true,
                        'score_impact' => -9.0
                    );
                }
            }
        }
    }

    /**
     * ROBOTS META TAG ANALYSIS
     */
    private function check_robots_meta_tag(&$results) {
        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            preg_match('/<meta[^>]+name=["\']robots["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $homepage_content, $robots_matches);

            if (empty($robots_matches)) {
                $results['warning'][] = array(
                    'id' => 'robots_meta_missing',
                    'type' => 'robots',
                    'title' => 'Robots Meta Tag Missing',
                    'description' => 'Your homepage does not have a robots meta tag.',
                    'details' => 'The robots meta tag tells search engines whether they can index and follow links on your page.',
                    'fixable' => true,
                    'score_impact' => -7.5
                );
            } else {
                $robots_content = trim($robots_matches[1]);
                $results['info'][] = array(
                    'id' => 'robots_meta_present',
                    'type' => 'robots',
                    'title' => 'Robots Meta Tag Present',
                    'description' => "Robots meta tag is correctly set: \"$robots_content\"",
                    'details' => 'The robots meta tag tells search engines whether they can index and follow links on your page. Missing or misconfigured tags can harm visibility.',
                    'fixable' => false,
                    'score_impact' => 7.5
                );
            }
        }
    }

    /**
     * COMPREHENSIVE IMAGE ALT TEXT ANALYSIS
     */
    private function check_image_alt_text_comprehensive(&$results) {
        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            // Find all images
            preg_match_all('/<img[^>]*>/i', $homepage_content, $img_matches);
            $total_images = count($img_matches[0]);
            $images_without_alt = 0;

            foreach ($img_matches[0] as $img_tag) {
                if (strpos($img_tag, 'alt=') === false || preg_match('/alt=["\']["\']/', $img_tag)) {
                    $images_without_alt++;
                }
            }

            if ($images_without_alt > 0) {
                $results['critical'][] = array(
                    'id' => 'images_missing_alt_comprehensive',
                    'type' => 'images',
                    'title' => 'Image Alt Text Failed',
                    'description' => "$images_without_alt images are missing alt attributes.",
                    'details' => 'Alt text improves image accessibility and provides SEO value.',
                    'fixable' => true,
                    'score_impact' => -4.5
                );
            } else if ($total_images > 0) {
                $results['info'][] = array(
                    'id' => 'images_alt_text_good',
                    'type' => 'images',
                    'title' => 'Image Alt Text Passed',
                    'description' => "All $total_images images have alt attributes.",
                    'details' => 'Alt text improves image accessibility and provides SEO value.',
                    'fixable' => false,
                    'score_impact' => 4.5
                );
            }
        }
    }

    /**
     * H1 TAG ANALYSIS
     */
    private function check_h1_tag_analysis(&$results) {
        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            preg_match_all('/<h1[^>]*>(.*?)<\/h1>/i', $homepage_content, $h1_matches);
            $h1_count = count($h1_matches[0]);

            if ($h1_count === 0) {
                $results['critical'][] = array(
                    'id' => 'h1_tag_missing',
                    'type' => 'headings',
                    'title' => 'H1 Tag Missing',
                    'description' => 'No H1 tag found on the homepage.',
                    'details' => 'An H1 tag helps search engines understand the main topic of the page.',
                    'fixable' => true,
                    'score_impact' => -7.5
                );
            } elseif ($h1_count === 1) {
                $results['info'][] = array(
                    'id' => 'h1_tag_perfect',
                    'type' => 'headings',
                    'title' => 'H1 Tag Passed',
                    'description' => 'Exactly one H1 tag found.',
                    'details' => 'An H1 tag helps search engines understand the main topic of the page.',
                    'fixable' => false,
                    'score_impact' => 7.5
                );
            } else {
                $results['warning'][] = array(
                    'id' => 'h1_tag_multiple',
                    'type' => 'headings',
                    'title' => 'Multiple H1 Tags',
                    'description' => "$h1_count H1 tags found. Recommended to have only one.",
                    'details' => 'Multiple H1 tags can confuse search engines about the main topic.',
                    'fixable' => true,
                    'score_impact' => -3.0
                );
            }
        }
    }

    /**
     * ADVANCED SCHEMA MARKUP ANALYSIS
     */
    private function check_schema_markup_advanced(&$results) {
        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            $schema_types = array();

            // Check for JSON-LD schema
            if (preg_match_all('/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/is', $homepage_content, $json_matches)) {
                foreach ($json_matches[1] as $json_content) {
                    $data = json_decode(trim($json_content), true);
                    if ($data && isset($data['@type'])) {
                        $schema_types[] = $data['@type'];
                    }
                }
            }

            // Check for microdata
            if (preg_match_all('/itemtype=["\']([^"\']*)["\']/', $homepage_content, $microdata_matches)) {
                foreach ($microdata_matches[1] as $type) {
                    $schema_types[] = basename($type);
                }
            }

            if (!empty($schema_types)) {
                $unique_types = array_unique($schema_types);
                $results['info'][] = array(
                    'id' => 'schema_markup_found',
                    'type' => 'schema',
                    'title' => 'Schema Markup Passed',
                    'description' => 'Schema types found: ' . implode(', ', $unique_types),
                    'details' => 'Structured data helps search engines understand your content better.',
                    'fixable' => false,
                    'score_impact' => 6.0
                );
            } else {
                $results['warning'][] = array(
                    'id' => 'schema_markup_missing_advanced',
                    'type' => 'schema',
                    'title' => 'Schema Markup Missing',
                    'description' => 'No schema markup detected on the homepage.',
                    'details' => 'Schema markup helps search engines understand your content better and can result in rich snippets.',
                    'fixable' => true,
                    'score_impact' => -6.0
                );
            }
        }
    }

    /**
     * Check permalink structure
     */
    private function check_permalink_structure(&$results) {
        // Get permalink structure
        $permalink_structure = get_option('permalink_structure');

        if (empty($permalink_structure)) {
            $results['warning'][] = array(
                'id' => 'plain_permalinks',
                'type' => 'permalink',
                'title' => 'Plain Permalinks Detected',
                'description' => 'Your site is using plain permalinks, which are not SEO-friendly.',
                'details' => 'SEO-friendly URLs should contain relevant keywords and be readable by humans. Plain permalinks (e.g., "?p=123") do not provide any context to search engines or users.',
                'fixable' => true
            );
        } elseif (strpos($permalink_structure, '%postname%') === false) {
            $results['info'][] = array(
                'id' => 'non_optimal_permalinks',
                'type' => 'permalink',
                'title' => 'Non-Optimal Permalink Structure',
                'description' => 'Your permalink structure does not include the post name, which is recommended for SEO.',
                'details' => 'Using post names in your permalinks makes URLs more descriptive and keyword-rich, which can improve SEO.',
                'fixable' => true
            );
        }
    }

    /**
     * Check for broken links
     */
    private function check_broken_links(&$results) {
        // Get recent posts to check for broken links
        $posts = get_posts(array(
            'post_type' => array('post', 'page'),
            'posts_per_page' => 10,
            'post_status' => 'publish'
        ));

        $broken_links = array();
        $total_links_checked = 0;

        foreach ($posts as $post) {
            $content = $post->post_content;

            // Extract links from content
            preg_match_all('/<a[^>]+href=["\']([^"\']+)["\'][^>]*>/i', $content, $matches);

            if (!empty($matches[1])) {
                foreach ($matches[1] as $url) {
                    // Skip internal anchors and javascript links
                    if (strpos($url, '#') === 0 || strpos($url, 'javascript:') === 0 || strpos($url, 'mailto:') === 0) {
                        continue;
                    }

                    $total_links_checked++;

                    // Check if link is broken (limit to 5 links per scan to avoid timeout)
                    if ($total_links_checked <= 5) {
                        $response = wp_remote_head($url, array(
                            'timeout' => 10,
                            'redirection' => 5,
                            'user-agent' => 'WordPress SEO Scanner'
                        ));

                        if (is_wp_error($response)) {
                            $broken_links[] = array(
                                'url' => $url,
                                'post_title' => $post->post_title,
                                'error' => $response->get_error_message()
                            );
                        } else {
                            $status_code = wp_remote_retrieve_response_code($response);
                            if ($status_code >= 400) {
                                $broken_links[] = array(
                                    'url' => $url,
                                    'post_title' => $post->post_title,
                                    'error' => 'HTTP ' . $status_code
                                );
                            }
                        }
                    }
                }
            }
        }

        if (!empty($broken_links)) {
            $broken_list = array();
            foreach ($broken_links as $link) {
                $broken_list[] = $link['url'] . ' (in "' . $link['post_title'] . '")';
            }

            $results['warning'][] = array(
                'id' => 'broken_links_found',
                'type' => 'links',
                'title' => 'Broken Links Detected',
                'description' => count($broken_links) . ' broken links found: ' . implode(', ', array_slice($broken_list, 0, 3)) . (count($broken_list) > 3 ? '...' : ''),
                'details' => 'Broken links can hurt your SEO and user experience. Consider fixing or removing these links.',
                'fixable' => false
            );
        } else if ($total_links_checked > 0) {
            $results['info'][] = array(
                'id' => 'no_broken_links',
                'type' => 'links',
                'title' => 'No Broken Links Found',
                'description' => 'Checked ' . min($total_links_checked, 5) . ' links and found no broken links.',
                'details' => 'Your links appear to be working correctly. This is good for SEO and user experience.',
                'fixable' => false
            );
        }

        if ($total_links_checked > 5) {
            $results['info'][] = array(
                'id' => 'limited_link_check',
                'type' => 'links',
                'title' => 'Limited Link Check',
                'description' => 'Only checked the first 5 links to avoid timeout. Total links found: ' . $total_links_checked,
                'details' => 'For a comprehensive broken link check, consider using a dedicated tool or running the scan during off-peak hours.',
                'fixable' => false
            );
        }
    }

    /**
     * Check heading structure
     */
    private function check_heading_structure(&$results) {
        // Get homepage content
        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            // Check for H1 tag
            preg_match_all('/<h1[^>]*>(.*?)<\/h1>/i', $homepage_content, $h1_matches);

            if (empty($h1_matches[0])) {
                $results['warning'][] = array(
                    'id' => 'missing_h1',
                    'type' => 'headings',
                    'title' => 'Missing H1 Tag',
                    'description' => 'Your homepage does not have an H1 heading tag.',
                    'details' => 'The H1 tag is a crucial SEO element that helps search engines understand the main topic of your page.',
                    'fixable' => true
                );
            } elseif (count($h1_matches[0]) > 1) {
                $results['info'][] = array(
                    'id' => 'multiple_h1',
                    'type' => 'headings',
                    'title' => 'Multiple H1 Tags',
                    'description' => 'Your homepage has ' . count($h1_matches[0]) . ' H1 tags. It\'s recommended to have only one H1 tag per page.',
                    'details' => 'Having multiple H1 tags can confuse search engines about the main topic of your page.',
                    'fixable' => true
                );
            }

            // Check heading hierarchy
            preg_match_all('/<h2[^>]*>(.*?)<\/h2>/i', $homepage_content, $h2_matches);
            preg_match_all('/<h3[^>]*>(.*?)<\/h3>/i', $homepage_content, $h3_matches);

            if (!empty($h3_matches[0]) && empty($h2_matches[0])) {
                $results['info'][] = array(
                    'id' => 'skipped_heading_level',
                    'type' => 'headings',
                    'title' => 'Skipped Heading Level',
                    'description' => 'Your homepage has H3 tags but no H2 tags, which suggests a skipped heading level.',
                    'details' => 'Proper heading hierarchy (H1 → H2 → H3) helps both users and search engines understand the structure of your content.',
                    'fixable' => true
                );
            }
        }
    }

    /**
     * Check keyword usage
     */
    private function check_keyword_usage(&$results) {
        // Get recent posts to analyze keyword usage
        $recent_posts = get_posts(array(
            'post_type' => array('post', 'page'),
            'posts_per_page' => 5,
            'post_status' => 'publish'
        ));

        if (empty($recent_posts)) {
            return;
        }

        $posts_with_keyword_issues = array();

        foreach ($recent_posts as $post) {
            $title = $post->post_title;
            $content = strip_tags($post->post_content);

            // Simple keyword analysis
            $title_words = str_word_count(strtolower($title), 1);
            $content_words = str_word_count(strtolower($content), 1);

            // Check if title words appear in content
            $title_in_content = 0;
            foreach ($title_words as $word) {
                if (strlen($word) > 3 && in_array($word, $content_words)) {
                    $title_in_content++;
                }
            }

            // If less than 50% of title words appear in content, flag it
            if (count($title_words) > 0 && ($title_in_content / count($title_words)) < 0.5) {
                $posts_with_keyword_issues[] = $post->post_title;
            }
        }

        if (!empty($posts_with_keyword_issues)) {
            $results['info'][] = array(
                'id' => 'keyword_consistency_issues',
                'type' => 'keywords',
                'title' => 'Keyword Consistency Issues',
                'description' => count($posts_with_keyword_issues) . ' posts may have keyword consistency issues: ' . implode(', ', array_slice($posts_with_keyword_issues, 0, 3)) . (count($posts_with_keyword_issues) > 3 ? '...' : ''),
                'details' => 'These posts have titles with words that don\'t appear frequently in the content. Consider improving keyword consistency between titles and content.',
                'fixable' => false
            );
        } else {
            $results['info'][] = array(
                'id' => 'good_keyword_consistency',
                'type' => 'keywords',
                'title' => 'Good Keyword Consistency',
                'description' => 'Your recent posts show good keyword consistency between titles and content.',
                'details' => 'This is good for SEO as it helps search engines understand what your content is about.',
                'fixable' => false
            );
        }
    }

    /**
     * Make AI API request
     */
    private function make_ai_request($prompt, $provider = 'openai') {
        $api_key = '';
        $endpoint = '';
        $headers = array();
        $body = array();

        switch ($provider) {
            case 'openai':
                $api_key = get_option('wp_hss_openai_api_key', '');
                $model = get_option('wp_hss_openai_model', 'gpt-4');
                $temperature = get_option('wp_hss_openai_temperature', 0.7);

                if (empty($api_key)) {
                    return array('error' => 'OpenAI API key not configured');
                }

                $endpoint = 'https://api.openai.com/v1/chat/completions';
                $headers = array(
                    'Authorization' => 'Bearer ' . $api_key,
                    'Content-Type' => 'application/json'
                );
                $body = array(
                    'model' => $model,
                    'messages' => array(
                        array('role' => 'user', 'content' => $prompt)
                    ),
                    'temperature' => floatval($temperature),
                    'max_tokens' => 1000
                );
                break;

            case 'anthropic':
                $api_key = get_option('wp_hss_anthropic_api_key', '');
                $model = get_option('wp_hss_anthropic_model', 'claude-3-sonnet');

                if (empty($api_key)) {
                    return array('error' => 'Anthropic API key not configured');
                }

                $endpoint = 'https://api.anthropic.com/v1/messages';
                $headers = array(
                    'x-api-key' => $api_key,
                    'Content-Type' => 'application/json',
                    'anthropic-version' => '2023-06-01'
                );
                $body = array(
                    'model' => $model,
                    'max_tokens' => 1000,
                    'messages' => array(
                        array('role' => 'user', 'content' => $prompt)
                    )
                );
                break;

            default:
                return array('error' => 'Unsupported AI provider');
        }

        $response = wp_remote_post($endpoint, array(
            'headers' => $headers,
            'body' => json_encode($body),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return array('error' => $response->get_error_message());
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($status_code !== 200) {
            return array('error' => 'API request failed with status ' . $status_code);
        }

        $data = json_decode($response_body, true);

        if ($provider === 'openai') {
            if (isset($data['choices'][0]['message']['content'])) {
                return array('content' => $data['choices'][0]['message']['content']);
            }
        } elseif ($provider === 'anthropic') {
            if (isset($data['content'][0]['text'])) {
                return array('content' => $data['content'][0]['text']);
            }
        }

        return array('error' => 'Unexpected API response format');
    }

    /**
     * Generate AI-optimized title
     */
    private function generate_ai_title($current_title, $content_excerpt) {
        $enabled_provider = $this->get_enabled_ai_provider();

        if (!$enabled_provider) {
            return array('error' => 'No AI provider enabled');
        }

        $prompt = "As an SEO expert, please optimize this title for better search engine rankings. Current title: \"$current_title\". Content excerpt: \"$content_excerpt\". Please provide a better SEO-optimized title that is 30-60 characters long, includes relevant keywords, and is compelling for users. Only return the optimized title, nothing else.";

        return $this->make_ai_request($prompt, $enabled_provider);
    }

    /**
     * Generate AI-optimized meta description
     */
    private function generate_ai_meta_description($title, $content_excerpt) {
        $enabled_provider = $this->get_enabled_ai_provider();

        if (!$enabled_provider) {
            return array('error' => 'No AI provider enabled');
        }

        $prompt = "As an SEO expert, please create an optimized meta description for this content. Title: \"$title\". Content excerpt: \"$content_excerpt\". Please provide a compelling meta description that is 120-160 characters long, includes relevant keywords, and encourages clicks. Only return the meta description, nothing else.";

        return $this->make_ai_request($prompt, $enabled_provider);
    }

    /**
     * Get enabled AI provider
     */
    private function get_enabled_ai_provider() {
        if (get_option('wp_hss_enable_openai', false) && !empty(get_option('wp_hss_openai_api_key', ''))) {
            return 'openai';
        }

        if (get_option('wp_hss_enable_anthropic', false) && !empty(get_option('wp_hss_anthropic_api_key', ''))) {
            return 'anthropic';
        }

        return false;
    }

    /**
     * Register settings
     */
    public function register_settings() {
        // Register a single settings group for all our options
        register_setting('wp_hss_settings', 'wp_hss_options', array($this, 'sanitize_settings'));
    }

    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();

        if (is_array($input)) {
            foreach ($input as $key => $value) {
                if (strpos($key, '_email') !== false) {
                    $sanitized[$key] = sanitize_email($value);
                } elseif (strpos($key, '_url') !== false || strpos($key, '_endpoint') !== false) {
                    $sanitized[$key] = esc_url_raw($value);
                } elseif (strpos($key, '_temperature') !== false || strpos($key, '_timeout') !== false || strpos($key, '_tokens') !== false || strpos($key, '_duration') !== false) {
                    $sanitized[$key] = floatval($value);
                } elseif (strpos($key, '_request_format') !== false) {
                    $sanitized[$key] = sanitize_textarea_field($value);
                } else {
                    $sanitized[$key] = sanitize_text_field($value);
                }
            }
        }

        return $sanitized;
    }



    /**
     * Check readability
     */
    private function check_readability(&$results) {
        // Get recent posts
        $recent_posts = get_posts(array(
            'post_type' => 'post',
            'posts_per_page' => 3,
            'post_status' => 'publish'
        ));

        if (empty($recent_posts)) {
            return;
        }

        $posts_with_readability_issues = array();

        foreach ($recent_posts as $post) {
            $content = strip_tags($post->post_content);

            // Simple readability check - sentence length
            $sentences = preg_split('/(?<=[.!?])\s+/', $content, -1, PREG_SPLIT_NO_EMPTY);
            $long_sentences = 0;

            foreach ($sentences as $sentence) {
                $words = explode(' ', $sentence);
                if (count($words) > 20) {
                    $long_sentences++;
                }
            }

            if ($long_sentences > 3) {
                $posts_with_readability_issues[] = $post->post_title;
            }
        }

        if (!empty($posts_with_readability_issues)) {
            $results['info'][] = array(
                'id' => 'readability_issues',
                'type' => 'readability',
                'title' => 'Readability Issues Detected',
                'description' => count($posts_with_readability_issues) . ' posts have potential readability issues: ' . implode(', ', $posts_with_readability_issues),
                'details' => 'Content with good readability uses short sentences, simple language, and clear structure. It\'s easier for users to read and can improve engagement metrics.',
                'fixable' => false
            );
        }
    }

    /**
     * Check page speed
     */
    private function check_page_speed(&$results) {
        // In a real implementation, this would use the Google PageSpeed Insights API
        // For this simple version, we'll just add a placeholder result

        $results['info'][] = array(
            'id' => 'page_speed',
            'type' => 'performance',
            'title' => 'Page Speed Analysis',
            'description' => 'Use the "Page Speed Analysis" tool in the SEO Tools section to check your site\'s loading speed.',
            'details' => 'Page speed is a ranking factor for search engines. Faster-loading pages provide a better user experience and can improve your search rankings.',
            'fixable' => false
        );
    }

    /**
     * Check image optimization
     */
    private function check_image_optimization(&$results) {
        // Get recent images
        $attachments = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => 5,
            'post_status' => 'inherit'
        ));

        if (empty($attachments)) {
            return;
        }

        $images_without_alt = array();
        $large_images = array();

        foreach ($attachments as $attachment) {
            // Check for alt text
            $alt_text = get_post_meta($attachment->ID, '_wp_attachment_image_alt', true);

            if (empty($alt_text)) {
                $images_without_alt[] = $attachment->post_title;
            }

            // Check image size
            $file_path = get_attached_file($attachment->ID);

            if ($file_path && file_exists($file_path)) {
                $file_size = filesize($file_path);

                if ($file_size > 500000) { // 500KB
                    $large_images[] = $attachment->post_title . ' (' . round($file_size / 1024) . 'KB)';
                }
            }
        }

        if (!empty($images_without_alt)) {
            $results['warning'][] = array(
                'id' => 'images_without_alt',
                'type' => 'images',
                'title' => 'Images Without Alt Text',
                'description' => count($images_without_alt) . ' images do not have alt text: ' . implode(', ', $images_without_alt),
                'details' => 'Alt text is important for accessibility and helps search engines understand the content of your images.',
                'fixable' => true
            );
        }

        if (!empty($large_images)) {
            $results['info'][] = array(
                'id' => 'large_images',
                'type' => 'images',
                'title' => 'Large Images Detected',
                'description' => count($large_images) . ' images are larger than 500KB: ' . implode(', ', $large_images),
                'details' => 'Large images can slow down your page loading speed. Consider optimizing these images to improve performance.',
                'fixable' => true
            );
        }
    }

    /**
     * Check schema markup
     */
    private function check_schema_markup(&$results) {
        // Get homepage content
        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            // Check for schema markup
            $has_schema = strpos($homepage_content, 'application/ld+json') !== false ||
                          strpos($homepage_content, 'itemscope') !== false ||
                          strpos($homepage_content, 'itemtype') !== false;

            if (!$has_schema) {
                $results['warning'][] = array(
                    'id' => 'missing_schema',
                    'type' => 'schema',
                    'title' => 'Missing Schema Markup',
                    'description' => 'Your homepage does not have any schema markup.',
                    'details' => 'Schema markup helps search engines understand your content better and can result in rich snippets in search results.',
                    'fixable' => true
                );
            }
        }
    }

    /**
     * Check social media integration
     */
    private function check_social_media(&$results) {
        // Get homepage content
        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            // Check for Open Graph tags
            $has_og = strpos($homepage_content, 'property="og:') !== false ||
                      strpos($homepage_content, 'property=\'og:') !== false;

            if (!$has_og) {
                $results['warning'][] = array(
                    'id' => 'missing_open_graph',
                    'type' => 'social',
                    'title' => 'Missing Open Graph Tags',
                    'description' => 'Your homepage does not have Open Graph tags for social media sharing.',
                    'details' => 'Open Graph tags control how your content appears when shared on social media platforms like Facebook.',
                    'fixable' => true
                );
            }

            // Check for Twitter Card tags
            $has_twitter = strpos($homepage_content, 'name="twitter:') !== false ||
                           strpos($homepage_content, 'name=\'twitter:') !== false;

            if (!$has_twitter) {
                $results['warning'][] = array(
                    'id' => 'missing_twitter_cards',
                    'type' => 'social',
                    'title' => 'Missing Twitter Card Tags',
                    'description' => 'Your homepage does not have Twitter Card tags for Twitter sharing.',
                    'details' => 'Twitter Card tags control how your content appears when shared on Twitter.',
                    'fixable' => true
                );
            }
        }
    }

    /**
     * Check SSL configuration
     */
    private function check_ssl(&$results) {
        // Check if site is using HTTPS
        $is_ssl = is_ssl();

        if (!$is_ssl) {
            $results['critical'][] = array(
                'id' => 'ssl_missing',
                'type' => 'ssl',
                'title' => 'Missing SSL Certificate',
                'description' => 'Your site is not using HTTPS. This is a critical SEO and security issue.',
                'details' => 'Search engines prefer secure websites. Not having SSL may negatively impact your rankings and show insecure warnings to visitors.',
                'fixable' => false
            );
        }
    }

    /**
     * Check sitemap configuration
     */
    private function check_sitemap(&$results) {
        $home_url = home_url();
        $sitemap_url = $home_url . '/sitemap.xml';

        // Try to fetch sitemap
        $response = wp_remote_get($sitemap_url, array('timeout' => 5));
        $sitemap_exists = !is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200;

        if (!$sitemap_exists) {
            $results['warning'][] = array(
                'id' => 'sitemap_missing',
                'type' => 'sitemap',
                'title' => 'XML Sitemap Missing',
                'description' => 'No XML sitemap detected. A sitemap helps search engines discover and index your content.',
                'details' => 'XML sitemaps are essential for SEO as they help search engines find and index your content more efficiently.',
                'fixable' => true
            );
        }
    }

    /**
     * Check robots.txt configuration
     */
    private function check_robots_txt(&$results) {
        $home_url = home_url();
        $robots_url = $home_url . '/robots.txt';

        // Try to fetch robots.txt
        $response = wp_remote_get($robots_url, array('timeout' => 5));

        if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
            $results['warning'][] = array(
                'id' => 'robots_txt_missing',
                'type' => 'robots_txt',
                'title' => 'Robots.txt Missing',
                'description' => 'No robots.txt file found or it\'s not accessible.',
                'details' => 'A robots.txt file helps control how search engines crawl your site. Without it, search engines may index pages you don\'t want indexed.',
                'fixable' => true
            );
        } else {
            // Check robots.txt content
            $robots_content = wp_remote_retrieve_body($response);

            // Check if sitemap is referenced in robots.txt
            if (strpos($robots_content, 'Sitemap:') === false) {
                $results['info'][] = array(
                    'id' => 'robots_txt_no_sitemap',
                    'type' => 'robots_txt',
                    'title' => 'Sitemap Not Referenced in Robots.txt',
                    'description' => 'Your robots.txt file does not reference your XML sitemap.',
                    'details' => 'Adding a Sitemap directive to your robots.txt helps search engines find your sitemap more easily.',
                    'fixable' => true
                );
            }
        }
    }

    /**
     * Check meta tags
     */
    private function check_meta_tags(&$results) {
        $home_url = home_url();

        // Fetch homepage content
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            // Check title
            preg_match('/<title>(.*?)<\/title>/i', $homepage_content, $title_matches);

            if (empty($title_matches)) {
                $results['critical'][] = array(
                    'id' => 'title_missing',
                    'type' => 'meta',
                    'title' => 'Title Tag Missing',
                    'description' => 'Your homepage does not have a title tag.',
                    'details' => 'The title tag is one of the most important SEO elements. It tells search engines what your page is about.',
                    'fixable' => true
                );
            } else {
                $title = $title_matches[1];
                $title_length = mb_strlen($title);

                if ($title_length < 30) {
                    $results['warning'][] = array(
                        'id' => 'title_too_short',
                        'type' => 'meta',
                        'title' => 'Title Too Short',
                        'description' => "Your homepage title is only $title_length characters. Recommended minimum is 30 characters.",
                        'details' => 'Short titles may not provide enough information for search engines and users.',
                        'fixable' => true
                    );
                } elseif ($title_length > 60) {
                    $results['warning'][] = array(
                        'id' => 'title_too_long',
                        'type' => 'meta',
                        'title' => 'Title Too Long',
                        'description' => "Your homepage title is $title_length characters. Recommended maximum is 60 characters.",
                        'details' => 'Long titles may be truncated in search results.',
                        'fixable' => true
                    );
                }
            }

            // Check meta description
            preg_match('/<meta name="description" content="(.*?)"/i', $homepage_content, $desc_matches);

            if (empty($desc_matches)) {
                $results['warning'][] = array(
                    'id' => 'meta_description_missing',
                    'type' => 'meta',
                    'title' => 'Meta Description Missing',
                    'description' => 'Your homepage does not have a meta description.',
                    'details' => 'Meta descriptions provide a summary of your page for search engine results.',
                    'fixable' => true
                );
            } else {
                $description = $desc_matches[1];
                $desc_length = mb_strlen($description);

                if ($desc_length < 120) {
                    $results['info'][] = array(
                        'id' => 'meta_description_too_short',
                        'type' => 'meta',
                        'title' => 'Meta Description Too Short',
                        'description' => "Your meta description is only $desc_length characters. Recommended minimum is 120 characters.",
                        'details' => 'Short meta descriptions may not provide enough information for users to click on your result.',
                        'fixable' => true
                    );
                } elseif ($desc_length > 160) {
                    $results['info'][] = array(
                        'id' => 'meta_description_too_long',
                        'type' => 'meta',
                        'title' => 'Meta Description Too Long',
                        'description' => "Your meta description is $desc_length characters. Recommended maximum is 160 characters.",
                        'details' => 'Long meta descriptions may be truncated in search results.',
                        'fixable' => true
                    );
                }
            }
        }
    }

    /**
     * Check content
     */
    private function check_content(&$results) {
        // Get recent posts
        $recent_posts = get_posts(array(
            'post_type' => 'post',
            'posts_per_page' => 5,
            'post_status' => 'publish'
        ));

        if (empty($recent_posts)) {
            return;
        }

        $posts_without_images = array();
        $posts_with_short_content = array();

        foreach ($recent_posts as $post) {
            // Check for featured image
            if (!has_post_thumbnail($post->ID)) {
                $posts_without_images[] = $post->post_title;
            }

            // Check content length
            $content = strip_tags($post->post_content);
            $word_count = str_word_count($content);

            if ($word_count < 300) {
                $posts_with_short_content[] = array(
                    'title' => $post->post_title,
                    'word_count' => $word_count
                );
            }
        }

        // Add issues for posts without images
        if (!empty($posts_without_images)) {
            $results['info'][] = array(
                'id' => 'posts_without_images',
                'type' => 'content',
                'title' => 'Posts Without Featured Images',
                'description' => count($posts_without_images) . ' recent posts do not have featured images: ' . implode(', ', $posts_without_images),
                'details' => 'Featured images are important for social sharing and visual appeal.',
                'fixable' => false
            );
        }

        // Add issues for posts with short content
        if (!empty($posts_with_short_content)) {
            $post_list = array();
            foreach ($posts_with_short_content as $post) {
                $post_list[] = $post['title'] . ' (' . $post['word_count'] . ' words)';
            }

            $results['warning'][] = array(
                'id' => 'posts_with_short_content',
                'type' => 'content',
                'title' => 'Posts With Short Content',
                'description' => count($posts_with_short_content) . ' recent posts have less than 300 words: ' . implode(', ', $post_list),
                'details' => 'Longer content tends to rank better in search engines. Aim for at least 300 words for most posts.',
                'fixable' => false
            );
        }
    }

    /**
     * Check mobile friendliness
     */
    private function check_mobile(&$results) {
        $home_url = home_url();

        // Fetch homepage content
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            // Check viewport meta tag
            if (strpos($homepage_content, 'viewport') === false) {
                $results['critical'][] = array(
                    'id' => 'viewport_meta_missing',
                    'type' => 'mobile',
                    'title' => 'Viewport Meta Tag Missing',
                    'description' => 'Your site is missing the viewport meta tag, which is essential for mobile responsiveness.',
                    'details' => 'The viewport meta tag tells browsers how to adjust the page dimensions and scaling to suit the device.',
                    'fixable' => true
                );
            }
        }
    }

    /**
     * Fix an issue
     */
    private function fix_issue($issue_id, $issue_type) {
        switch ($issue_id) {
            case 'sitemap_missing':
            case 'sitemap_missing_live':
                return $this->fix_sitemap();

            case 'robots_txt_missing':
            case 'robots_txt_missing_live':
                return $this->fix_robots_txt();

            case 'robots_txt_no_sitemap':
                return $this->fix_robots_txt_sitemap();

            case 'title_missing':
            case 'title_too_short':
            case 'title_too_long':
                return $this->fix_title();

            case 'meta_description_missing':
            case 'meta_description_too_short':
            case 'meta_description_too_long':
            case 'description_missing':
            case 'description_too_short':
            case 'description_too_long':
                return $this->fix_meta_description();

            case 'viewport_meta_missing':
            case 'viewport_missing_live':
                return $this->fix_viewport_meta();

            case 'canonical_missing':
                return $this->fix_canonical_tag();

            case 'images_missing_alt_live':
            case 'images_without_alt':
                return $this->fix_image_alt_text();

            case 'h1_missing_live':
            case 'h1_multiple_live':
                return $this->fix_h1_tags();

            case 'schema_missing_live':
            case 'missing_schema':
                return $this->fix_schema_markup();

            case 'open_graph_missing_live':
            case 'missing_open_graph':
                return $this->fix_open_graph();

            case 'robots_meta_missing':
                return $this->fix_robots_meta();

            case 'ssl_missing_live':
            case 'ssl_missing':
                return $this->fix_ssl();

            case 'internal_links_missing_live':
                return $this->fix_internal_links();

            case 'headings_missing_live':
                return $this->fix_headings_structure();

            default:
                return array(
                    'success' => false,
                    'message' => 'Unknown issue ID: ' . $issue_id
                );
        }
    }

    /**
     * Fix sitemap
     */
    private function fix_sitemap() {
        // In a real implementation, this would generate a sitemap
        // For this simple version, we'll just return instructions

        return array(
            'success' => true,
            'message' => 'Sitemap fix instructions provided.',
            'details' => 'To create a sitemap:<br>1. Install an SEO plugin like Yoast SEO or Rank Math<br>2. Enable the XML sitemap feature in the plugin settings<br>3. The sitemap will be automatically generated'
        );
    }

    /**
     * Fix robots.txt
     */
    private function fix_robots_txt() {
        // In a real implementation, this would create a robots.txt file
        // For this simple version, we'll just return instructions

        return array(
            'success' => true,
            'message' => 'Robots.txt fix instructions provided.',
            'details' => 'To create a robots.txt file:<br>1. Create a new file named robots.txt in your site\'s root directory<br>2. Add the following content:<br><pre>User-agent: *<br>Disallow: /wp-admin/<br>Disallow: /wp-includes/<br>Allow: /wp-admin/admin-ajax.php<br><br>Sitemap: ' . home_url('/sitemap.xml') . '</pre>'
        );
    }

    /**
     * Fix robots.txt sitemap reference
     */
    private function fix_robots_txt_sitemap() {
        // In a real implementation, this would update the robots.txt file
        // For this simple version, we'll just return instructions

        return array(
            'success' => true,
            'message' => 'Robots.txt sitemap reference fix instructions provided.',
            'details' => 'To add a sitemap reference to your robots.txt file:<br>1. Edit your robots.txt file<br>2. Add the following line at the end:<br><pre>Sitemap: ' . home_url('/sitemap.xml') . '</pre>'
        );
    }

    /**
     * Fix title
     */
    private function fix_title() {
        // In a real implementation, this would update the title tag
        // For this simple version, we'll just return instructions

        $site_name = get_bloginfo('name');
        $site_description = get_bloginfo('description');

        $new_title = $site_name;
        if (!empty($site_description)) {
            $new_title .= ' - ' . $site_description;
        }

        return array(
            'success' => true,
            'message' => 'Title fix instructions provided.',
            'details' => 'To update your homepage title:<br>1. If using an SEO plugin, edit the homepage title in the plugin settings<br>2. If not using an SEO plugin, edit your theme\'s header.php file and update the title tag<br><br>Recommended title: <strong>' . esc_html($new_title) . '</strong>'
        );
    }

    /**
     * Fix meta description
     */
    private function fix_meta_description() {
        // In a real implementation, this would update the meta description
        // For this simple version, we'll just return instructions

        $site_name = get_bloginfo('name');
        $site_description = get_bloginfo('description');

        $new_description = 'Welcome to ' . $site_name . '. ';
        if (!empty($site_description)) {
            $new_description .= $site_description . '. ';
        }
        $new_description .= 'We provide valuable information and resources to help you succeed. Visit our site to learn more about our products and services.';

        return array(
            'success' => true,
            'message' => 'Meta description fix instructions provided.',
            'details' => 'To update your homepage meta description:<br>1. If using an SEO plugin, edit the homepage meta description in the plugin settings<br>2. If not using an SEO plugin, edit your theme\'s header.php file and add a meta description tag<br><br>Recommended meta description: <strong>' . esc_html($new_description) . '</strong>'
        );
    }

    /**
     * Fix viewport meta - REAL LIVE FIX
     */
    private function fix_viewport_meta() {
        // ACTUALLY ADD VIEWPORT META TAG TO WORDPRESS
        add_action('wp_head', function() {
            echo '<meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />' . "\n";
        }, 0); // Priority 0 to ensure it's added early

        // SAVE THIS FIX PERMANENTLY
        $current_fixes = get_option('wp_hss_active_fixes', array());
        $current_fixes['viewport_meta'] = true;
        update_option('wp_hss_active_fixes', $current_fixes);

        return array(
            'success' => true,
            'message' => 'VIEWPORT META TAG ACTUALLY ADDED TO YOUR WEBSITE!',
            'details' => '✅ <strong>REAL FIX APPLIED:</strong> Viewport meta tag is now automatically added to all pages. Your website is now mobile-responsive and will display correctly on all devices!'
        );
    }

    /**
     * Fix canonical tag - REAL LIVE FIX
     */
    private function fix_canonical_tag() {
        // ACTUALLY ADD CANONICAL TAG TO WORDPRESS
        add_action('wp_head', function() {
            if (is_singular()) {
                echo '<link rel="canonical" href="' . get_permalink() . '" />' . "\n";
            } elseif (is_home() || is_front_page()) {
                echo '<link rel="canonical" href="' . home_url('/') . '" />' . "\n";
            } elseif (is_category()) {
                echo '<link rel="canonical" href="' . get_category_link(get_queried_object_id()) . '" />' . "\n";
            } elseif (is_tag()) {
                echo '<link rel="canonical" href="' . get_tag_link(get_queried_object_id()) . '" />' . "\n";
            } elseif (is_author()) {
                echo '<link rel="canonical" href="' . get_author_posts_url(get_queried_object_id()) . '" />' . "\n";
            }
        }, 1);

        // SAVE THIS FIX PERMANENTLY
        $current_fixes = get_option('wp_hss_active_fixes', array());
        $current_fixes['canonical_tag'] = true;
        update_option('wp_hss_active_fixes', $current_fixes);

        return array(
            'success' => true,
            'message' => 'CANONICAL TAG ACTUALLY ADDED TO YOUR WEBSITE!',
            'details' => '✅ <strong>REAL FIX APPLIED:</strong> Canonical tags are now automatically added to all pages on your website. This fix is LIVE and ACTIVE right now!'
        );
    }

    /**
     * Fix image alt text - REAL LIVE FIX
     */
    private function fix_image_alt_text() {
        // ACTUALLY FIX MISSING ALT TEXT ON IMAGES
        $fixed_count = 0;

        // Get all attachments (images) without alt text
        $attachments = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'posts_per_page' => -1,
            'post_status' => 'inherit'
        ));

        foreach ($attachments as $attachment) {
            $alt_text = get_post_meta($attachment->ID, '_wp_attachment_image_alt', true);

            if (empty($alt_text)) {
                // AUTOMATICALLY GENERATE ALT TEXT FROM FILENAME/TITLE
                $filename = basename(get_attached_file($attachment->ID));
                $title = $attachment->post_title;

                // Create meaningful alt text
                $auto_alt = !empty($title) ? $title : str_replace(array('-', '_', '.jpg', '.jpeg', '.png', '.gif', '.webp'), ' ', $filename);
                $auto_alt = ucwords(trim($auto_alt));

                // ACTUALLY UPDATE THE ALT TEXT
                update_post_meta($attachment->ID, '_wp_attachment_image_alt', $auto_alt);
                $fixed_count++;
            }
        }

        // SAVE THIS FIX PERMANENTLY
        $current_fixes = get_option('wp_hss_active_fixes', array());
        $current_fixes['image_alt_text'] = true;
        update_option('wp_hss_active_fixes', $current_fixes);

        return array(
            'success' => true,
            'message' => "ACTUALLY FIXED $fixed_count IMAGES WITH MISSING ALT TEXT!",
            'details' => "✅ <strong>REAL FIX APPLIED:</strong> Automatically added alt text to $fixed_count images. All images on your website now have proper alt text for SEO and accessibility!"
        );
    }

    /**
     * Fix H1 tags
     */
    private function fix_h1_tags() {
        return array(
            'success' => true,
            'message' => 'H1 tag fix instructions provided.',
            'details' => 'To fix H1 tag issues:<br>1. <strong>Missing H1:</strong> Add an H1 tag to your page/post title<br>2. <strong>Multiple H1s:</strong> Change extra H1 tags to H2, H3, etc.<br>3. Edit your theme\'s templates (header.php, index.php) to ensure proper heading structure<br><br><strong>Best practice:</strong> Use only one H1 per page, typically for the main title.'
        );
    }

    /**
     * Fix schema markup
     */
    private function fix_schema_markup() {
        return array(
            'success' => true,
            'message' => 'Schema markup fix instructions provided.',
            'details' => 'To add schema markup:<br>1. <strong>Easy way:</strong> Install an SEO plugin like Yoast, Rank Math, or Schema Pro<br>2. <strong>Manual way:</strong> Add JSON-LD schema to your theme\'s header.php<br>3. Use Google\'s Structured Data Markup Helper for guidance<br><br><strong>Common schemas:</strong> Organization, Website, Article, Product, Local Business'
        );
    }

    /**
     * Fix Open Graph tags
     */
    private function fix_open_graph() {
        return array(
            'success' => true,
            'message' => 'Open Graph tags fix instructions provided.',
            'details' => 'To add Open Graph tags:<br>1. <strong>Easy way:</strong> Install an SEO plugin like Yoast or Rank Math<br>2. <strong>Manual way:</strong> Add these meta tags to your theme\'s header.php:<br><pre>&lt;meta property="og:title" content="&lt;?php wp_title(); ?&gt;" /&gt;<br>&lt;meta property="og:description" content="Your page description" /&gt;<br>&lt;meta property="og:image" content="Your image URL" /&gt;<br>&lt;meta property="og:url" content="&lt;?php echo get_permalink(); ?&gt;" /&gt;</pre>'
        );
    }

    /**
     * Fix robots meta tag - REAL LIVE FIX
     */
    private function fix_robots_meta() {
        // ACTUALLY ADD ROBOTS META TAG TO WORDPRESS
        add_action('wp_head', function() {
            if (is_admin() || is_feed() || is_robots() || is_trackback()) {
                return;
            }

            // Default robots directive
            $robots_content = 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1';

            // Special cases
            if (is_search() || is_404()) {
                $robots_content = 'noindex, follow';
            } elseif (is_attachment()) {
                $robots_content = 'noindex, follow';
            }

            echo '<meta name="robots" content="' . esc_attr($robots_content) . '" />' . "\n";
        }, 1);

        // SAVE THIS FIX PERMANENTLY
        $current_fixes = get_option('wp_hss_active_fixes', array());
        $current_fixes['robots_meta'] = true;
        update_option('wp_hss_active_fixes', $current_fixes);

        return array(
            'success' => true,
            'message' => 'ROBOTS META TAGS ACTUALLY ADDED TO YOUR WEBSITE!',
            'details' => '✅ <strong>REAL FIX APPLIED:</strong> Robots meta tags are now automatically added to all pages with proper directives. Search engines now have clear instructions for crawling your site!'
        );
    }

    /**
     * Fix SSL certificate
     */
    private function fix_ssl() {
        return array(
            'success' => true,
            'message' => 'SSL certificate fix instructions provided.',
            'details' => 'To enable SSL/HTTPS:<br>1. <strong>Get SSL certificate:</strong> Contact your hosting provider or use Let\'s Encrypt (free)<br>2. <strong>Install certificate:</strong> Your host usually handles this<br>3. <strong>Update WordPress:</strong> Change Site URL and Home URL to https:// in Settings > General<br>4. <strong>Force HTTPS:</strong> Add redirect rules or use a plugin like Really Simple SSL<br><br><strong>Important:</strong> Backup your site before making changes!'
        );
    }

    /**
     * Fix internal links
     */
    private function fix_internal_links() {
        return array(
            'success' => true,
            'message' => 'Internal links fix instructions provided.',
            'details' => 'To add internal links:<br>1. <strong>In content:</strong> Link to related posts/pages using relevant anchor text<br>2. <strong>Navigation menu:</strong> Add important pages to your main menu<br>3. <strong>Sidebar/footer:</strong> Add "Related Posts" or "Popular Posts" widgets<br>4. <strong>Breadcrumbs:</strong> Install a breadcrumb plugin<br><br><strong>Best practice:</strong> Use descriptive anchor text, not "click here"'
        );
    }

    /**
     * Fix headings structure
     */
    private function fix_headings_structure() {
        return array(
            'success' => true,
            'message' => 'Headings structure fix instructions provided.',
            'details' => 'To improve heading structure:<br>1. <strong>Add headings:</strong> Use H1 for main title, H2 for sections, H3 for subsections<br>2. <strong>Logical order:</strong> Don\'t skip levels (H1 → H2 → H3, not H1 → H3)<br>3. <strong>Edit content:</strong> Add headings to break up long text blocks<br>4. <strong>Theme templates:</strong> Ensure proper heading hierarchy in your theme<br><br><strong>Benefits:</strong> Better accessibility, improved SEO, easier reading'
        );
    }

    /**
     * Render dashboard page
     */
    public function render_dashboard_page() {
        // Get cached health score and issue counts for better performance
        $health_score = $this->get_cached_health_score();
        $issue_counts = $this->get_cached_issue_counts();

        echo '<div class="wrap">';
        echo '<h1>WP Health & SEO Sentinel</h1>';
        echo '<p>Welcome to the SEO Sentinel dashboard. This plugin helps you analyze and optimize your site for better SEO performance.</p>';

        // SEO Health Stats
        echo '<div class="card">';
        echo '<h2>SEO Health Overview</h2>';

        echo '<div class="wp-hss-stats-grid">';

        // Health Score
        echo '<div class="wp-hss-stat-card">';
        echo '<div class="wp-hss-stat-label">SEO Health Score</div>';
        echo '<div id="wp-hss-health-score" class="wp-hss-stat-value" style="color: ' . ($health_score >= 80 ? '#00a32a' : ($health_score >= 50 ? '#dba617' : '#d63638')) . ';">' . $health_score . '</div>';
        echo '<div class="wp-hss-stat-description">' . ($health_score >= 80 ? 'Good' : ($health_score >= 50 ? 'Needs Improvement' : 'Poor')) . '</div>';
        echo '</div>';

        // Critical Issues
        echo '<div class="wp-hss-stat-card">';
        echo '<div class="wp-hss-stat-label">Critical Issues</div>';
        echo '<div id="wp-hss-critical-count" class="wp-hss-stat-value" style="color: #d63638;">' . $issue_counts['critical'] . '</div>';
        echo '<div class="wp-hss-stat-description">Need immediate attention</div>';
        echo '</div>';

        // Warnings
        echo '<div class="wp-hss-stat-card">';
        echo '<div class="wp-hss-stat-label">Warnings</div>';
        echo '<div id="wp-hss-warning-count" class="wp-hss-stat-value" style="color: #dba617;">' . $issue_counts['warning'] . '</div>';
        echo '<div class="wp-hss-stat-description">Should be addressed</div>';
        echo '</div>';

        // Suggestions
        echo '<div class="wp-hss-stat-card">';
        echo '<div class="wp-hss-stat-label">Suggestions</div>';
        echo '<div id="wp-hss-info-count" class="wp-hss-stat-value" style="color: #2271b1;">' . $issue_counts['info'] . '</div>';
        echo '<div class="wp-hss-stat-description">Opportunities to improve</div>';
        echo '</div>';

        echo '</div>'; // End stats grid

        echo '</div>'; // End card

        // Quick Actions
        echo '<div class="card">';
        echo '<h2>Quick Actions</h2>';
        echo '<p><a href="' . admin_url('admin.php?page=wp-hss-scanner') . '" class="button button-primary">Run SEO Scan</a></p>';
        echo '</div>';

        // Recent Content
        $recent_posts = get_posts(array(
            'post_type' => 'post',
            'posts_per_page' => 5,
            'post_status' => 'publish'
        ));

        if (!empty($recent_posts)) {
            echo '<div class="card">';
            echo '<h2>Recent Content</h2>';
            echo '<table class="widefat">';
            echo '<thead>';
            echo '<tr>';
            echo '<th>Title</th>';
            echo '<th>Date</th>';
            echo '<th>Actions</th>';
            echo '</tr>';
            echo '</thead>';
            echo '<tbody>';

            foreach ($recent_posts as $post) {
                echo '<tr>';
                echo '<td><a href="' . get_permalink($post->ID) . '" target="_blank">' . esc_html($post->post_title) . '</a></td>';
                echo '<td>' . get_the_date('M j, Y', $post->ID) . '</td>';
                echo '<td><a href="' . admin_url('post.php?post=' . $post->ID . '&action=edit') . '" class="button button-small">Edit</a></td>';
                echo '</tr>';
            }

            echo '</tbody>';
            echo '</table>';
            echo '</div>';
        }

        // SEO Tips
        echo '<div class="card">';
        echo '<h2>SEO Tips</h2>';
        echo '<ul>';
        echo '<li><strong>Use descriptive titles:</strong> Each page should have a unique, descriptive title that includes your main keyword.</li>';
        echo '<li><strong>Optimize meta descriptions:</strong> Write compelling meta descriptions that encourage clicks from search results.</li>';
        echo '<li><strong>Use header tags properly:</strong> Structure your content with H1, H2, H3 tags to help search engines understand your content hierarchy.</li>';
        echo '<li><strong>Optimize images:</strong> Use descriptive filenames and alt text for all images.</li>';
        echo '<li><strong>Create quality content:</strong> Publish original, valuable content that addresses your audience\'s needs.</li>';
        echo '</ul>';
        echo '</div>';

        echo '</div>'; // End wrap
    }

    /**
     * Get health score
     */
    private function get_health_score() {
        // Perform actual scan to calculate real health score
        $results = $this->scan_site();

        $critical_count = count($results['critical']);
        $warning_count = count($results['warning']);
        $info_count = count($results['info']);

        // Calculate health score based on actual issues
        $health_score = 100;
        $health_score -= $critical_count * 15; // Critical issues have high impact
        $health_score -= $warning_count * 8;   // Warning issues have medium impact
        $health_score -= $info_count * 3;      // Info issues have low impact

        // Ensure score is between 0 and 100
        $health_score = max(0, min(100, $health_score));

        // Store the score for caching
        update_option('wp_hss_health_score', $health_score);
        update_option('wp_hss_last_scan', current_time('timestamp'));

        return $health_score;
    }

    /**
     * Get issue counts
     */
    private function get_issue_counts() {
        // Perform actual scan to get real issue counts
        $results = $this->scan_site();

        $critical = count($results['critical']);
        $warning = count($results['warning']);
        $info = count($results['info']);

        // Store the counts for caching
        update_option('wp_hss_issue_counts', array(
            'critical' => $critical,
            'warning' => $warning,
            'info' => $info,
            'total' => $critical + $warning + $info
        ));

        return array(
            'critical' => $critical,
            'warning' => $warning,
            'info' => $info,
            'total' => $critical + $warning + $info
        );
    }

    /**
     * Get cached health score if recent, otherwise calculate new one
     */
    private function get_cached_health_score() {
        $last_scan = get_option('wp_hss_last_scan', 0);
        $cache_duration = 3600; // 1 hour cache

        if ((current_time('timestamp') - $last_scan) < $cache_duration) {
            $cached_score = get_option('wp_hss_health_score', false);
            if ($cached_score !== false) {
                return $cached_score;
            }
        }

        return $this->get_health_score();
    }

    /**
     * Get cached issue counts if recent, otherwise calculate new ones
     */
    private function get_cached_issue_counts() {
        $last_scan = get_option('wp_hss_last_scan', 0);
        $cache_duration = 3600; // 1 hour cache

        if ((current_time('timestamp') - $last_scan) < $cache_duration) {
            $cached_counts = get_option('wp_hss_issue_counts', false);
            if ($cached_counts !== false) {
                return $cached_counts;
            }
        }

        return $this->get_issue_counts();
    }

    /**
     * Render scanner page
     */
    public function render_scanner_page() {
        // Get cached health score and issue counts for better performance
        $health_score = $this->get_cached_health_score();
        $issue_counts = $this->get_cached_issue_counts();

        echo '<div class="wrap">';
        echo '<h1>SEO Scanner</h1>';
        echo '<p>Use this tool to scan your site for SEO issues and get recommendations for improvement.</p>';

        // SEO Health Stats
        echo '<div class="card">';
        echo '<h2>Current SEO Health</h2>';

        echo '<div class="wp-hss-stats-grid">';

        // Health Score
        echo '<div class="wp-hss-stat-card">';
        echo '<div class="wp-hss-stat-label">SEO Health Score</div>';
        echo '<div id="wp-hss-health-score" class="wp-hss-stat-value" style="color: ' . ($health_score >= 80 ? '#00a32a' : ($health_score >= 50 ? '#dba617' : '#d63638')) . ';">' . $health_score . '</div>';
        echo '</div>';

        // Critical Issues
        echo '<div class="wp-hss-stat-card">';
        echo '<div class="wp-hss-stat-label">Critical Issues</div>';
        echo '<div id="wp-hss-critical-count" class="wp-hss-stat-value" style="color: #d63638;">' . $issue_counts['critical'] . '</div>';
        echo '</div>';

        // Warnings
        echo '<div class="wp-hss-stat-card">';
        echo '<div class="wp-hss-stat-label">Warnings</div>';
        echo '<div id="wp-hss-warning-count" class="wp-hss-stat-value" style="color: #dba617;">' . $issue_counts['warning'] . '</div>';
        echo '</div>';

        // Suggestions
        echo '<div class="wp-hss-stat-card">';
        echo '<div class="wp-hss-stat-label">Suggestions</div>';
        echo '<div id="wp-hss-info-count" class="wp-hss-stat-value" style="color: #2271b1;">' . $issue_counts['info'] . '</div>';
        echo '</div>';

        echo '</div>'; // End stats grid

        echo '</div>'; // End card

        // Scan Options
        echo '<div class="card">';
        echo '<h2>Scan Options</h2>';

        echo '<div style="margin-bottom: 20px;">';
        echo '<p>Select what to scan:</p>';

        echo '<div class="wp-hss-scan-options-grid">';

        // Technical SEO
        echo '<div class="wp-hss-scan-option-group">';
        echo '<h3>Technical SEO</h3>';
        echo '<label><input type="checkbox" name="scan_option" value="technical" checked> Technical SEO</label>';
        echo '<p class="description">Checks SSL, sitemap, robots.txt, permalink structure, and broken links.</p>';
        echo '</div>';

        // Meta Tags
        echo '<div class="wp-hss-scan-option-group">';
        echo '<h3>Meta Tags</h3>';
        echo '<label><input type="checkbox" name="scan_option" value="meta" checked> Meta Tags</label>';
        echo '<p class="description">Analyzes title tags, meta descriptions, and heading structure.</p>';
        echo '</div>';

        // Content
        echo '<div class="wp-hss-scan-option-group">';
        echo '<h3>Content</h3>';
        echo '<label><input type="checkbox" name="scan_option" value="content" checked> Content</label>';
        echo '<p class="description">Evaluates content quality, keyword usage, and readability.</p>';
        echo '</div>';

        // Mobile Friendliness
        echo '<div class="wp-hss-scan-option-group">';
        echo '<h3>Mobile Friendliness</h3>';
        echo '<label><input type="checkbox" name="scan_option" value="mobile" checked> Mobile Friendliness</label>';
        echo '<p class="description">Checks viewport meta tag and mobile responsiveness.</p>';
        echo '</div>';

        // Performance
        echo '<div class="wp-hss-scan-option-group">';
        echo '<h3>Performance</h3>';
        echo '<label><input type="checkbox" name="scan_option" value="performance" checked> Performance</label>';
        echo '<p class="description">Analyzes page speed and image optimization.</p>';
        echo '</div>';

        // Schema Markup
        echo '<div class="wp-hss-scan-option-group">';
        echo '<h3>Schema Markup</h3>';
        echo '<label><input type="checkbox" name="scan_option" value="schema" checked> Schema Markup</label>';
        echo '<p class="description">Checks for structured data implementation.</p>';
        echo '</div>';

        // Social Media
        echo '<div class="wp-hss-scan-option-group">';
        echo '<h3>Social Media</h3>';
        echo '<label><input type="checkbox" name="scan_option" value="social" checked> Social Media</label>';
        echo '<p class="description">Verifies Open Graph and Twitter Card tags.</p>';
        echo '</div>';

        echo '</div>'; // End scan options grid

        echo '<div style="margin-top: 20px;">';
        echo '<label><input type="checkbox" id="select-all-options" checked> Select/Deselect All</label>';
        echo '</div>';

        echo '</div>'; // End margin div

        // Scan Depth
        echo '<div style="margin-bottom: 20px;">';
        echo '<h3>Scan Depth</h3>';
        echo '<select id="scan-depth" class="regular-text">';
        echo '<option value="quick">Quick Scan (Homepage Only)</option>';
        echo '<option value="standard" selected>Standard Scan (Main Pages)</option>';
        echo '<option value="deep">Deep Scan (All Pages)</option>';
        echo '</select>';
        echo '<p class="description">Deeper scans are more thorough but take longer to complete.</p>';
        echo '</div>';

        echo '<p><button id="wp-hss-scan-button" class="button button-primary button-hero">Start Comprehensive Scan</button></p>';
        echo '<p class="description">A comprehensive scan may take a few moments to complete.</p>';
        echo '</div>';

        // What We Check
        echo '<div class="card">';
        echo '<h2>What We Check</h2>';

        echo '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px;">';

        // Meta Tags
        echo '<div>';
        echo '<h3>Meta Tags</h3>';
        echo '<ul>';
        echo '<li>Title tags (length, uniqueness, keywords)</li>';
        echo '<li>Meta descriptions (length, quality)</li>';
        echo '<li>Heading structure (H1, H2, H3 usage)</li>';
        echo '<li>Image alt text</li>';
        echo '</ul>';
        echo '</div>';

        // Content
        echo '<div>';
        echo '<h3>Content</h3>';
        echo '<ul>';
        echo '<li>Content length and quality</li>';
        echo '<li>Keyword usage and density</li>';
        echo '<li>Internal linking structure</li>';
        echo '<li>Readability and formatting</li>';
        echo '</ul>';
        echo '</div>';

        // Technical SEO
        echo '<div>';
        echo '<h3>Technical SEO</h3>';
        echo '<ul>';
        echo '<li>SSL configuration</li>';
        echo '<li>XML sitemap</li>';
        echo '<li>Robots.txt file</li>';
        echo '<li>URL structure</li>';
        echo '</ul>';
        echo '</div>';

        // Mobile Friendliness
        echo '<div>';
        echo '<h3>Mobile Friendliness</h3>';
        echo '<ul>';
        echo '<li>Viewport meta tag</li>';
        echo '<li>Responsive design elements</li>';
        echo '<li>Touch elements sizing</li>';
        echo '<li>Mobile-friendly content</li>';
        echo '</ul>';
        echo '</div>';

        echo '</div>'; // End grid

        echo '</div>'; // End card

        // Scan Results
        echo '<div id="wp-hss-scan-results"></div>';

        echo '</div>'; // End wrap
    }

    /**
     * Render tools page
     */
    public function render_tools_page() {
        echo '<div class="wrap">';
        echo '<h1>SEO Tools</h1>';
        echo '<p>Advanced tools for optimizing your website SEO</p>';

        // Tools Grid
        echo '<div class="wp-hss-tools-grid">';

        // Bulk Title Optimizer
        echo '<div class="card">';
        echo '<h2>Bulk Title Optimizer</h2>';
        echo '<p>Optimize all your page titles at once using AI-powered suggestions.</p>';
        echo '<p>This tool will analyze your content and generate SEO-friendly titles that are the perfect length and include relevant keywords.</p>';
        echo '<p><button class="button button-primary wp-hss-tool-button" data-tool="bulk_title_optimizer">Open Tool</button></p>';
        echo '</div>';

        // Meta Description Generator
        echo '<div class="card">';
        echo '<h2>Meta Description Generator</h2>';
        echo '<p>Generate optimized meta descriptions for your content.</p>';
        echo '<p>Create compelling meta descriptions that encourage clicks while staying within the recommended length.</p>';
        echo '<p><button class="button button-primary wp-hss-tool-button" data-tool="meta_description_generator">Open Tool</button></p>';
        echo '</div>';

        // Schema Markup Generator
        echo '<div class="card">';
        echo '<h2>Schema Markup Generator</h2>';
        echo '<p>Create and validate schema markup for your pages to improve rich snippets.</p>';
        echo '<p>Generate structured data for various content types to help search engines better understand your content.</p>';
        echo '<p><button class="button button-primary wp-hss-tool-button" data-tool="schema_markup_generator">Open Tool</button></p>';
        echo '</div>';

        // Content Optimizer
        echo '<div class="card">';
        echo '<h2>Content Optimizer</h2>';
        echo '<p>Analyze and optimize your content for better SEO performance.</p>';
        echo '<p>Get suggestions for improving readability, keyword usage, and overall content quality.</p>';
        echo '<p><button class="button button-primary wp-hss-tool-button" data-tool="content_optimizer">Open Tool</button></p>';
        echo '</div>';

        // Internal Link Analyzer
        echo '<div class="card">';
        echo '<h2>Internal Link Analyzer</h2>';
        echo '<p>Analyze your internal linking structure and get suggestions for improvement.</p>';
        echo '<p>Identify pages with few internal links and get recommendations for better link distribution.</p>';
        echo '<p><button class="button button-primary wp-hss-tool-button" data-tool="internal_link_analyzer">Open Tool</button></p>';
        echo '</div>';

        echo '</div>'; // End tools grid

        // Tool Content Area
        echo '<div id="wp-hss-tool-content" class="card" style="display: none;">';
        echo '<div class="wp-hss-tool-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">';
        echo '<h2 id="wp-hss-tool-title"></h2>';
        echo '<button id="wp-hss-close-tool" class="button">Close</button>';
        echo '</div>';
        echo '<div id="wp-hss-tool-body"></div>';
        echo '</div>';

        // Add JavaScript for tools
        echo '<script>
        jQuery(document).ready(function($) {
            // Handle tool button click
            $(".wp-hss-tool-button").on("click", function() {
                var toolId = $(this).data("tool");
                var toolTitle = $(this).closest(".card").find("h2").text();

                // Show tool content area
                $("#wp-hss-tool-content").show();

                // Set tool title
                $("#wp-hss-tool-title").text(toolTitle);

                // Show loading indicator
                $("#wp-hss-tool-body").html(\'<div class="wp-hss-loading"><span class="wp-hss-spinner"></span> Loading tool...</div>\');

                // Scroll to tool content
                $("html, body").animate({
                    scrollTop: $("#wp-hss-tool-content").offset().top - 50
                }, 500);

                // Simulate loading tool (in a real implementation, this would be an AJAX call)
                setTimeout(function() {
                    var html = "";

                    switch (toolId) {
                        case "bulk_title_optimizer":
                            html = getBulkTitleOptimizerHtml();
                            break;
                        case "meta_description_generator":
                            html = getMetaDescriptionGeneratorHtml();
                            break;
                        case "schema_markup_generator":
                            html = getSchemaMarkupGeneratorHtml();
                            break;
                        case "content_optimizer":
                            html = getContentOptimizerHtml();
                            break;
                        case "internal_link_analyzer":
                            html = getInternalLinkAnalyzerHtml();
                            break;
                        default:
                            html = "<p>Tool not implemented yet.</p>";
                    }

                    // Show tool content
                    $("#wp-hss-tool-body").html(html);
                }, 1000);
            });

            // Handle close tool button click
            $("#wp-hss-close-tool").on("click", function() {
                $("#wp-hss-tool-content").hide();
            });

            // Bulk Title Optimizer HTML
            function getBulkTitleOptimizerHtml() {
                var html = "";

                html += "<p>Select the content types you want to optimize titles for:</p>";
                html += "<p>";
                html += "<label style=\'margin-right: 15px;\'><input type=\'checkbox\' name=\'content_type[]\' value=\'post\' checked> Posts</label>";
                html += "<label style=\'margin-right: 15px;\'><input type=\'checkbox\' name=\'content_type[]\' value=\'page\' checked> Pages</label>";
                html += "<label><input type=\'checkbox\' name=\'content_type[]\' value=\'product\'> Products</label>";
                html += "</p>";

                html += "<p><button class=\'button button-primary\' id=\'wp-hss-generate-titles\'>Generate Optimized Titles</button></p>";

                html += "<div id=\'wp-hss-titles-results\'></div>";

                return html;
            }

            // Meta Description Generator HTML
            function getMetaDescriptionGeneratorHtml() {
                var html = "";

                html += "<p>Select the content types you want to generate meta descriptions for:</p>";
                html += "<p>";
                html += "<label style=\'margin-right: 15px;\'><input type=\'checkbox\' name=\'content_type[]\' value=\'post\' checked> Posts</label>";
                html += "<label style=\'margin-right: 15px;\'><input type=\'checkbox\' name=\'content_type[]\' value=\'page\' checked> Pages</label>";
                html += "<label><input type=\'checkbox\' name=\'content_type[]\' value=\'product\'> Products</label>";
                html += "</p>";

                html += "<p><button class=\'button button-primary\' id=\'wp-hss-generate-descriptions\'>Generate Meta Descriptions</button></p>";

                html += "<div id=\'wp-hss-descriptions-results\'></div>";

                return html;
            }

            // Schema Markup Generator HTML
            function getSchemaMarkupGeneratorHtml() {
                var html = "";

                html += "<p>Select the schema type you want to generate:</p>";
                html += "<p>";
                html += "<select id=\'wp-hss-schema-type\'>";
                html += "<option value=\'Article\'>Article</option>";
                html += "<option value=\'Product\'>Product</option>";
                html += "<option value=\'LocalBusiness\'>Local Business</option>";
                html += "<option value=\'Organization\'>Organization</option>";
                html += "<option value=\'Person\'>Person</option>";
                html += "<option value=\'Event\'>Event</option>";
                html += "<option value=\'Recipe\'>Recipe</option>";
                html += "<option value=\'Review\'>Review</option>";
                html += "</select>";
                html += "</p>";

                html += "<p><button class=\'button button-primary\' id=\'wp-hss-generate-schema\'>Generate Schema Markup</button></p>";

                html += "<div id=\'wp-hss-schema-results\'></div>";

                return html;
            }

            // Content Optimizer HTML
            function getContentOptimizerHtml() {
                var html = "";

                html += "<p>Select a post or page to optimize:</p>";
                html += "<p>";
                html += "<select id=\'wp-hss-content-id\'>";
                html += "<option value=\'\'>-- Select Content --</option>";
                html += "<option value=\'1\'>Sample Post 1</option>";
                html += "<option value=\'2\'>Sample Page 1</option>";
                html += "<option value=\'3\'>Sample Post 2</option>";
                html += "</select>";
                html += "</p>";

                html += "<p><button class=\'button button-primary\' id=\'wp-hss-analyze-content\'>Analyze Content</button></p>";

                html += "<div id=\'wp-hss-content-results\'></div>";

                return html;
            }

            // Internal Link Analyzer HTML
            function getInternalLinkAnalyzerHtml() {
                var html = "";

                html += "<p>Analyze your internal linking structure to identify opportunities for improvement.</p>";

                html += "<p><button class=\'button button-primary\' id=\'wp-hss-analyze-links\'>Analyze Internal Links</button></p>";

                html += "<div id=\'wp-hss-links-results\'></div>";

                return html;
            }
        });
        </script>';

        echo '</div>'; // End wrap
    }

    /**
     * Render settings test page
     */
    public function render_settings_test_page() {
        include(WP_HSS_PLUGIN_DIR . 'settings-test.php');
    }

    /**
     * Render simple settings page
     */
    public function render_simple_settings_page() {
        include(WP_HSS_PLUGIN_DIR . 'simple-settings.php');
    }

    /**
     * Render settings page
     */
    public function render_settings_page() {
        echo '<div class="wrap">';
        echo '<h1>Settings</h1>';
        echo '<p>Configure the plugin settings here.</p>';

        // Tabs
        echo '<div class="wp-hss-tabs">';
        echo '<div class="wp-hss-tab active" data-tab="wp-hss-general-settings">General</div>';
        echo '<div class="wp-hss-tab" data-tab="wp-hss-scan-settings">Scan Settings</div>';
        echo '<div class="wp-hss-tab" data-tab="wp-hss-api-settings">API Integration</div>';
        echo '<div class="wp-hss-tab" data-tab="wp-hss-advanced-settings">Advanced</div>';
        echo '</div>';

        // General Settings
        echo '<div id="wp-hss-general-settings" class="wp-hss-tab-content active">';
        echo '<div class="card">';
        echo '<h2>General Settings</h2>';
        echo '<form method="post" action="options.php">';
        settings_fields('wp_hss_settings');
        do_settings_sections('wp_hss_settings');
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th scope="row">Enable Dashboard Widget</th>';
        echo '<td><input type="checkbox" name="wp_hss_options[dashboard_widget]" value="1" checked></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Show in Admin Bar</th>';
        echo '<td><input type="checkbox" name="wp_hss_admin_bar" value="1" checked></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Email Notifications</th>';
        echo '<td><input type="checkbox" name="wp_hss_email_notifications" value="1"> Send weekly SEO health reports</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Email Address</th>';
        echo '<td><input type="email" name="wp_hss_email" value="" placeholder="<EMAIL>" class="regular-text"></td>';
        echo '</tr>';
        echo '</table>';
        echo '<p class="submit"><input type="submit" name="submit" id="submit" class="button button-primary" value="Save Changes"></p>';
        echo '</form>';
        echo '</div>';
        echo '</div>';

        // Scan Settings
        echo '<div id="wp-hss-scan-settings" class="wp-hss-tab-content">';
        echo '<div class="card">';
        echo '<h2>Scan Settings</h2>';
        echo '<form method="post" action="' . admin_url('admin-post.php') . '">';
        echo '<input type="hidden" name="action" value="wp_hss_save_settings">';
        echo '<input type="hidden" name="settings_group" value="scan">';
        echo wp_nonce_field('wp_hss_save_settings', 'wp_hss_settings_nonce', true, false);
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th scope="row">Scan Frequency</th>';
        echo '<td>';
        echo '<select name="wp_hss_scan_frequency" class="regular-text">';
        echo '<option value="daily">Daily</option>';
        echo '<option value="weekly" selected>Weekly</option>';
        echo '<option value="monthly">Monthly</option>';
        echo '<option value="manual">Manual Only</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Scan Depth</th>';
        echo '<td>';
        echo '<select name="wp_hss_scan_depth" class="regular-text">';
        echo '<option value="homepage">Homepage Only</option>';
        echo '<option value="main" selected>Main Pages</option>';
        echo '<option value="all">All Pages</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Scan Components</th>';
        echo '<td>';
        echo '<label style="display: block; margin-bottom: 5px;"><input type="checkbox" name="wp_hss_scan_meta" value="1" checked> Meta Tags</label>';
        echo '<label style="display: block; margin-bottom: 5px;"><input type="checkbox" name="wp_hss_scan_content" value="1" checked> Content</label>';
        echo '<label style="display: block; margin-bottom: 5px;"><input type="checkbox" name="wp_hss_scan_technical" value="1" checked> Technical SEO</label>';
        echo '<label style="display: block; margin-bottom: 5px;"><input type="checkbox" name="wp_hss_scan_mobile" value="1" checked> Mobile Friendliness</label>';
        echo '<label style="display: block;"><input type="checkbox" name="wp_hss_scan_performance" value="1" checked> Performance</label>';
        echo '</td>';
        echo '</tr>';
        echo '</table>';
        echo '<p class="submit"><input type="submit" name="submit" id="submit" class="button button-primary" value="Save Changes"></p>';
        echo '</form>';
        echo '</div>';
        echo '</div>';

        // API Integration
        echo '<div id="wp-hss-api-settings" class="wp-hss-tab-content">';
        echo '<div class="card">';
        echo '<h2>AI Integration</h2>';
        echo '<p>Connect to AI services to enhance SEO analysis and get intelligent recommendations.</p>';

        echo '<form method="post" action="' . admin_url('admin-post.php') . '">';
        echo '<input type="hidden" name="action" value="wp_hss_save_settings">';
        echo '<input type="hidden" name="settings_group" value="ai">';
        echo wp_nonce_field('wp_hss_save_settings', 'wp_hss_settings_nonce', true, false);

        // AI Provider Selection
        echo '<div class="wp-hss-ai-provider-selector">';
        echo '<h3>Select AI Provider</h3>';
        echo '<p>Choose which AI provider you want to configure:</p>';
        echo '<select id="ai-provider-selector" class="regular-text">';
        echo '<option value="openai" selected>OpenAI (GPT-3.5, GPT-4, GPT-4o)</option>';
        echo '<option value="anthropic">Anthropic (Claude 3 Haiku, Sonnet, Opus)</option>';
        echo '<option value="google">Google AI (Gemini Pro, Ultra)</option>';
        echo '<option value="mistral">Mistral AI</option>';
        echo '<option value="custom">Custom Provider</option>';
        echo '</select>';
        echo '</div>';

        // OpenAI Settings
        echo '<div id="openai-settings" class="ai-provider-settings">';
        echo '<h3>OpenAI Settings</h3>';
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th scope="row">Enable OpenAI</th>';
        echo '<td><input type="checkbox" name="wp_hss_enable_openai" value="1" checked> Use OpenAI for enhanced SEO analysis</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">API Key</th>';
        echo '<td><input type="password" name="wp_hss_openai_api_key" value="" placeholder="Enter your OpenAI API key" class="regular-text"></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Model</th>';
        echo '<td>';
        echo '<select name="wp_hss_openai_model" class="regular-text">';
        echo '<option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>';
        echo '<option value="gpt-3.5-turbo-16k">GPT-3.5 Turbo 16K</option>';
        echo '<option value="gpt-4" selected>GPT-4</option>';
        echo '<option value="gpt-4-turbo">GPT-4 Turbo</option>';
        echo '<option value="gpt-4o">GPT-4o</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Temperature</th>';
        echo '<td><input type="range" name="wp_hss_openai_temperature" min="0" max="1" step="0.1" value="0.7" class="regular-text"> <span id="openai-temp-value">0.7</span> (Higher = more creative)</td>';
        echo '</tr>';
        echo '</table>';
        echo '</div>';

        // Anthropic Settings
        echo '<div id="anthropic-settings" class="ai-provider-settings" style="display: none;">';
        echo '<h3>Anthropic Settings</h3>';
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th scope="row">Enable Anthropic</th>';
        echo '<td><input type="checkbox" name="wp_hss_enable_anthropic" value="1"> Use Anthropic Claude for enhanced SEO analysis</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">API Key</th>';
        echo '<td><input type="password" name="wp_hss_anthropic_api_key" value="" placeholder="Enter your Anthropic API key" class="regular-text"></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Model</th>';
        echo '<td>';
        echo '<select name="wp_hss_anthropic_model" class="regular-text">';
        echo '<option value="claude-instant-1">Claude Instant</option>';
        echo '<option value="claude-2">Claude 2</option>';
        echo '<option value="claude-3-haiku">Claude 3 Haiku</option>';
        echo '<option value="claude-3-sonnet" selected>Claude 3 Sonnet</option>';
        echo '<option value="claude-3-opus">Claude 3 Opus</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Temperature</th>';
        echo '<td><input type="range" name="wp_hss_anthropic_temperature" min="0" max="1" step="0.1" value="0.7" class="regular-text"> <span id="anthropic-temp-value">0.7</span> (Higher = more creative)</td>';
        echo '</tr>';
        echo '</table>';
        echo '</div>';

        // Google AI Settings
        echo '<div id="google-settings" class="ai-provider-settings" style="display: none;">';
        echo '<h3>Google AI Settings</h3>';
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th scope="row">Enable Google AI</th>';
        echo '<td><input type="checkbox" name="wp_hss_enable_google" value="1"> Use Google Gemini for enhanced SEO analysis</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">API Key</th>';
        echo '<td><input type="password" name="wp_hss_google_api_key" value="" placeholder="Enter your Google AI API key" class="regular-text"></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Model</th>';
        echo '<td>';
        echo '<select name="wp_hss_google_model" class="regular-text">';
        echo '<option value="gemini-pro">Gemini Pro</option>';
        echo '<option value="gemini-pro-vision">Gemini Pro Vision</option>';
        echo '<option value="gemini-ultra" selected>Gemini Ultra</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Temperature</th>';
        echo '<td><input type="range" name="wp_hss_google_temperature" min="0" max="1" step="0.1" value="0.7" class="regular-text"> <span id="google-temp-value">0.7</span> (Higher = more creative)</td>';
        echo '</tr>';
        echo '</table>';
        echo '</div>';

        // Mistral AI Settings
        echo '<div id="mistral-settings" class="ai-provider-settings" style="display: none;">';
        echo '<h3>Mistral AI Settings</h3>';
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th scope="row">Enable Mistral AI</th>';
        echo '<td><input type="checkbox" name="wp_hss_enable_mistral" value="1"> Use Mistral AI for enhanced SEO analysis</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">API Key</th>';
        echo '<td><input type="password" name="wp_hss_mistral_api_key" value="" placeholder="Enter your Mistral AI API key" class="regular-text"></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Model</th>';
        echo '<td>';
        echo '<select name="wp_hss_mistral_model" class="regular-text">';
        echo '<option value="mistral-tiny">Mistral Tiny</option>';
        echo '<option value="mistral-small">Mistral Small</option>';
        echo '<option value="mistral-medium" selected>Mistral Medium</option>';
        echo '<option value="mistral-large">Mistral Large</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Temperature</th>';
        echo '<td><input type="range" name="wp_hss_mistral_temperature" min="0" max="1" step="0.1" value="0.7" class="regular-text"> <span id="mistral-temp-value">0.7</span> (Higher = more creative)</td>';
        echo '</tr>';
        echo '</table>';
        echo '</div>';

        // Custom Provider Settings
        echo '<div id="custom-settings" class="ai-provider-settings" style="display: none;">';
        echo '<h3>Custom AI Provider</h3>';
        echo '<p>Configure a custom AI provider with your own endpoint.</p>';
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th scope="row">Enable Custom Provider</th>';
        echo '<td><input type="checkbox" name="wp_hss_enable_custom" value="1"> Use custom AI provider</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Provider Name</th>';
        echo '<td><input type="text" name="wp_hss_custom_provider_name" value="" placeholder="e.g., My Custom AI" class="regular-text"></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">API Endpoint</th>';
        echo '<td><input type="text" name="wp_hss_custom_endpoint" value="" placeholder="https://api.example.com/v1/completions" class="regular-text"></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">API Key</th>';
        echo '<td><input type="password" name="wp_hss_custom_api_key" value="" placeholder="Enter your API key" class="regular-text"></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Model Name/ID</th>';
        echo '<td><input type="text" name="wp_hss_custom_model" value="" placeholder="e.g., custom-model-v1" class="regular-text"></td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Request Format</th>';
        echo '<td><textarea name="wp_hss_custom_request_format" rows="5" class="large-text code" placeholder=\'{"model": "{model}", "prompt": "{prompt}", "temperature": {temperature}}\'></textarea></td>';
        echo '</tr>';
        echo '</table>';
        echo '</div>';

        // AI Features (Common for all providers)
        echo '<h3>AI Features</h3>';
        echo '<p>Select which features should use AI assistance:</p>';
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th scope="row">SEO Features</th>';
        echo '<td>';
        echo '<label style="display: block; margin-bottom: 5px;"><input type="checkbox" name="wp_hss_ai_title_optimization" value="1" checked> Title Optimization</label>';
        echo '<label style="display: block; margin-bottom: 5px;"><input type="checkbox" name="wp_hss_ai_meta_description" value="1" checked> Meta Description Generation</label>';
        echo '<label style="display: block; margin-bottom: 5px;"><input type="checkbox" name="wp_hss_ai_content_analysis" value="1" checked> Content Analysis</label>';
        echo '<label style="display: block; margin-bottom: 5px;"><input type="checkbox" name="wp_hss_ai_keyword_suggestions" value="1" checked> Keyword Suggestions</label>';
        echo '<label style="display: block; margin-bottom: 5px;"><input type="checkbox" name="wp_hss_ai_schema_generation" value="1" checked> Schema Markup Generation</label>';
        echo '<label style="display: block;"><input type="checkbox" name="wp_hss_ai_content_optimization" value="1" checked> Content Optimization Suggestions</label>';
        echo '</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Usage Limits</th>';
        echo '<td>';
        echo '<label>Max tokens per request: <input type="number" name="wp_hss_ai_max_tokens" value="2000" min="100" max="100000" step="100" class="small-text"></label>';
        echo '<p class="description">Higher values allow more detailed responses but consume more API credits.</p>';
        echo '</td>';
        echo '</tr>';
        echo '</table>';

        // Add JavaScript for provider selection and temperature sliders
        echo '<script>
        jQuery(document).ready(function($) {
            // Handle provider selection
            $("#ai-provider-selector").on("change", function() {
                var provider = $(this).val();

                // Hide all provider settings
                $(".ai-provider-settings").hide();

                // Show selected provider settings
                $("#" + provider + "-settings").show();
            });

            // Update temperature value displays
            $("input[name=\'wp_hss_openai_temperature\']").on("input", function() {
                $("#openai-temp-value").text($(this).val());
            });
            $("input[name=\'wp_hss_anthropic_temperature\']").on("input", function() {
                $("#anthropic-temp-value").text($(this).val());
            });
            $("input[name=\'wp_hss_google_temperature\']").on("input", function() {
                $("#google-temp-value").text($(this).val());
            });
            $("input[name=\'wp_hss_mistral_temperature\']").on("input", function() {
                $("#mistral-temp-value").text($(this).val());
            });
        });
        </script>';

        echo '<p class="submit"><input type="submit" name="submit" id="submit" class="button button-primary" value="Save Changes"></p>';
        echo '</form>';
        echo '</div>';
        echo '</div>';

        // Advanced Settings
        echo '<div id="wp-hss-advanced-settings" class="wp-hss-tab-content">';
        echo '<div class="card">';
        echo '<h2>Advanced Settings</h2>';
        echo '<p>These settings are for advanced users. Incorrect configuration may affect your site\'s performance.</p>';
        echo '<form method="post" action="options.php">';
        echo '<table class="form-table">';
        echo '<tr>';
        echo '<th scope="row">Debug Mode</th>';
        echo '<td><input type="checkbox" name="wp_hss_debug_mode" value="1"> Enable debug logging</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Cache Duration</th>';
        echo '<td>';
        echo '<select name="wp_hss_cache_duration" class="regular-text">';
        echo '<option value="3600">1 hour</option>';
        echo '<option value="86400" selected>1 day</option>';
        echo '<option value="604800">1 week</option>';
        echo '<option value="0">No caching</option>';
        echo '</select>';
        echo '</td>';
        echo '</tr>';
        echo '<tr>';
        echo '<th scope="row">Custom CSS</th>';
        echo '<td><textarea name="wp_hss_custom_css" rows="5" class="large-text code" placeholder=".wp-hss-custom { color: #ff0000; }"></textarea></td>';
        echo '</tr>';
        echo '</table>';
        echo '<p class="submit"><input type="submit" name="submit" id="submit" class="button button-primary" value="Save Changes"></p>';
        echo '</form>';
        echo '</div>';
        echo '</div>';

        // Add JavaScript for tabs
        echo '<script>
        jQuery(document).ready(function($) {
            // Handle tab clicks
            $(".wp-hss-tab").on("click", function() {
                var tabId = $(this).data("tab");

                // Update active tab
                $(".wp-hss-tab").removeClass("active");
                $(this).addClass("active");

                // Show active tab content
                $(".wp-hss-tab-content").removeClass("active");
                $("#" + tabId).addClass("active");
            });
        });
        </script>';

        echo '</div>'; // End wrap
    }
}

// Initialize the plugin
new WP_Health_SEO_Sentinel();
