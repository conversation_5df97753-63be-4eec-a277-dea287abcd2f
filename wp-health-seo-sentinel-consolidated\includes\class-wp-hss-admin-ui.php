<?php
/**
 * The admin-specific functionality of the plugin.
 *
 * @since      1.0.0
 */
class WP_HSS_Admin_UI {

    private $plugin_name;
    private $version;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     * @param    string    $plugin_name    The name of this plugin.
     * @param    string    $version        The version of this plugin.
     */
    public function __construct($plugin_name, $version) {
        $this->plugin_name = $plugin_name;
        $this->version = $version;
    }

    /**
     * Register the stylesheets for the admin area.
     *
     * @since    1.0.0
     * @param    string    $hook_suffix    The current admin page.
     */
    public function enqueue_styles($hook_suffix) {
        try {
            // Only load on our plugin pages
            if (strpos($hook_suffix, 'wp-hss') === false) {
                return;
            }

            // Check if the CSS file exists
            $css_file = WP_HSS_PLUGIN_URL . 'admin/css/wp-hss-admin.css';
            $css_path = WP_HSS_PLUGIN_DIR . 'admin/css/wp-hss-admin.css';

            // Create CSS directory if it doesn't exist
            if (!file_exists(dirname($css_path))) {
                wp_mkdir_p(dirname($css_path));
            }

            // Create a basic CSS file if it doesn't exist
            if (!file_exists($css_path)) {
                $css_content = "
                .wp-hss-container {
                    padding: 20px;
                    background: #fff;
                    border: 1px solid #ccd0d4;
                    box-shadow: 0 1px 1px rgba(0,0,0,.04);
                    margin-top: 20px;
                }

                .wp-hss-header {
                    margin-bottom: 20px;
                    border-bottom: 1px solid #eee;
                    padding-bottom: 20px;
                }

                .wp-hss-card {
                    background: #fff;
                    border: 1px solid #ccd0d4;
                    box-shadow: 0 1px 1px rgba(0,0,0,.04);
                    padding: 15px;
                    margin-bottom: 20px;
                }

                .wp-hss-card h2 {
                    margin-top: 0;
                }

                .wp-hss-results-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                }

                .wp-hss-results-table th,
                .wp-hss-results-table td {
                    padding: 10px;
                    text-align: left;
                    border-bottom: 1px solid #eee;
                }
                ";

                file_put_contents($css_path, $css_content);
            }

            wp_enqueue_style($this->plugin_name, $css_file, array(), $this->version, 'all');
        } catch (Exception $e) {
            error_log('WP HSS: Error enqueuing styles: ' . $e->getMessage());
        }
    }

    /**
     * Register the JavaScript for the admin area.
     *
     * @since    1.0.0
     * @param    string    $hook_suffix    The current admin page.
     */
    public function enqueue_scripts($hook_suffix) {
        try {
            if (strpos($hook_suffix, 'wp-hss') === false) {
                return;
            }

            // Check if the JS file exists
            $js_file = WP_HSS_PLUGIN_URL . 'admin/js/wp-hss-admin.js';
            $js_path = WP_HSS_PLUGIN_DIR . 'admin/js/wp-hss-admin.js';

            // Create JS directory if it doesn't exist
            if (!file_exists(dirname($js_path))) {
                wp_mkdir_p(dirname($js_path));
            }

            // Create a basic JS file if it doesn't exist
            if (!file_exists($js_path)) {
                $js_content = "
                jQuery(document).ready(function($) {
                    // Handle scan button click
                    $('#wp-hss-scan-button').on('click', function() {
                        $(this).prop('disabled', true).text('Scanning...');

                        $.ajax({
                            url: wp_hss_ajax.ajax_url,
                            type: 'POST',
                            data: {
                                action: 'wp_hss_start_scan',
                                nonce: wp_hss_ajax.nonce
                            },
                            success: function(response) {
                                if (response.success) {
                                    $('#wp-hss-scan-results').html(response.data.html);
                                } else {
                                    $('#wp-hss-scan-results').html('<div class=\"notice notice-error\"><p>' + response.data.message + '</p></div>');
                                }
                                $('#wp-hss-scan-button').prop('disabled', false).text('Start Scan');
                            },
                            error: function() {
                                $('#wp-hss-scan-results').html('<div class=\"notice notice-error\"><p>An error occurred. Please try again.</p></div>');
                                $('#wp-hss-scan-button').prop('disabled', false).text('Start Scan');
                            }
                        });
                    });
                });
                ";

                file_put_contents($js_path, $js_content);
            }

            wp_enqueue_script($this->plugin_name, $js_file, array('jquery'), $this->version, true);

            wp_localize_script($this->plugin_name, 'wp_hss_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('wp_hss_ajax_nonce'),
                'strings' => array(
                    'scanning' => __('Scanning...', 'wp-hss'),
                    'scan_complete' => __('Scan complete!', 'wp-hss'),
                    'scan_failed' => __('Scan failed. Please try again.', 'wp-hss'),
                    'fixing' => __('Fixing...', 'wp-hss'),
                    'fix_complete' => __('Fix complete!', 'wp-hss'),
                    'fix_failed' => __('Fix failed. Please try again.', 'wp-hss')
                )
            ));
        } catch (Exception $e) {
            error_log('WP HSS: Error enqueuing scripts: ' . $e->getMessage());
        }
    }

    /**
     * Register the admin menu pages.
     *
     * @since    1.0.0
     */
    public function add_admin_menu() {
        try {
            add_menu_page(
                __('WP Sentinel', 'wp-hss'),
                __('WP Sentinel', 'wp-hss'),
                'manage_options', // Capability
                'wp-hss-dashboard',
                array($this, 'display_dashboard_page'),
                'dashicons-shield-alt', // Icon
                75 // Position
            );

            add_submenu_page(
                'wp-hss-dashboard',
                __('Dashboard', 'wp-hss'),
                __('Dashboard', 'wp-hss'),
                'manage_options',
                'wp-hss-dashboard', // Parent slug
                array($this, 'display_dashboard_page')
            );

            add_submenu_page(
                'wp-hss-dashboard',
                __('Settings', 'wp-hss'),
                __('Settings', 'wp-hss'),
                'manage_options',
                'wp-hss-settings',
                array($this, 'display_settings_page')
            );
        } catch (Exception $e) {
            error_log('WP HSS: Error adding admin menu: ' . $e->getMessage());
        }
    }

    /**
     * Display the dashboard page.
     *
     * @since    1.0.0
     */
    public function display_dashboard_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'wp-hss'));
        }

        // Get scan stats
        $stats = $this->get_scan_stats();

        ?>
        <div class="wrap">
            <div class="wp-hss-container">
                <div class="wp-hss-header">
                    <div class="wp-hss-header-content">
                        <div class="wp-hss-logo">
                            <span class="dashicons dashicons-shield-alt" style="font-size: 32px; color: var(--wp-hss-primary);"></span>
                            <h1><?php _e('WP Health & SEO Sentinel', 'wp-hss'); ?></h1>
                        </div>
                        <p><?php _e('Comprehensive SEO analysis and optimization for WordPress', 'wp-hss'); ?></p>
                    </div>
                    <div class="wp-hss-header-actions">
                        <a href="<?php echo admin_url('admin.php?page=wp-hss-settings'); ?>" class="wp-hss-button wp-hss-button-outline">
                            <i class="dashicons dashicons-admin-settings"></i>
                            <?php _e('Settings', 'wp-hss'); ?>
                        </a>
                        <button id="wp-hss-scan-button" class="wp-hss-button wp-hss-button-primary">
                            <i class="dashicons dashicons-search"></i>
                            <?php _e('Start Scan', 'wp-hss'); ?>
                        </button>
                    </div>
                </div>

                <!-- Dashboard Overview -->
                <div class="wp-hss-overview">
                    <div class="wp-hss-dashboard-grid">
                        <!-- Health Score -->
                        <div class="wp-hss-stat-card">
                            <div class="wp-hss-stat-header">
                                <h3 class="wp-hss-stat-title"><?php _e('SEO Health Score', 'wp-hss'); ?></h3>
                                <div class="wp-hss-stat-icon <?php echo $stats['health_score'] >= 80 ? 'good' : ($stats['health_score'] >= 50 ? 'warning' : 'critical'); ?>">
                                    <i class="dashicons dashicons-chart-line"></i>
                                </div>
                            </div>
                            <p id="wp-hss-health-score" class="wp-hss-stat-value" style="color: <?php echo $stats['health_score'] >= 80 ? 'var(--wp-hss-success)' : ($stats['health_score'] >= 50 ? 'var(--wp-hss-warning)' : 'var(--wp-hss-danger)'); ?>">
                                <?php echo $stats['health_score']; ?>
                            </p>
                            <p class="wp-hss-stat-description">
                                <?php
                                if ($stats['health_score'] >= 80) {
                                    _e('Your site is performing well!', 'wp-hss');
                                } elseif ($stats['health_score'] >= 50) {
                                    _e('Your site needs some improvements.', 'wp-hss');
                                } else {
                                    _e('Your site needs significant SEO work.', 'wp-hss');
                                }
                                ?>
                            </p>
                        </div>

                        <!-- Critical Issues -->
                        <div class="wp-hss-stat-card critical">
                            <div class="wp-hss-stat-header">
                                <h3 class="wp-hss-stat-title"><?php _e('Critical Issues', 'wp-hss'); ?></h3>
                                <div class="wp-hss-stat-icon critical">
                                    <i class="dashicons dashicons-warning"></i>
                                </div>
                            </div>
                            <p id="wp-hss-stat-critical" class="wp-hss-stat-value"><?php echo $stats['critical_count']; ?></p>
                            <p class="wp-hss-stat-description">
                                <?php _e('Issues that need immediate attention', 'wp-hss'); ?>
                            </p>
                        </div>

                        <!-- Warnings -->
                        <div class="wp-hss-stat-card warning">
                            <div class="wp-hss-stat-header">
                                <h3 class="wp-hss-stat-title"><?php _e('Warnings', 'wp-hss'); ?></h3>
                                <div class="wp-hss-stat-icon warning">
                                    <i class="dashicons dashicons-flag"></i>
                                </div>
                            </div>
                            <p id="wp-hss-stat-warning" class="wp-hss-stat-value"><?php echo $stats['warning_count']; ?></p>
                            <p class="wp-hss-stat-description">
                                <?php _e('Issues that should be addressed', 'wp-hss'); ?>
                            </p>
                        </div>

                        <!-- Info/Suggestions -->
                        <div class="wp-hss-stat-card info">
                            <div class="wp-hss-stat-header">
                                <h3 class="wp-hss-stat-title"><?php _e('Suggestions', 'wp-hss'); ?></h3>
                                <div class="wp-hss-stat-icon info">
                                    <i class="dashicons dashicons-info"></i>
                                </div>
                            </div>
                            <p id="wp-hss-stat-info" class="wp-hss-stat-value"><?php echo $stats['info_count']; ?></p>
                            <p class="wp-hss-stat-description">
                                <?php _e('Opportunities for improvement', 'wp-hss'); ?>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Main Content Tabs -->
                <div class="wp-hss-tabs">
                    <div class="wp-hss-tab active" data-tab="wp-hss-tab-scanner">
                        <i class="dashicons dashicons-search"></i> <?php _e('Scanner', 'wp-hss'); ?>
                    </div>
                    <div class="wp-hss-tab" data-tab="wp-hss-tab-history">
                        <i class="dashicons dashicons-calendar-alt"></i> <?php _e('Scan History', 'wp-hss'); ?>
                    </div>
                    <div class="wp-hss-tab" data-tab="wp-hss-tab-tools">
                        <i class="dashicons dashicons-admin-tools"></i> <?php _e('Tools', 'wp-hss'); ?>
                    </div>
                    <div class="wp-hss-tab" data-tab="wp-hss-tab-help">
                        <i class="dashicons dashicons-editor-help"></i> <?php _e('Help', 'wp-hss'); ?>
                    </div>
                </div>

                <!-- Scanner Tab -->
                <div id="wp-hss-tab-scanner" class="wp-hss-tab-content active">
                    <div class="wp-hss-card">
                        <h2><i class="dashicons dashicons-search"></i> <?php _e('SEO Scanner', 'wp-hss'); ?></h2>
                        <div class="wp-hss-card-content">
                            <p><?php _e('The SEO Scanner analyzes your website for common SEO issues and provides actionable recommendations to improve your search engine rankings.', 'wp-hss'); ?></p>

                            <div class="wp-hss-info-box">
                                <h3><i class="dashicons dashicons-info"></i> <?php _e('What We Check', 'wp-hss'); ?></h3>
                                <ul>
                                    <li><?php _e('Meta titles and descriptions', 'wp-hss'); ?></li>
                                    <li><?php _e('Content quality and keyword optimization', 'wp-hss'); ?></li>
                                    <li><?php _e('Image alt text and accessibility', 'wp-hss'); ?></li>
                                    <li><?php _e('Heading structure and internal linking', 'wp-hss'); ?></li>
                                    <li><?php _e('Schema markup and technical SEO factors', 'wp-hss'); ?></li>
                                    <li><?php _e('Mobile-friendliness and page speed', 'wp-hss'); ?></li>
                                </ul>
                            </div>
                        </div>
                        <div class="wp-hss-card-footer">
                            <button id="wp-hss-scan-button-card" class="wp-hss-button wp-hss-button-primary wp-hss-button-lg">
                                <i class="dashicons dashicons-search"></i>
                                <?php _e('Start Comprehensive Scan', 'wp-hss'); ?>
                            </button>
                        </div>
                    </div>

                    <div id="wp-hss-scan-results"></div>
                </div>

                <!-- Scan History Tab -->
                <div id="wp-hss-tab-history" class="wp-hss-tab-content">
                    <div class="wp-hss-card">
                        <h2><i class="dashicons dashicons-calendar-alt"></i> <?php _e('Scan History', 'wp-hss'); ?></h2>
                        <div class="wp-hss-card-content">
                            <?php
                            // Get scan history
                            $scan_history = $this->get_scan_history();

                            if (empty($scan_history)) {
                                echo '<div class="wp-hss-info-box">';
                                echo '<h3><i class="dashicons dashicons-info"></i> ' . __('No Scan History', 'wp-hss') . '</h3>';
                                echo '<p>' . __('You haven\'t performed any scans yet. Run your first scan to see results here.', 'wp-hss') . '</p>';
                                echo '</div>';
                            } else {
                                echo '<table class="wp-hss-results-table">';
                                echo '<thead>';
                                echo '<tr>';
                                echo '<th>' . __('Date', 'wp-hss') . '</th>';
                                echo '<th>' . __('Issues Found', 'wp-hss') . '</th>';
                                echo '<th>' . __('Health Score', 'wp-hss') . '</th>';
                                echo '<th>' . __('Actions', 'wp-hss') . '</th>';
                                echo '</tr>';
                                echo '</thead>';
                                echo '<tbody>';

                                foreach ($scan_history as $scan) {
                                    echo '<tr>';
                                    echo '<td>' . date('M j, Y g:i a', strtotime($scan['date'])) . '</td>';
                                    echo '<td>' . $scan['issues_count'] . '</td>';
                                    echo '<td>' . $scan['health_score'] . '</td>';
                                    echo '<td>';
                                    echo '<button class="wp-hss-button wp-hss-button-sm wp-hss-button-outline wp-hss-view-scan" data-scan-id="' . esc_attr($scan['id']) . '">';
                                    echo '<i class="dashicons dashicons-visibility"></i> ' . __('View', 'wp-hss');
                                    echo '</button>';
                                    echo '</td>';
                                    echo '</tr>';
                                }

                                echo '</tbody>';
                                echo '</table>';
                            }
                            ?>
                        </div>
                    </div>
                </div>

                <!-- Tools Tab -->
                <div id="wp-hss-tab-tools" class="wp-hss-tab-content">
                    <div class="wp-hss-card">
                        <h2><i class="dashicons dashicons-admin-tools"></i> <?php _e('SEO Tools', 'wp-hss'); ?></h2>
                        <div class="wp-hss-card-content">
                            <div class="wp-hss-dashboard-grid">
                                <!-- Bulk Title Optimizer -->
                                <div class="wp-hss-tool-card">
                                    <h3><i class="dashicons dashicons-editor-textcolor"></i> <?php _e('Bulk Title Optimizer', 'wp-hss'); ?></h3>
                                    <p><?php _e('Optimize all your page titles at once using AI-powered suggestions.', 'wp-hss'); ?></p>
                                    <button class="wp-hss-button wp-hss-button-primary wp-hss-button-sm wp-hss-tool-button" data-tool="title-optimizer">
                                        <?php _e('Open Tool', 'wp-hss'); ?>
                                    </button>
                                </div>

                                <!-- Meta Description Generator -->
                                <div class="wp-hss-tool-card">
                                    <h3><i class="dashicons dashicons-editor-paragraph"></i> <?php _e('Meta Description Generator', 'wp-hss'); ?></h3>
                                    <p><?php _e('Generate optimized meta descriptions for your content using AI.', 'wp-hss'); ?></p>
                                    <button class="wp-hss-button wp-hss-button-primary wp-hss-button-sm wp-hss-tool-button" data-tool="meta-generator">
                                        <?php _e('Open Tool', 'wp-hss'); ?>
                                    </button>
                                </div>

                                <!-- Image Alt Text Generator -->
                                <div class="wp-hss-tool-card">
                                    <h3><i class="dashicons dashicons-format-image"></i> <?php _e('Image Alt Text Generator', 'wp-hss'); ?></h3>
                                    <p><?php _e('Generate SEO-friendly alt text for all your images automatically.', 'wp-hss'); ?></p>
                                    <button class="wp-hss-button wp-hss-button-primary wp-hss-button-sm wp-hss-tool-button" data-tool="alt-text-generator">
                                        <?php _e('Open Tool', 'wp-hss'); ?>
                                    </button>
                                </div>

                                <!-- Schema Markup Generator -->
                                <div class="wp-hss-tool-card">
                                    <h3><i class="dashicons dashicons-editor-code"></i> <?php _e('Schema Markup Generator', 'wp-hss'); ?></h3>
                                    <p><?php _e('Create and validate schema markup for your pages to improve rich snippets.', 'wp-hss'); ?></p>
                                    <button class="wp-hss-button wp-hss-button-primary wp-hss-button-sm wp-hss-tool-button" data-tool="schema-generator">
                                        <?php _e('Open Tool', 'wp-hss'); ?>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Help Tab -->
                <div id="wp-hss-tab-help" class="wp-hss-tab-content">
                    <div class="wp-hss-card">
                        <h2><i class="dashicons dashicons-editor-help"></i> <?php _e('Help & Documentation', 'wp-hss'); ?></h2>
                        <div class="wp-hss-card-content">
                            <div class="wp-hss-info-box">
                                <h3><i class="dashicons dashicons-info"></i> <?php _e('About WP Health & SEO Sentinel', 'wp-hss'); ?></h3>
                                <p><?php _e('WP Health & SEO Sentinel is a comprehensive SEO analysis and optimization tool for WordPress. It helps you identify and fix SEO issues to improve your search engine rankings.', 'wp-hss'); ?></p>
                            </div>

                            <h3><?php _e('Frequently Asked Questions', 'wp-hss'); ?></h3>

                            <div class="wp-hss-faq">
                                <div class="wp-hss-faq-item">
                                    <h4><?php _e('How often should I run a scan?', 'wp-hss'); ?></h4>
                                    <p><?php _e('We recommend running a full scan at least once a month, and after publishing significant new content or making major changes to your site.', 'wp-hss'); ?></p>
                                </div>

                                <div class="wp-hss-faq-item">
                                    <h4><?php _e('What is the SEO Health Score?', 'wp-hss'); ?></h4>
                                    <p><?php _e('The SEO Health Score is a numerical representation of your site\'s overall SEO performance. It takes into account various factors including meta tags, content quality, technical SEO, and more.', 'wp-hss'); ?></p>
                                </div>

                                <div class="wp-hss-faq-item">
                                    <h4><?php _e('How does the AI-powered optimization work?', 'wp-hss'); ?></h4>
                                    <p><?php _e('Our AI integration uses advanced language models to analyze your content and generate optimized titles, descriptions, and other SEO elements. It considers best practices, keyword relevance, and readability to create compelling, search-friendly content.', 'wp-hss'); ?></p>
                                </div>
                            </div>

                            <div class="wp-hss-card-footer">
                                <p><strong><?php _e('Plugin Version:', 'wp-hss'); ?></strong> <?php echo WP_HSS_VERSION; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            // Sync the two scan buttons
            jQuery(document).ready(function($) {
                $('#wp-hss-scan-button-card').on('click', function() {
                    $('#wp-hss-scan-button').click();
                });
            });
        </script>
        <?php
    }

    /**
     * Get scan statistics.
     *
     * @since    1.0.0
     * @return   array    Scan statistics.
     */
    private function get_scan_stats() {
        // Default stats with sample data for demonstration
        $stats = array(
            'critical_count' => 3,
            'warning_count' => 8,
            'info_count' => 5,
            'total_count' => 16,
            'health_score' => 78
        );

        // Only try to get real stats if the DB class exists and has the required methods
        if (class_exists('WP_HSS_DB') && method_exists('WP_HSS_DB', 'get_latest_scan') && method_exists('WP_HSS_DB', 'get_issues_by_scan')) {
            try {
                // Get latest scan
                $latest_scan = WP_HSS_DB::get_latest_scan();

                if ($latest_scan) {
                    // Get issues for this scan
                    $issues = WP_HSS_DB::get_issues_by_scan($latest_scan['id']);

                    if (is_array($issues)) {
                        // Reset counters
                        $stats['critical_count'] = 0;
                        $stats['warning_count'] = 0;
                        $stats['info_count'] = 0;

                        // Count issues by severity
                        foreach ($issues as $issue) {
                            if (isset($issue['severity'])) {
                                if ($issue['severity'] === 'critical') {
                                    $stats['critical_count']++;
                                } elseif ($issue['severity'] === 'warning') {
                                    $stats['warning_count']++;
                                } elseif ($issue['severity'] === 'info') {
                                    $stats['info_count']++;
                                }
                            }
                        }

                        $stats['total_count'] = count($issues);

                        // Calculate health score
                        if ($stats['total_count'] > 0) {
                            // Weight issues by severity
                            $weighted_issues = ($stats['critical_count'] * 5) + ($stats['warning_count'] * 2) + $stats['info_count'];
                            $max_possible_score = $stats['total_count'] * 5; // If all issues were critical

                            // Calculate score (higher is better)
                            $score = 100 - (($weighted_issues / $max_possible_score) * 100);

                            // Ensure score is between 0 and 100
                            $stats['health_score'] = max(0, min(100, round($score)));
                        }
                    }
                }
            } catch (Exception $e) {
                error_log('WP HSS: Error getting scan stats: ' . $e->getMessage());
                // Keep using the default sample stats
            }
        }

        return $stats;
    }

    /**
     * Get scan history.
     *
     * @since    1.0.0
     * @return   array    Scan history.
     */
    private function get_scan_history() {
        // Default empty history
        $history = array();

        // Only try to get real history if the DB class exists and has the required methods
        if (class_exists('WP_HSS_DB') && method_exists('WP_HSS_DB', 'get_scans') && method_exists('WP_HSS_DB', 'get_issues_by_scan')) {
            try {
                $scans = WP_HSS_DB::get_scans(10); // Get last 10 scans

                if (is_array($scans)) {
                    foreach ($scans as $scan) {
                        if (isset($scan['id'])) {
                            $issues = WP_HSS_DB::get_issues_by_scan($scan['id']);

                            // Count issues by severity
                            $critical_count = 0;
                            $warning_count = 0;
                            $info_count = 0;

                            if (is_array($issues)) {
                                foreach ($issues as $issue) {
                                    if (isset($issue['severity'])) {
                                        if ($issue['severity'] === 'critical') {
                                            $critical_count++;
                                        } elseif ($issue['severity'] === 'warning') {
                                            $warning_count++;
                                        } elseif ($issue['severity'] === 'info') {
                                            $info_count++;
                                        }
                                    }
                                }

                                $total_count = count($issues);

                                // Calculate health score
                                $health_score = 100;
                                if ($total_count > 0) {
                                    // Weight issues by severity
                                    $weighted_issues = ($critical_count * 5) + ($warning_count * 2) + $info_count;
                                    $max_possible_score = $total_count * 5; // If all issues were critical

                                    // Calculate score (higher is better)
                                    $score = 100 - (($weighted_issues / $max_possible_score) * 100);

                                    // Ensure score is between 0 and 100
                                    $health_score = max(0, min(100, round($score)));
                                }

                                $history[] = array(
                                    'id' => $scan['id'],
                                    'date' => isset($scan['created_at']) ? $scan['created_at'] : date('Y-m-d H:i:s'),
                                    'issues_count' => $total_count,
                                    'health_score' => $health_score
                                );
                            }
                        }
                    }
                }
            } catch (Exception $e) {
                error_log('WP HSS: Error getting scan history: ' . $e->getMessage());
                // Keep using the empty history
            }
        }

        // If no real history, add some sample data for demonstration
        if (empty($history)) {
            // Sample history data
            $history = array(
                array(
                    'id' => 'sample1',
                    'date' => date('Y-m-d H:i:s', strtotime('-1 day')),
                    'issues_count' => 16,
                    'health_score' => 78
                ),
                array(
                    'id' => 'sample2',
                    'date' => date('Y-m-d H:i:s', strtotime('-3 days')),
                    'issues_count' => 22,
                    'health_score' => 65
                )
            );
        }

        return $history;
    }

    /**
     * Display the settings page.
     *
     * @since    1.0.0
     */
    public function display_settings_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'wp-hss'));
        }

        // Get current settings
        $settings = get_option('wp_hss_settings', array());

        // Save settings if form is submitted
        if (isset($_POST['wp_hss_save_settings'])) {
            check_admin_referer('wp_hss_settings_nonce');

            $new_settings = array(
                'enable_scanning' => isset($_POST['wp_hss_enable_scanning']) ? 1 : 0,
                'scan_post_types' => isset($_POST['wp_hss_scan_post_types']) ? $_POST['wp_hss_scan_post_types'] : array('post', 'page'),
                'ai_enable_features' => isset($_POST['wp_hss_ai_enable']) ? 1 : 0,
                'ai_api_key' => isset($_POST['wp_hss_ai_api_key']) ? sanitize_text_field($_POST['wp_hss_ai_api_key']) : '',
                'ai_provider' => isset($_POST['wp_hss_ai_provider']) ? sanitize_text_field($_POST['wp_hss_ai_provider']) : 'openai',
                'ai_model' => isset($_POST['wp_hss_ai_model']) ? sanitize_text_field($_POST['wp_hss_ai_model']) : 'gpt-4',
                'title_min_length' => isset($_POST['wp_hss_title_min_length']) ? intval($_POST['wp_hss_title_min_length']) : 30,
                'title_max_length' => isset($_POST['wp_hss_title_max_length']) ? intval($_POST['wp_hss_title_max_length']) : 60,
                'meta_min_length' => isset($_POST['wp_hss_meta_min_length']) ? intval($_POST['wp_hss_meta_min_length']) : 120,
                'meta_max_length' => isset($_POST['wp_hss_meta_max_length']) ? intval($_POST['wp_hss_meta_max_length']) : 160,
                'enable_advanced_scanning' => isset($_POST['wp_hss_enable_advanced_scanning']) ? 1 : 0,
                'enable_content_analysis' => isset($_POST['wp_hss_enable_content_analysis']) ? 1 : 0,
                'enable_keyword_analysis' => isset($_POST['wp_hss_enable_keyword_analysis']) ? 1 : 0,
                'enable_schema_validation' => isset($_POST['wp_hss_enable_schema_validation']) ? 1 : 0,
                'enable_mobile_friendly_check' => isset($_POST['wp_hss_enable_mobile_friendly_check']) ? 1 : 0,
                'enable_performance_check' => isset($_POST['wp_hss_enable_performance_check']) ? 1 : 0
            );

            // Validate API key if enabled
            if ($new_settings['ai_enable_features'] && !empty($new_settings['ai_api_key'])) {
                $validation_result = $this->validate_ai_api_key($new_settings['ai_api_key'], $new_settings['ai_provider']);
                if ($validation_result['success']) {
                    $new_settings['ai_api_key_valid'] = true;
                    echo '<div class="notice notice-success is-dismissible"><p>' . __('AI API key validated successfully!', 'wp-hss') . '</p></div>';
                } else {
                    $new_settings['ai_api_key_valid'] = false;
                    echo '<div class="notice notice-error is-dismissible"><p>' . __('AI API key validation failed: ', 'wp-hss') . esc_html($validation_result['message']) . '</p></div>';
                }
            }

            update_option('wp_hss_settings', $new_settings);

            echo '<div class="notice notice-success is-dismissible"><p>' . __('Settings saved successfully.', 'wp-hss') . '</p></div>';

            $settings = $new_settings;
        }

        // Check if API key is valid
        $api_key_status = '';
        if (isset($settings['ai_enable_features']) && $settings['ai_enable_features'] && !empty($settings['ai_api_key'])) {
            if (isset($settings['ai_api_key_valid']) && $settings['ai_api_key_valid']) {
                $api_key_status = '<span class="wp-hss-api-status wp-hss-api-valid">' . __('API Key Valid', 'wp-hss') . '</span>';
            } else {
                $api_key_status = '<span class="wp-hss-api-status wp-hss-api-invalid">' . __('API Key Invalid', 'wp-hss') . '</span>';
            }
        }

        ?>
        <div class="wrap">
            <h1><?php _e('WP Health & SEO Sentinel Settings', 'wp-hss'); ?></h1>

            <form method="post" action="">
                <?php wp_nonce_field('wp_hss_settings_nonce'); ?>

                <div class="wp-hss-settings-section">
                    <h2><?php _e('General Settings', 'wp-hss'); ?></h2>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable Scanning', 'wp-hss'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="wp_hss_enable_scanning" value="1" <?php checked(isset($settings['enable_scanning']) ? $settings['enable_scanning'] : 1); ?>>
                                    <?php _e('Enable SEO scanning functionality', 'wp-hss'); ?>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Post Types to Scan', 'wp-hss'); ?></th>
                            <td>
                                <?php
                                $post_types = get_post_types(array('public' => true), 'objects');
                                $selected_post_types = isset($settings['scan_post_types']) ? $settings['scan_post_types'] : array('post', 'page');

                                foreach ($post_types as $post_type) {
                                    ?>
                                    <label style="display: block; margin-bottom: 5px;">
                                        <input type="checkbox" name="wp_hss_scan_post_types[]" value="<?php echo esc_attr($post_type->name); ?>" <?php checked(in_array($post_type->name, $selected_post_types)); ?>>
                                        <?php echo esc_html($post_type->label); ?>
                                    </label>
                                    <?php
                                }
                                ?>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="wp-hss-settings-section">
                    <h2><?php _e('AI Integration', 'wp-hss'); ?></h2>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable AI Features', 'wp-hss'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="wp_hss_ai_enable" id="wp-hss-ai-enable" value="1" <?php checked(isset($settings['ai_enable_features']) ? $settings['ai_enable_features'] : 0); ?>>
                                    <?php _e('Enable AI-powered SEO suggestions and fixes', 'wp-hss'); ?>
                                </label>
                                <p class="description"><?php _e('Use artificial intelligence to generate better titles, descriptions, and content suggestions.', 'wp-hss'); ?></p>
                            </td>
                        </tr>
                        <tr class="wp-hss-ai-setting">
                            <th scope="row"><?php _e('AI Provider', 'wp-hss'); ?></th>
                            <td>
                                <select name="wp_hss_ai_provider" id="wp-hss-ai-provider">
                                    <option value="openai" <?php selected(isset($settings['ai_provider']) ? $settings['ai_provider'] : 'openai', 'openai'); ?>><?php _e('OpenAI', 'wp-hss'); ?></option>
                                    <option value="anthropic" <?php selected(isset($settings['ai_provider']) ? $settings['ai_provider'] : 'openai', 'anthropic'); ?>><?php _e('Anthropic', 'wp-hss'); ?></option>
                                    <option value="google" <?php selected(isset($settings['ai_provider']) ? $settings['ai_provider'] : 'openai', 'google'); ?>><?php _e('Google AI', 'wp-hss'); ?></option>
                                </select>
                            </td>
                        </tr>
                        <tr class="wp-hss-ai-setting">
                            <th scope="row"><?php _e('AI Model', 'wp-hss'); ?></th>
                            <td>
                                <select name="wp_hss_ai_model" id="wp-hss-ai-model">
                                    <option value="gpt-4" <?php selected(isset($settings['ai_model']) ? $settings['ai_model'] : 'gpt-4', 'gpt-4'); ?>><?php _e('GPT-4 (Best quality)', 'wp-hss'); ?></option>
                                    <option value="gpt-3.5-turbo" <?php selected(isset($settings['ai_model']) ? $settings['ai_model'] : 'gpt-4', 'gpt-3.5-turbo'); ?>><?php _e('GPT-3.5 Turbo (Faster)', 'wp-hss'); ?></option>
                                    <option value="claude-3" <?php selected(isset($settings['ai_model']) ? $settings['ai_model'] : 'gpt-4', 'claude-3'); ?>><?php _e('Claude 3', 'wp-hss'); ?></option>
                                    <option value="gemini-pro" <?php selected(isset($settings['ai_model']) ? $settings['ai_model'] : 'gpt-4', 'gemini-pro'); ?>><?php _e('Gemini Pro', 'wp-hss'); ?></option>
                                </select>
                                <p class="description"><?php _e('Select the AI model to use for generating content. Better models provide higher quality results but may be slower or more expensive.', 'wp-hss'); ?></p>
                            </td>
                        </tr>
                        <tr class="wp-hss-ai-setting">
                            <th scope="row"><?php _e('API Key', 'wp-hss'); ?></th>
                            <td>
                                <input type="password" name="wp_hss_ai_api_key" id="wp-hss-ai-api-key" value="<?php echo esc_attr(isset($settings['ai_api_key']) ? $settings['ai_api_key'] : ''); ?>" class="regular-text">
                                <?php echo $api_key_status; ?>
                                <button type="button" id="wp-hss-validate-api-key" class="button button-secondary"><?php _e('Validate API Key', 'wp-hss'); ?></button>
                                <p class="description"><?php _e('Enter your API key for the selected AI provider.', 'wp-hss'); ?></p>
                                <div id="wp-hss-api-validation-result"></div>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="wp-hss-settings-section">
                    <h2><?php _e('SEO Parameters', 'wp-hss'); ?></h2>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Title Length', 'wp-hss'); ?></th>
                            <td>
                                <label>
                                    <?php _e('Min:', 'wp-hss'); ?>
                                    <input type="number" name="wp_hss_title_min_length" value="<?php echo esc_attr(isset($settings['title_min_length']) ? $settings['title_min_length'] : 30); ?>" min="10" max="100" step="1" style="width: 70px;">
                                </label>
                                <label style="margin-left: 20px;">
                                    <?php _e('Max:', 'wp-hss'); ?>
                                    <input type="number" name="wp_hss_title_max_length" value="<?php echo esc_attr(isset($settings['title_max_length']) ? $settings['title_max_length'] : 60); ?>" min="20" max="120" step="1" style="width: 70px;">
                                </label>
                                <p class="description"><?php _e('Recommended title length in characters. Google typically displays the first 50-60 characters of a title.', 'wp-hss'); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Meta Description Length', 'wp-hss'); ?></th>
                            <td>
                                <label>
                                    <?php _e('Min:', 'wp-hss'); ?>
                                    <input type="number" name="wp_hss_meta_min_length" value="<?php echo esc_attr(isset($settings['meta_min_length']) ? $settings['meta_min_length'] : 120); ?>" min="50" max="300" step="1" style="width: 70px;">
                                </label>
                                <label style="margin-left: 20px;">
                                    <?php _e('Max:', 'wp-hss'); ?>
                                    <input type="number" name="wp_hss_meta_max_length" value="<?php echo esc_attr(isset($settings['meta_max_length']) ? $settings['meta_max_length'] : 160); ?>" min="100" max="320" step="1" style="width: 70px;">
                                </label>
                                <p class="description"><?php _e('Recommended meta description length in characters. Google typically displays about 155-160 characters.', 'wp-hss'); ?></p>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="wp-hss-settings-section">
                    <h2><?php _e('Advanced Scanning', 'wp-hss'); ?></h2>
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php _e('Enable Advanced Scanning', 'wp-hss'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="wp_hss_enable_advanced_scanning" value="1" <?php checked(isset($settings['enable_advanced_scanning']) ? $settings['enable_advanced_scanning'] : 0); ?>>
                                    <?php _e('Enable advanced scanning features', 'wp-hss'); ?>
                                </label>
                                <p class="description"><?php _e('Perform more comprehensive scans that check for additional SEO issues.', 'wp-hss'); ?></p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Content Analysis', 'wp-hss'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="wp_hss_enable_content_analysis" value="1" <?php checked(isset($settings['enable_content_analysis']) ? $settings['enable_content_analysis'] : 0); ?>>
                                    <?php _e('Analyze content quality and readability', 'wp-hss'); ?>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Keyword Analysis', 'wp-hss'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="wp_hss_enable_keyword_analysis" value="1" <?php checked(isset($settings['enable_keyword_analysis']) ? $settings['enable_keyword_analysis'] : 0); ?>>
                                    <?php _e('Check keyword density and placement', 'wp-hss'); ?>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Schema Validation', 'wp-hss'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="wp_hss_enable_schema_validation" value="1" <?php checked(isset($settings['enable_schema_validation']) ? $settings['enable_schema_validation'] : 0); ?>>
                                    <?php _e('Validate schema markup', 'wp-hss'); ?>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Mobile-Friendly Check', 'wp-hss'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="wp_hss_enable_mobile_friendly_check" value="1" <?php checked(isset($settings['enable_mobile_friendly_check']) ? $settings['enable_mobile_friendly_check'] : 0); ?>>
                                    <?php _e('Check if pages are mobile-friendly', 'wp-hss'); ?>
                                </label>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row"><?php _e('Performance Check', 'wp-hss'); ?></th>
                            <td>
                                <label>
                                    <input type="checkbox" name="wp_hss_enable_performance_check" value="1" <?php checked(isset($settings['enable_performance_check']) ? $settings['enable_performance_check'] : 0); ?>>
                                    <?php _e('Check page loading speed and performance', 'wp-hss'); ?>
                                </label>
                            </td>
                        </tr>
                    </table>
                </div>

                <p class="submit">
                    <input type="submit" name="wp_hss_save_settings" class="button button-primary" value="<?php _e('Save Settings', 'wp-hss'); ?>">
                </p>
            </form>
        </div>

        <script>
        jQuery(document).ready(function($) {
            // Toggle AI settings visibility
            function toggleAISettings() {
                if ($('#wp-hss-ai-enable').is(':checked')) {
                    $('.wp-hss-ai-setting').show();
                } else {
                    $('.wp-hss-ai-setting').hide();
                }
            }

            // Initial toggle
            toggleAISettings();

            // Toggle on change
            $('#wp-hss-ai-enable').on('change', toggleAISettings);

            // Handle API key validation
            $('#wp-hss-validate-api-key').on('click', function(e) {
                e.preventDefault();

                var apiKey = $('#wp-hss-ai-api-key').val();
                var provider = $('#wp-hss-ai-provider').val();

                if (!apiKey) {
                    $('#wp-hss-api-validation-result').html('<div class="notice notice-error inline"><p>Please enter an API key.</p></div>');
                    return;
                }

                $('#wp-hss-api-validation-result').html('<p><span class="spinner is-active"></span> Validating API key...</p>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'wp_hss_validate_api_key',
                        nonce: '<?php echo wp_create_nonce('wp_hss_validate_api_key'); ?>',
                        api_key: apiKey,
                        provider: provider
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#wp-hss-api-validation-result').html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
                        } else {
                            $('#wp-hss-api-validation-result').html('<div class="notice notice-error inline"><p>' + response.data.message + '</p></div>');
                        }
                    },
                    error: function() {
                        $('#wp-hss-api-validation-result').html('<div class="notice notice-error inline"><p>An error occurred while validating the API key.</p></div>');
                    }
                });
            });

            // Update available models based on selected provider
            $('#wp-hss-ai-provider').on('change', function() {
                var provider = $(this).val();
                var $modelSelect = $('#wp-hss-ai-model');

                // Clear existing options
                $modelSelect.empty();

                // Add options based on provider
                if (provider === 'openai') {
                    $modelSelect.append('<option value="gpt-4">GPT-4 (Best quality)</option>');
                    $modelSelect.append('<option value="gpt-3.5-turbo">GPT-3.5 Turbo (Faster)</option>');
                } else if (provider === 'anthropic') {
                    $modelSelect.append('<option value="claude-3-opus">Claude 3 Opus (Best quality)</option>');
                    $modelSelect.append('<option value="claude-3-sonnet">Claude 3 Sonnet (Balanced)</option>');
                    $modelSelect.append('<option value="claude-3-haiku">Claude 3 Haiku (Fastest)</option>');
                } else if (provider === 'google') {
                    $modelSelect.append('<option value="gemini-pro">Gemini Pro</option>');
                    $modelSelect.append('<option value="gemini-ultra">Gemini Ultra</option>');
                }
            });
        });
        </script>
        <?php
    }

    /**
     * Validate AI API key.
     *
     * @since    1.0.0
     * @param    string    $api_key     API key to validate.
     * @param    string    $provider    AI provider.
     * @return   array                  Validation result.
     */
    private function validate_ai_api_key($api_key, $provider = 'openai') {
        // For now, just do a basic validation
        // In a real implementation, this would make an actual API call to verify the key

        if (empty($api_key)) {
            return array(
                'success' => false,
                'message' => 'API key cannot be empty.'
            );
        }

        // Check key format based on provider
        if ($provider === 'openai' && strpos($api_key, 'sk-') !== 0) {
            return array(
                'success' => false,
                'message' => 'Invalid OpenAI API key format. Should start with "sk-".'
            );
        } else if ($provider === 'anthropic' && strpos($api_key, 'sk-ant-') !== 0) {
            return array(
                'success' => false,
                'message' => 'Invalid Anthropic API key format. Should start with "sk-ant-".'
            );
        }

        // In a real implementation, we would make an API call here to verify the key
        // For now, just return success if the key passes basic validation
        return array(
            'success' => true,
            'message' => 'API key validated successfully.'
        );
    }
}
