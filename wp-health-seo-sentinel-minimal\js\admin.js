/**
 * Admin JavaScript for WP Health & SEO Sentinel
 */
jQuery(document).ready(function($) {
    // Handle select all checkbox
    $('#select-all-options').on('change', function() {
        var isChecked = $(this).prop('checked');
        $('input[name="scan_option"]').prop('checked', isChecked);
    });

    // Update select all checkbox when individual options change
    $('input[name="scan_option"]').on('change', function() {
        var allChecked = $('input[name="scan_option"]').length === $('input[name="scan_option"]:checked').length;
        $('#select-all-options').prop('checked', allChecked);
    });

    // Handle scan button click
    $('#wp-hss-scan-button').on('click', function() {
        var $button = $(this);
        var $results = $('#wp-hss-scan-results');

        // Check if any scan options are selected
        if ($('input[name="scan_option"]:checked').length === 0) {
            alert('Please select at least one scan option.');
            return;
        }

        // Disable button and show loading state
        $button.prop('disabled', true).text(wp_hss.strings.scanning);

        // Show loading indicator with progress animation
        $results.html('<div class="wp-hss-loading">' +
            '<span class="wp-hss-spinner"></span> ' +
            '<div>' +
            '<div>' + wp_hss.strings.scanning + '</div>' +
            '<div class="wp-hss-scan-progress">' +
            '<div id="scan-progress-text">Initializing scan...</div>' +
            '<div class="wp-hss-progress-bar"><div class="wp-hss-progress-bar-inner"></div></div>' +
            '</div>' +
            '</div>' +
            '</div>');

        // Animate progress bar
        var progressBar = $('.wp-hss-progress-bar-inner');
        var progressText = $('#scan-progress-text');
        var scanSteps = [
            'Checking technical SEO...',
            'Analyzing meta tags...',
            'Evaluating content...',
            'Checking mobile friendliness...',
            'Analyzing performance...',
            'Checking schema markup...',
            'Verifying social media tags...',
            'Compiling results...'
        ];

        var currentStep = 0;
        var scanInterval = setInterval(function() {
            if (currentStep < scanSteps.length) {
                var progress = (currentStep + 1) / scanSteps.length * 100;
                progressBar.css('width', progress + '%');
                progressText.text(scanSteps[currentStep]);
                currentStep++;
            } else {
                clearInterval(scanInterval);
            }
        }, 800);

        // Get selected scan options
        var scanOptions = [];
        $('input[name="scan_option"]:checked').each(function() {
            scanOptions.push($(this).val());
        });

        // Get scan depth
        var scanDepth = $('#scan-depth').val();

        // Send AJAX request
        $.ajax({
            url: wp_hss.ajax_url,
            type: 'POST',
            data: {
                action: 'wp_hss_scan_site',
                nonce: wp_hss.nonce,
                scan_options: scanOptions,
                scan_depth: scanDepth
            },
            success: function(response) {
                if (response.success) {
                    // Update stats
                    updateStats(response.data.counts, response.data.health_score);

                    // Generate results HTML
                    var html = generateResultsHtml(response.data.results);

                    // Show results
                    $results.html(html);

                    // Initialize fix buttons
                    initFixButtons();
                } else {
                    // Show error
                    $results.html('<div class="wp-hss-error-box"><h3><span class="dashicons dashicons-warning"></span> ' + wp_hss.strings.scan_error + '</h3><p>' + response.data.message + '</p></div>');
                }

                // Reset button
                $button.prop('disabled', false).text('Run Scan Again');

                // Scroll to results
                $('html, body').animate({
                    scrollTop: $results.offset().top - 50
                }, 500);
            },
            error: function() {
                // Show error
                $results.html('<div class="wp-hss-error-box"><h3><span class="dashicons dashicons-warning"></span> ' + wp_hss.strings.scan_error + '</h3><p>An error occurred while scanning. Please try again.</p></div>');

                // Reset button
                $button.prop('disabled', false).text('Run Scan Again');
            }
        });
    });

    // Generate results HTML
    function generateResultsHtml(results) {
        var html = '';

        // Add summary
        var totalIssues = results.critical.length + results.warning.length + results.info.length;

        if (totalIssues === 0) {
            html += '<div class="card">';
            html += '<h2>Scan Results</h2>';
            html += '<div class="wp-hss-success-box">';
            html += '<h3><span class="dashicons dashicons-yes"></span> No Issues Found</h3>';
            html += '<p>Great job! Your site has no SEO issues.</p>';
            html += '</div>';
            html += '</div>';
            return html;
        }

        html += '<div class="card">';
        html += '<h2>Scan Results</h2>';
        html += '<p>We found the following SEO issues on your site:</p>';

        // Add tabs
        html += '<div class="wp-hss-tabs">';
        html += '<div class="wp-hss-tab active" data-tab="wp-hss-all-issues">All Issues (' + totalIssues + ')</div>';

        if (results.critical.length > 0) {
            html += '<div class="wp-hss-tab" data-tab="wp-hss-critical-issues">Critical (' + results.critical.length + ')</div>';
        }

        if (results.warning.length > 0) {
            html += '<div class="wp-hss-tab" data-tab="wp-hss-warning-issues">Warnings (' + results.warning.length + ')</div>';
        }

        if (results.info.length > 0) {
            html += '<div class="wp-hss-tab" data-tab="wp-hss-info-issues">Suggestions (' + results.info.length + ')</div>';
        }

        html += '</div>';

        // Add tab content
        html += '<div id="wp-hss-all-issues" class="wp-hss-tab-content active">';
        html += generateIssuesListHtml(results.critical, 'critical');
        html += generateIssuesListHtml(results.warning, 'warning');
        html += generateIssuesListHtml(results.info, 'info');
        html += '</div>';

        if (results.critical.length > 0) {
            html += '<div id="wp-hss-critical-issues" class="wp-hss-tab-content">';
            html += generateIssuesListHtml(results.critical, 'critical');
            html += '</div>';
        }

        if (results.warning.length > 0) {
            html += '<div id="wp-hss-warning-issues" class="wp-hss-tab-content">';
            html += generateIssuesListHtml(results.warning, 'warning');
            html += '</div>';
        }

        if (results.info.length > 0) {
            html += '<div id="wp-hss-info-issues" class="wp-hss-tab-content">';
            html += generateIssuesListHtml(results.info, 'info');
            html += '</div>';
        }

        html += '</div>';

        return html;
    }

    // Generate issues list HTML
    function generateIssuesListHtml(issues, severity) {
        if (issues.length === 0) {
            return '';
        }

        var html = '<div class="wp-hss-issues-list">';

        // Add heading
        var headingText = '';
        var headingIcon = '';

        switch (severity) {
            case 'critical':
                headingText = 'Critical Issues';
                headingIcon = 'warning';
                break;
            case 'warning':
                headingText = 'Warnings';
                headingIcon = 'flag';
                break;
            case 'info':
                headingText = 'Suggestions';
                headingIcon = 'info';
                break;
        }

        html += '<h3><span class="dashicons dashicons-' + headingIcon + '"></span> ' + headingText + '</h3>';

        // Add issues
        for (var i = 0; i < issues.length; i++) {
            var issue = issues[i];

            html += '<div class="wp-hss-issue-item">';
            html += '<div class="wp-hss-issue-header">';
            html += '<h4 class="wp-hss-issue-title">' + issue.title + '</h4>';
            html += '<span class="wp-hss-issue-severity" style="color: ' + (severity === 'critical' ? '#d63638' : (severity === 'warning' ? '#dba617' : '#2271b1')) + ';">' + severity.charAt(0).toUpperCase() + severity.slice(1) + '</span>';
            html += '</div>';

            html += '<div class="wp-hss-issue-content">';
            html += '<p>' + issue.description + '</p>';

            if (issue.details) {
                html += '<p><strong>Details:</strong> ' + issue.details + '</p>';
            }

            html += '</div>';

            html += '<div class="wp-hss-issue-actions">';

            if (issue.fixable) {
                html += '<button class="button button-primary wp-hss-fix-button" data-issue-id="' + issue.id + '" data-issue-type="' + issue.type + '">Fix Issue</button>';
            } else {
                html += '<button class="button" disabled>Manual Fix Required</button>';
            }

            html += '</div>';
            html += '</div>';
        }

        html += '</div>';

        return html;
    }

    // Initialize fix buttons
    function initFixButtons() {
        // Handle tab clicks
        $('.wp-hss-tab').on('click', function() {
            var tabId = $(this).data('tab');

            // Update active tab
            $('.wp-hss-tab').removeClass('active');
            $(this).addClass('active');

            // Show active tab content
            $('.wp-hss-tab-content').removeClass('active');
            $('#' + tabId).addClass('active');
        });

        // Handle fix button clicks
        $('.wp-hss-fix-button').on('click', function() {
            var $button = $(this);
            var issueId = $button.data('issue-id');
            var issueType = $button.data('issue-type');

            // Disable button and show loading state
            $button.prop('disabled', true).text(wp_hss.strings.fixing);

            // Send AJAX request
            $.ajax({
                url: wp_hss.ajax_url,
                type: 'POST',
                data: {
                    action: 'wp_hss_fix_issue',
                    nonce: wp_hss.nonce,
                    issue_id: issueId,
                    issue_type: issueType
                },
                success: function(response) {
                    if (response.success) {
                        // Show success state
                        $button.text(wp_hss.strings.fix_complete).addClass('button-disabled');

                        // Add success message
                        var $message = $('<div class="wp-hss-success-box" style="margin-top: 10px;"><p><span class="dashicons dashicons-yes"></span> ' + response.data.message + '</p></div>');

                        if (response.data.details) {
                            $message.append('<p>' + response.data.details + '</p>');
                        }

                        $button.closest('.wp-hss-issue-actions').after($message);
                    } else {
                        // Show error state
                        $button.text(wp_hss.strings.fix_error).removeClass('button-primary').addClass('button-secondary');

                        // Add error message
                        var $message = $('<div class="wp-hss-error-box" style="margin-top: 10px;"><p><span class="dashicons dashicons-warning"></span> ' + response.data.message + '</p></div>');
                        $button.closest('.wp-hss-issue-actions').after($message);

                        // Reset button after delay
                        setTimeout(function() {
                            $button.prop('disabled', false).text('Fix Issue').removeClass('button-secondary').addClass('button-primary');
                        }, 3000);
                    }
                },
                error: function() {
                    // Show error state
                    $button.text(wp_hss.strings.fix_error).removeClass('button-primary').addClass('button-secondary');

                    // Add error message
                    var $message = $('<div class="wp-hss-error-box" style="margin-top: 10px;"><p><span class="dashicons dashicons-warning"></span> An error occurred while fixing the issue. Please try again.</p></div>');
                    $button.closest('.wp-hss-issue-actions').after($message);

                    // Reset button after delay
                    setTimeout(function() {
                        $button.prop('disabled', false).text('Fix Issue').removeClass('button-secondary').addClass('button-primary');
                    }, 3000);
                }
            });
        });
    }

    // Update stats
    function updateStats(counts, healthScore) {
        // Update health score
        $('#wp-hss-health-score').text(healthScore);

        // Update health score color
        if (healthScore >= 80) {
            $('#wp-hss-health-score').css('color', '#00a32a');
        } else if (healthScore >= 50) {
            $('#wp-hss-health-score').css('color', '#dba617');
        } else {
            $('#wp-hss-health-score').css('color', '#d63638');
        }

        // Update issue counts
        $('#wp-hss-critical-count').text(counts.critical);
        $('#wp-hss-warning-count').text(counts.warning);
        $('#wp-hss-info-count').text(counts.info);
    }

    // Initialize tabs on settings page - using event delegation for better reliability
    $(document).on('click', '.wp-hss-tab', function(e) {
        e.preventDefault();
        e.stopPropagation();

        var tabId = $(this).data('tab');
        var $tabsContainer = $(this).closest('.wp-hss-tabs');
        var $tabContents;

        // If this is a subtab, only affect tabs within its container
        if ($tabsContainer.hasClass('wp-hss-subtabs')) {
            // For subtabs, find the parent container to scope the content selection
            var $parentContainer = $tabsContainer.closest('.wp-hss-tab-content, .card');

            // Update active tab within this subtab group only
            $tabsContainer.find('.wp-hss-tab').removeClass('active');
            $(this).addClass('active');

            // Show active tab content within this container only
            $parentContainer.find('.wp-hss-tab-content').removeClass('active');
            $('#' + tabId).addClass('active');
        } else {
            // For main tabs, affect all top-level tabs
            $('.wp-hss-tab').not('.wp-hss-subtabs .wp-hss-tab').removeClass('active');
            $(this).addClass('active');

            // Show active tab content (only top-level)
            $('.wp-hss-tab-content').not('.wp-hss-tab-content .wp-hss-tab-content').removeClass('active');
            $('#' + tabId).addClass('active');
        }

        return false;
    });
});
