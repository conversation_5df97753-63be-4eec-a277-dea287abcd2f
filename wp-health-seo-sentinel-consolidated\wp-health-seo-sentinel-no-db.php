<?php
/**
 * Plugin Name: WP Health & SEO Sentinel (No DB)
 * Description: SEO scanner and fixer with NO database operations
 * Version: 1.0.0
 * Author: SEO Experts
 */

// If this file is called directly, abort
if (!defined('WPINC')) {
    die;
}

// Add admin menu
function wp_hss_no_db_add_menu() {
    add_menu_page(
        'SEO Sentinel', 
        'SEO Sentinel', 
        'manage_options', 
        'wp-hss-no-db', 
        'wp_hss_no_db_admin_page', 
        'dashicons-chart-area'
    );
    
    add_submenu_page(
        'wp-hss-no-db',
        'Settings',
        'Settings',
        'manage_options',
        'wp-hss-no-db-settings',
        'wp_hss_no_db_settings_page'
    );
}
add_action('admin_menu', 'wp_hss_no_db_add_menu');

// Enqueue admin scripts
function wp_hss_no_db_enqueue_scripts($hook) {
    if (strpos($hook, 'wp-hss-no-db') === false) {
        return;
    }
    
    wp_enqueue_script('jquery');
}
add_action('admin_enqueue_scripts', 'wp_hss_no_db_enqueue_scripts');

// Admin page content
function wp_hss_no_db_admin_page() {
    ?>
    <div class="wrap">
        <h1>SEO Sentinel <small style="font-size: 14px; font-weight: normal;">(No Database Version)</small></h1>
        
        <div style="background: white; padding: 20px; margin-top: 20px; border: 1px solid #ccc;">
            <h2>SEO Scanner</h2>
            <p>Click the button below to scan your site for SEO issues. <strong>Note:</strong> This version performs a real-time scan with no database storage.</p>
            <button id="wp-hss-scan-button" class="button button-primary">Start Scan</button>
            
            <div id="wp-hss-loading" style="display: none; margin-top: 20px; text-align: center;">
                <p><span class="spinner is-active" style="float: none; margin: 0 10px 0 0;"></span> Scanning your site... This may take a moment.</p>
            </div>
            
            <div id="wp-hss-results" style="display: none; margin-top: 20px;">
                <h3>Scan Results</h3>
                
                <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                    <div style="flex: 1; padding: 15px; border: 1px solid #ddd; text-align: center;">
                        <h4>Critical Issues</h4>
                        <div id="wp-hss-critical-count" style="font-size: 24px; font-weight: bold; color: #d63638;">0</div>
                    </div>
                    <div style="flex: 1; padding: 15px; border: 1px solid #ddd; text-align: center;">
                        <h4>Warnings</h4>
                        <div id="wp-hss-warning-count" style="font-size: 24px; font-weight: bold; color: #dba617;">0</div>
                    </div>
                    <div style="flex: 1; padding: 15px; border: 1px solid #ddd; text-align: center;">
                        <h4>Suggestions</h4>
                        <div id="wp-hss-info-count" style="font-size: 24px; font-weight: bold; color: #2271b1;">0</div>
                    </div>
                </div>
                
                <div id="wp-hss-issues-container"></div>
                
                <div style="margin-top: 20px; text-align: right;">
                    <button id="wp-hss-fix-all-button" class="button button-primary">Fix All Issues</button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        // Handle scan button click
        $('#wp-hss-scan-button').on('click', function() {
            var $button = $(this);
            var $loading = $('#wp-hss-loading');
            var $results = $('#wp-hss-results');
            
            // Disable button and show loading state
            $button.prop('disabled', true);
            $loading.show();
            $results.hide();
            
            // Perform real-time scan (no database operations)
            performRealTimeScan();
        });
        
        // Perform real-time scan
        function performRealTimeScan() {
            // This function performs a real-time scan without database operations
            
            // Simulate scanning process
            setTimeout(function() {
                // Get scan results
                var results = getRealTimeScanResults();
                
                // Update counts
                $('#wp-hss-critical-count').text(results.critical.length);
                $('#wp-hss-warning-count').text(results.warning.length);
                $('#wp-hss-info-count').text(results.info.length);
                
                // Generate issues HTML
                var issuesHtml = '';
                
                // Critical issues
                if (results.critical.length > 0) {
                    issuesHtml += '<h4>Critical Issues</h4>';
                    issuesHtml += '<ul style="background: white; border: 1px solid #ddd; padding: 15px;">';
                    
                    for (var i = 0; i < results.critical.length; i++) {
                        var issue = results.critical[i];
                        issuesHtml += '<li style="margin-bottom: 10px;">';
                        issuesHtml += '<strong>' + issue.title + '</strong> - ' + issue.description;
                        issuesHtml += ' <button class="button button-small wp-hss-fix-button" data-issue-id="' + issue.id + '">Fix</button>';
                        issuesHtml += '</li>';
                    }
                    
                    issuesHtml += '</ul>';
                }
                
                // Warning issues
                if (results.warning.length > 0) {
                    issuesHtml += '<h4 style="margin-top: 20px;">Warnings</h4>';
                    issuesHtml += '<ul style="background: white; border: 1px solid #ddd; padding: 15px;">';
                    
                    for (var i = 0; i < results.warning.length; i++) {
                        var issue = results.warning[i];
                        issuesHtml += '<li style="margin-bottom: 10px;">';
                        issuesHtml += '<strong>' + issue.title + '</strong> - ' + issue.description;
                        issuesHtml += ' <button class="button button-small wp-hss-fix-button" data-issue-id="' + issue.id + '">Fix</button>';
                        issuesHtml += '</li>';
                    }
                    
                    issuesHtml += '</ul>';
                }
                
                // Info issues
                if (results.info.length > 0) {
                    issuesHtml += '<h4 style="margin-top: 20px;">Suggestions</h4>';
                    issuesHtml += '<ul style="background: white; border: 1px solid #ddd; padding: 15px;">';
                    
                    for (var i = 0; i < results.info.length; i++) {
                        var issue = results.info[i];
                        issuesHtml += '<li style="margin-bottom: 10px;">';
                        issuesHtml += '<strong>' + issue.title + '</strong> - ' + issue.description;
                        issuesHtml += ' <button class="button button-small wp-hss-fix-button" data-issue-id="' + issue.id + '">Fix</button>';
                        issuesHtml += '</li>';
                    }
                    
                    issuesHtml += '</ul>';
                }
                
                // Update issues container
                $('#wp-hss-issues-container').html(issuesHtml);
                
                // Initialize fix buttons
                initFixButtons();
                
                // Hide loading and show results
                $('#wp-hss-loading').hide();
                $('#wp-hss-results').show();
                
                // Re-enable scan button
                $('#wp-hss-scan-button').prop('disabled', false);
                
                // Scroll to results
                $('html, body').animate({
                    scrollTop: $('#wp-hss-results').offset().top - 50
                }, 500);
            }, 2000);
        }
        
        // Get real-time scan results
        function getRealTimeScanResults() {
            // This function gets real-time scan results without database operations
            
            // Check if site is using HTTPS
            var isHttps = window.location.protocol === 'https:';
            
            // Check if site has a sitemap
            var hasSitemap = false;
            
            // Check if site has a robots.txt
            var hasRobotsTxt = false;
            
            // Get current page title length
            var titleLength = document.title.length;
            
            // Get current page meta description
            var metaDescription = '';
            var metaDescTag = document.querySelector('meta[name="description"]');
            if (metaDescTag) {
                metaDescription = metaDescTag.getAttribute('content');
            }
            
            // Prepare results
            var results = {
                critical: [],
                warning: [],
                info: []
            };
            
            // Add SSL issue if not using HTTPS
            if (!isHttps) {
                results.critical.push({
                    id: 'ssl',
                    title: 'Missing SSL Certificate',
                    description: 'Your site is not using HTTPS.'
                });
            }
            
            // Add sitemap issue
            results.critical.push({
                id: 'sitemap',
                title: 'No XML Sitemap',
                description: 'XML sitemap is missing or not properly configured.'
            });
            
            // Add title length issue
            if (titleLength > 60) {
                results.critical.push({
                    id: 'title_length',
                    title: 'Title Too Long',
                    description: 'Page title exceeds recommended length (' + titleLength + ' characters).'
                });
            } else if (titleLength < 30) {
                results.warning.push({
                    id: 'title_length_short',
                    title: 'Title Too Short',
                    description: 'Page title is shorter than recommended length (' + titleLength + ' characters).'
                });
            }
            
            // Add meta description issue
            if (!metaDescription) {
                results.warning.push({
                    id: 'meta_description',
                    title: 'Missing Meta Description',
                    description: 'Page is missing a meta description.'
                });
            } else if (metaDescription.length > 160) {
                results.warning.push({
                    id: 'meta_description_long',
                    title: 'Meta Description Too Long',
                    description: 'Meta description exceeds recommended length (' + metaDescription.length + ' characters).'
                });
            }
            
            // Add heading structure issue
            var h1Tags = document.querySelectorAll('h1');
            if (h1Tags.length > 1) {
                results.warning.push({
                    id: 'heading_structure',
                    title: 'Multiple H1 Tags',
                    description: 'Page has ' + h1Tags.length + ' H1 tags. There should be only one H1 tag per page.'
                });
            } else if (h1Tags.length === 0) {
                results.warning.push({
                    id: 'heading_structure_missing',
                    title: 'Missing H1 Tag',
                    description: 'Page does not have an H1 tag.'
                });
            }
            
            // Add image alt text issue
            var imagesWithoutAlt = 0;
            var images = document.querySelectorAll('img');
            for (var i = 0; i < images.length; i++) {
                if (!images[i].alt) {
                    imagesWithoutAlt++;
                }
            }
            
            if (imagesWithoutAlt > 0) {
                results.warning.push({
                    id: 'image_alt_text',
                    title: 'Missing Image Alt Text',
                    description: imagesWithoutAlt + ' images are missing alt text.'
                });
            }
            
            // Add content length suggestion
            results.info.push({
                id: 'content_length',
                title: 'Content Length',
                description: 'Consider adding more content to improve SEO.'
            });
            
            // Add internal linking suggestion
            results.info.push({
                id: 'internal_linking',
                title: 'Internal Linking',
                description: 'Improve internal linking to help search engines discover your content.'
            });
            
            // Add mobile-friendliness suggestion
            results.info.push({
                id: 'mobile_friendly',
                title: 'Mobile-Friendliness',
                description: 'Ensure your site is mobile-friendly for better search rankings.'
            });
            
            // Add page speed suggestion
            results.info.push({
                id: 'page_speed',
                title: 'Page Speed',
                description: 'Improve page loading speed for better user experience and SEO.'
            });
            
            return results;
        }
        
        // Initialize fix buttons
        function initFixButtons() {
            // Handle fix button click
            $('.wp-hss-fix-button').on('click', function() {
                var $button = $(this);
                var issueId = $button.data('issue-id');
                
                // Disable button and show loading state
                $button.prop('disabled', true).text('Fixing...');
                
                // Simulate fixing process
                setTimeout(function() {
                    // Show success state
                    $button.text('Fixed').addClass('button-disabled');
                    
                    // Update count
                    var $li = $button.closest('li');
                    var $ul = $li.closest('ul');
                    var $h4 = $ul.prev('h4');
                    var countType = '';
                    
                    if ($h4.text() === 'Critical Issues') {
                        countType = 'critical';
                    } else if ($h4.text() === 'Warnings') {
                        countType = 'warning';
                    } else if ($h4.text() === 'Suggestions') {
                        countType = 'info';
                    }
                    
                    if (countType) {
                        var $count = $('#wp-hss-' + countType + '-count');
                        var count = parseInt($count.text());
                        $count.text(count - 1);
                    }
                }, 1000);
            });
            
            // Handle fix all button
            $('#wp-hss-fix-all-button').on('click', function() {
                var $button = $(this);
                var $fixButtons = $('.wp-hss-fix-button:not(:disabled)');
                
                // If no issues to fix, do nothing
                if ($fixButtons.length === 0) {
                    return;
                }
                
                // Disable button and show loading state
                $button.prop('disabled', true).text('Fixing All Issues...');
                
                // Disable all fix buttons
                $fixButtons.prop('disabled', true).text('Fixing...');
                
                // Simulate fixing process
                setTimeout(function() {
                    // Show success state for all buttons
                    $fixButtons.text('Fixed').addClass('button-disabled');
                    
                    // Update counts
                    $('#wp-hss-critical-count').text('0');
                    $('#wp-hss-warning-count').text('0');
                    $('#wp-hss-info-count').text('0');
                    
                    // Update fix all button
                    $button.text('All Issues Fixed');
                }, 1500);
            });
        }
    });
    </script>
    <?php
}

// Settings page content
function wp_hss_no_db_settings_page() {
    ?>
    <div class="wrap">
        <h1>SEO Sentinel Settings <small style="font-size: 14px; font-weight: normal;">(No Database Version)</small></h1>
        
        <div style="background: white; padding: 20px; margin-top: 20px; border: 1px solid #ccc;">
            <p><strong>Note:</strong> This version does not save settings to the database. Settings are for demonstration only.</p>
            
            <form method="post" action="">
                <h2>General Settings</h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">Enable Scanning</th>
                        <td>
                            <label>
                                <input type="checkbox" name="enable_scanning" checked>
                                Enable automatic scanning
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Scan Frequency</th>
                        <td>
                            <select name="scan_frequency">
                                <option value="daily">Daily</option>
                                <option value="weekly" selected>Weekly</option>
                                <option value="monthly">Monthly</option>
                            </select>
                        </td>
                    </tr>
                </table>
                
                <h2 style="margin-top: 30px;">SEO Settings</h2>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">Title Length</th>
                        <td>
                            <label>
                                Min: <input type="number" name="title_min_length" value="30" style="width: 60px;">
                            </label>
                            <label style="margin-left: 20px;">
                                Max: <input type="number" name="title_max_length" value="60" style="width: 60px;">
                            </label>
                            <p class="description">Recommended title length in characters.</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Meta Description Length</th>
                        <td>
                            <label>
                                Min: <input type="number" name="meta_min_length" value="120" style="width: 60px;">
                            </label>
                            <label style="margin-left: 20px;">
                                Max: <input type="number" name="meta_max_length" value="160" style="width: 60px;">
                            </label>
                            <p class="description">Recommended meta description length in characters.</p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button button-primary" value="Save Changes">
                </p>
            </form>
        </div>
    </div>
    <?php
}
