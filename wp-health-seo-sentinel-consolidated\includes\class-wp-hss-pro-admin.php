<?php
/**
 * Admin class for WP Health & SEO Sentinel Pro
 * Handles admin UI without database operations
 */
class WP_HSS_Pro_Admin {
    /**
     * Initialize the admin functionality
     */
    public function init() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Add settings link to plugins page
        add_filter('plugin_action_links_' . plugin_basename(WP_HSS_PRO_PLUGIN_FILE), array($this, 'add_settings_link'));
        
        // Enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }
    
    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Add main menu
        add_menu_page(
            'WP Health & SEO Sentinel Pro',
            'SEO Sentinel',
            'manage_options',
            'wp-hss-pro',
            array($this, 'render_dashboard_page'),
            'dashicons-chart-area',
            100
        );
        
        // Add submenu pages
        add_submenu_page(
            'wp-hss-pro',
            'Dashboard',
            'Dashboard',
            'manage_options',
            'wp-hss-pro',
            array($this, 'render_dashboard_page')
        );
        
        add_submenu_page(
            'wp-hss-pro',
            'Scanner',
            'Scanner',
            'manage_options',
            'wp-hss-pro-scanner',
            array($this, 'render_scanner_page')
        );
        
        add_submenu_page(
            'wp-hss-pro',
            'Tools',
            'Tools',
            'manage_options',
            'wp-hss-pro-tools',
            array($this, 'render_tools_page')
        );
        
        add_submenu_page(
            'wp-hss-pro',
            'Settings',
            'Settings',
            'manage_options',
            'wp-hss-pro-settings',
            array($this, 'render_settings_page')
        );
    }
    
    /**
     * Add settings link to plugins page
     */
    public function add_settings_link($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=wp-hss-pro-settings') . '">' . __('Settings', 'wp-hss-pro') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_scripts($hook) {
        // Only load on our plugin pages
        if (strpos($hook, 'wp-hss-pro') === false) {
            return;
        }
        
        // Enqueue styles
        wp_enqueue_style('wp-hss-pro-admin', WP_HSS_PRO_PLUGIN_URL . 'admin/css/wp-hss-pro-admin.css', array(), WP_HSS_PRO_VERSION);
        
        // Enqueue scripts
        wp_enqueue_script('wp-hss-pro-admin', WP_HSS_PRO_PLUGIN_URL . 'admin/js/wp-hss-pro-admin.js', array('jquery'), WP_HSS_PRO_VERSION, true);
        
        // Localize script
        wp_localize_script('wp-hss-pro-admin', 'wp_hss_pro', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wp_hss_pro_nonce'),
            'strings' => array(
                'scanning' => __('Scanning...', 'wp-hss-pro'),
                'scan_complete' => __('Scan Complete', 'wp-hss-pro'),
                'scan_error' => __('Scan Error', 'wp-hss-pro'),
                'fixing' => __('Fixing...', 'wp-hss-pro'),
                'fix_complete' => __('Fixed', 'wp-hss-pro'),
                'fix_error' => __('Fix Error', 'wp-hss-pro'),
                'fix_all' => __('Fix All Issues', 'wp-hss-pro'),
                'fixing_all' => __('Fixing All Issues...', 'wp-hss-pro'),
                'all_fixed' => __('All Issues Fixed', 'wp-hss-pro'),
                'confirm_fix_all' => __('Are you sure you want to fix all issues? This action cannot be undone.', 'wp-hss-pro')
            )
        ));
    }
    
    /**
     * Render dashboard page
     */
    public function render_dashboard_page() {
        include WP_HSS_PRO_PLUGIN_DIR . 'admin/partials/dashboard.php';
    }
    
    /**
     * Render scanner page
     */
    public function render_scanner_page() {
        include WP_HSS_PRO_PLUGIN_DIR . 'admin/partials/scanner.php';
    }
    
    /**
     * Render tools page
     */
    public function render_tools_page() {
        include WP_HSS_PRO_PLUGIN_DIR . 'admin/partials/tools.php';
    }
    
    /**
     * Render settings page
     */
    public function render_settings_page() {
        include WP_HSS_PRO_PLUGIN_DIR . 'admin/partials/settings.php';
    }
    
    /**
     * Get health score
     */
    public function get_health_score() {
        // In a real implementation, this would calculate the health score based on issues
        // For this no-database version, we'll return a random score
        return rand(50, 95);
    }
    
    /**
     * Get issue counts
     */
    public function get_issue_counts() {
        // In a real implementation, this would count issues by severity
        // For this no-database version, we'll return random counts
        return array(
            'critical' => rand(0, 5),
            'warning' => rand(2, 8),
            'info' => rand(3, 10),
            'total' => rand(5, 20)
        );
    }
    
    /**
     * Get recent posts
     */
    public function get_recent_posts($count = 5) {
        $args = array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'posts_per_page' => $count,
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        $posts = get_posts($args);
        $recent_posts = array();
        
        foreach ($posts as $post) {
            $recent_posts[] = array(
                'id' => $post->ID,
                'title' => $post->post_title,
                'url' => get_permalink($post->ID),
                'date' => get_the_date('M j, Y', $post->ID),
                'has_issues' => (bool) rand(0, 1)
            );
        }
        
        return $recent_posts;
    }
    
    /**
     * Get recent pages
     */
    public function get_recent_pages($count = 5) {
        $args = array(
            'post_type' => 'page',
            'post_status' => 'publish',
            'posts_per_page' => $count,
            'orderby' => 'date',
            'order' => 'DESC'
        );
        
        $pages = get_posts($args);
        $recent_pages = array();
        
        foreach ($pages as $page) {
            $recent_pages[] = array(
                'id' => $page->ID,
                'title' => $page->post_title,
                'url' => get_permalink($page->ID),
                'date' => get_the_date('M j, Y', $page->ID),
                'has_issues' => (bool) rand(0, 1)
            );
        }
        
        return $recent_pages;
    }
    
    /**
     * Get site info
     */
    public function get_site_info() {
        // Get WordPress version
        $wp_version = get_bloginfo('version');
        
        // Get theme info
        $theme = wp_get_theme();
        $theme_name = $theme->get('Name');
        $theme_version = $theme->get('Version');
        
        // Get plugin count
        $all_plugins = get_plugins();
        $active_plugins = get_option('active_plugins');
        $plugin_count = count($all_plugins);
        $active_plugin_count = count($active_plugins);
        
        // Get post counts
        $post_count = wp_count_posts('post');
        $page_count = wp_count_posts('page');
        
        // Get SSL status
        $is_ssl = is_ssl();
        
        return array(
            'wp_version' => $wp_version,
            'theme_name' => $theme_name,
            'theme_version' => $theme_version,
            'plugin_count' => $plugin_count,
            'active_plugin_count' => $active_plugin_count,
            'post_count' => $post_count->publish,
            'page_count' => $page_count->publish,
            'is_ssl' => $is_ssl
        );
    }
    
    /**
     * Get SEO plugins
     */
    public function get_seo_plugins() {
        $seo_plugins = array(
            'wordpress-seo/wp-seo.php' => 'Yoast SEO',
            'seo-by-rank-math/rank-math.php' => 'Rank Math SEO',
            'all-in-one-seo-pack/all_in_one_seo_pack.php' => 'All in One SEO Pack',
            'wp-seopress/seopress.php' => 'SEOPress',
            'the-seo-framework/the-seo-framework.php' => 'The SEO Framework'
        );
        
        $active_seo_plugins = array();
        
        foreach ($seo_plugins as $plugin_file => $plugin_name) {
            if (is_plugin_active($plugin_file)) {
                $active_seo_plugins[] = $plugin_name;
            }
        }
        
        return $active_seo_plugins;
    }
    
    /**
     * Get performance plugins
     */
    public function get_performance_plugins() {
        $performance_plugins = array(
            'wp-super-cache/wp-cache.php' => 'WP Super Cache',
            'w3-total-cache/w3-total-cache.php' => 'W3 Total Cache',
            'wp-fastest-cache/wpFastestCache.php' => 'WP Fastest Cache',
            'litespeed-cache/litespeed-cache.php' => 'LiteSpeed Cache',
            'autoptimize/autoptimize.php' => 'Autoptimize',
            'wp-rocket/wp-rocket.php' => 'WP Rocket'
        );
        
        $active_performance_plugins = array();
        
        foreach ($performance_plugins as $plugin_file => $plugin_name) {
            if (is_plugin_active($plugin_file)) {
                $active_performance_plugins[] = $plugin_name;
            }
        }
        
        return $active_performance_plugins;
    }
}
