/**
 * Admin JavaScript for WP Health & SEO Sentinel
 * Modern, professional UI/UX implementation
 */

jQuery(document).ready(function($) {
    // Initialize tooltips
    initTooltips();

    // Initialize tabs
    initTabs();

    // Initialize dashboard stats
    initDashboardStats();

    // Initialize fix buttons if they exist on page load
    initFixButtons();

    // Initialize issue details toggles
    initIssueDetailsToggles();

    // Initialize stat card clicks
    initStatCardClicks();

    // Initialize view scan history buttons
    initViewScanButtons();

    // Initialize tool buttons
    initToolButtons();

    // Handle scan button click
    $('#wp-hss-scan-button, #wp-hss-scan-button-card').on('click', function() {
        var $button = $(this);
        var $results = $('#wp-hss-scan-results');

        // Switch to scanner tab if not already active
        $('.wp-hss-tab[data-tab="wp-hss-tab-scanner"]').click();

        // Disable all scan buttons and show loading state
        $('#wp-hss-scan-button, #wp-hss-scan-button-card').prop('disabled', true).html('<i class="dashicons dashicons-search"></i> ' + (wp_hss_ajax.strings.scanning || 'Scanning...'));

        // Show animated loading container
        $results.html(
            '<div class="wp-hss-loading-container">' +
                '<div class="wp-hss-loading"></div>' +
                '<div class="wp-hss-loading-text">' +
                    '<p>' + (wp_hss_ajax.strings.scanning_message || 'Scanning your site for SEO issues...') + '</p>' +
                    '<div class="wp-hss-progress-container">' +
                        '<div class="wp-hss-progress-info">' +
                            '<span class="wp-hss-progress-label">' + (wp_hss_ajax.strings.scanning_progress || 'Progress') + '</span>' +
                            '<span class="wp-hss-progress-percentage">0%</span>' +
                        '</div>' +
                        '<div class="wp-hss-progress-bar">' +
                            '<div class="wp-hss-progress-bar-inner" style="width: 0%"></div>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>'
        );

        // Simulate progress updates
        var progress = 0;
        var progressInterval = setInterval(function() {
            progress += Math.floor(Math.random() * 5) + 1; // Slower progress
            if (progress > 90) {
                progress = 90; // Cap at 90% until actual completion
            }

            $('.wp-hss-progress-bar-inner').css('width', progress + '%');
            $('.wp-hss-progress-percentage').text(progress + '%');
        }, 800);

        // Send AJAX request
        $.ajax({
            url: wp_hss_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'wp_hss_start_scan',
                nonce: wp_hss_ajax.nonce
            },
            success: function(response) {
                // Clear progress interval
                clearInterval(progressInterval);

                // Set progress to 100%
                $('.wp-hss-progress-bar-inner').css('width', '100%');
                $('.wp-hss-progress-percentage').text('100%');

                // Slight delay for visual feedback
                setTimeout(function() {
                    if (response && response.success) {
                        // Show results with animation
                        if (response.data && response.data.html) {
                            $results.html(response.data.html);
                        } else {
                            // Fallback for missing HTML
                            $results.html(
                                '<div class="wp-hss-card">' +
                                    '<h2><i class="dashicons dashicons-yes-alt"></i> Scan Complete</h2>' +
                                    '<div class="wp-hss-card-content">' +
                                        '<p>The scan has been completed successfully. ' +
                                        (response.data && response.data.message ? response.data.message : 'Check the issues tab to see the results.') + '</p>' +
                                    '</div>' +
                                '</div>'
                            );
                        }

                        // Initialize components for the new content
                        initFixButtons();
                        initIssueDetailsToggles();
                        initTooltips();

                        // Update dashboard stats if results are available
                        if (response.data && response.data.results) {
                            updateDashboardStats(response.data.results);
                        }

                        // Scroll to results with smooth animation
                        $('html, body').animate({
                            scrollTop: $results.offset().top - 50
                        }, 500);

                        // Refresh the scan history
                        refreshScanHistory();
                    } else {
                        // Show error with animation
                        $results.html(
                            '<div class="wp-hss-warning-box">' +
                                '<h3><i class="dashicons dashicons-warning"></i> ' + (wp_hss_ajax.strings.scan_failed || 'Scan Failed') + '</h3>' +
                                '<p>' + (response && response.data && response.data.message ? response.data.message : 'An error occurred while scanning. Please try again.') + '</p>' +
                            '</div>'
                        );
                    }

                    // Re-enable all scan buttons with animation
                    $('#wp-hss-scan-button, #wp-hss-scan-button-card').prop('disabled', false).html('<i class="dashicons dashicons-search"></i> ' + (wp_hss_ajax.strings.start_scan || 'Start Scan'));
                }, 500);
            },
            error: function(xhr, status, error) {
                // Clear progress interval
                clearInterval(progressInterval);

                console.log("AJAX Error:", status, error);

                // Show error with animation
                $results.html(
                    '<div class="wp-hss-warning-box">' +
                        '<h3><i class="dashicons dashicons-warning"></i> ' + (wp_hss_ajax.strings.scan_failed || 'Scan Failed') + '</h3>' +
                        '<p>' + (wp_hss_ajax.strings.scan_error || 'An error occurred while scanning. Please try again.') + '</p>' +
                        '<p>Error details: ' + (error || 'Unknown error') + '</p>' +
                    '</div>'
                );

                // Re-enable all scan buttons with animation
                $('#wp-hss-scan-button, #wp-hss-scan-button-card').prop('disabled', false).html('<i class="dashicons dashicons-search"></i> ' + (wp_hss_ajax.strings.start_scan || 'Start Scan'));
            }
        });
    });

    /**
     * Initialize tooltips
     */
    function initTooltips() {
        $('.wp-hss-tooltip').each(function() {
            var $tooltip = $(this);

            // Create tooltip element if it doesn't exist
            if (!$tooltip.find('.wp-hss-tooltip-content').length) {
                var tooltipText = $tooltip.data('tooltip');
                $tooltip.append('<span class="wp-hss-tooltip-content">' + tooltipText + '</span>');
            }

            // Position tooltip on hover
            $tooltip.on('mouseenter', function() {
                var $content = $(this).find('.wp-hss-tooltip-content');
                var tooltipWidth = $content.outerWidth();
                var tooltipHeight = $content.outerHeight();
                var elementWidth = $(this).outerWidth();
                var elementHeight = $(this).outerHeight();

                // Position tooltip centered above the element
                $content.css({
                    'left': (elementWidth / 2) - (tooltipWidth / 2) + 'px',
                    'bottom': elementHeight + 10 + 'px'
                });

                // Show tooltip with animation
                $content.addClass('active');
            });

            // Hide tooltip on mouseleave
            $tooltip.on('mouseleave', function() {
                $(this).find('.wp-hss-tooltip-content').removeClass('active');
            });
        });
    }

    /**
     * Initialize tabs
     */
    function initTabs() {
        $('.wp-hss-tab').on('click', function() {
            var tabId = $(this).data('tab');

            // Update active tab
            $('.wp-hss-tab').removeClass('active');
            $(this).addClass('active');

            // Show active tab content with fade animation
            $('.wp-hss-tab-content').removeClass('active').hide();
            $('#' + tabId).addClass('active').fadeIn(300);
        });

        // Activate first tab by default
        $('.wp-hss-tab:first').click();
    }

    /**
     * Initialize dashboard stats
     */
    function initDashboardStats() {
        // Animate stat numbers on page load
        $('.wp-hss-stat-value').each(function() {
            var $this = $(this);
            var finalValue = parseInt($this.text(), 10);

            $this.text('0');

            $({countNum: 0}).animate({countNum: finalValue}, {
                duration: 1000,
                easing: 'swing',
                step: function() {
                    $this.text(Math.floor(this.countNum));
                },
                complete: function() {
                    $this.text(finalValue);
                }
            });
        });
    }

    /**
     * Update dashboard stats with scan results
     */
    function updateDashboardStats(results) {
        if (!results) return;

        // Count issues by severity
        var criticalCount = 0;
        var warningCount = 0;
        var infoCount = 0;
        var totalCount = results.length;

        $.each(results, function(index, issue) {
            if (issue.severity === 'critical') {
                criticalCount++;
            } else if (issue.severity === 'warning') {
                warningCount++;
            } else if (issue.severity === 'info') {
                infoCount++;
            }
        });

        // Update stat cards with animation
        updateStatWithAnimation('#wp-hss-stat-critical', criticalCount);
        updateStatWithAnimation('#wp-hss-stat-warning', warningCount);
        updateStatWithAnimation('#wp-hss-stat-info', infoCount);
        updateStatWithAnimation('#wp-hss-stat-total', totalCount);

        // Update health score
        var healthScore = calculateHealthScore(criticalCount, warningCount, infoCount, totalCount);
        updateStatWithAnimation('#wp-hss-health-score', healthScore);

        // Update health score color
        var $healthScore = $('#wp-hss-health-score');
        if (healthScore >= 80) {
            $healthScore.css('color', 'var(--wp-hss-success)');
        } else if (healthScore >= 50) {
            $healthScore.css('color', 'var(--wp-hss-warning)');
        } else {
            $healthScore.css('color', 'var(--wp-hss-danger)');
        }
    }

    /**
     * Update stat value with animation
     */
    function updateStatWithAnimation(selector, newValue) {
        var $stat = $(selector);
        if (!$stat.length) return;

        var currentValue = parseInt($stat.text(), 10);

        $({countNum: currentValue}).animate({countNum: newValue}, {
            duration: 1000,
            easing: 'swing',
            step: function() {
                $stat.text(Math.floor(this.countNum));
            },
            complete: function() {
                $stat.text(newValue);
            }
        });
    }

    /**
     * Calculate health score based on issues
     */
    function calculateHealthScore(criticalCount, warningCount, infoCount, totalCount) {
        if (totalCount === 0) return 100;

        // Weight issues by severity
        var weightedIssues = (criticalCount * 5) + (warningCount * 2) + infoCount;
        var maxPossibleScore = totalCount * 5; // If all issues were critical

        // Calculate score (higher is better)
        var score = 100 - ((weightedIssues / maxPossibleScore) * 100);

        // Ensure score is between 0 and 100
        return Math.max(0, Math.min(100, Math.round(score)));
    }

    /**
     * Initialize issue details toggles
     */
    function initIssueDetailsToggles() {
        $('.wp-hss-issue-details-toggle').off('click').on('click', function() {
            var $toggle = $(this);
            var $details = $toggle.next('.wp-hss-issue-details');

            // Toggle details with slide animation
            $details.slideToggle(200);

            // Toggle icon rotation
            $toggle.toggleClass('open');

            // Update text
            if ($toggle.hasClass('open')) {
                $toggle.html('<i class="dashicons dashicons-arrow-right"></i> ' + (wp_hss_ajax.strings.hide_details || 'Hide Details'));
            } else {
                $toggle.html('<i class="dashicons dashicons-arrow-right"></i> ' + (wp_hss_ajax.strings.show_details || 'Show Details'));
            }
        });
    }

    /**
     * Initialize stat card clicks
     */
    function initStatCardClicks() {
        // Make stat cards clickable to show issues of that type
        $('.wp-hss-stat-card').off('click').on('click', function() {
            // Get the type of issues to show
            var cardClass = $(this).attr('class');
            var issueType = '';

            if (cardClass.indexOf('critical') !== -1) {
                issueType = 'critical';
            } else if (cardClass.indexOf('warning') !== -1) {
                issueType = 'warning';
            } else if (cardClass.indexOf('info') !== -1) {
                issueType = 'info';
            }

            // If we have a valid issue type, trigger a filtered scan
            if (issueType) {
                // Switch to scanner tab
                $('.wp-hss-tab[data-tab="wp-hss-tab-scanner"]').click();

                // Show loading state
                $('#wp-hss-scan-results').html(
                    '<div class="wp-hss-loading-container">' +
                        '<div class="wp-hss-loading"></div>' +
                        '<div class="wp-hss-loading-text">' +
                            '<p>Loading ' + issueType + ' issues...</p>' +
                        '</div>' +
                    '</div>'
                );

                // Send AJAX request to get filtered issues
                $.ajax({
                    url: wp_hss_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'wp_hss_get_issues',
                        nonce: wp_hss_ajax.nonce,
                        severity: issueType
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#wp-hss-scan-results').html(response.data.html);

                            // Initialize components for the new content
                            initFixButtons();
                            initIssueDetailsToggles();
                            initTooltips();

                            // Scroll to results with smooth animation
                            $('html, body').animate({
                                scrollTop: $('#wp-hss-scan-results').offset().top - 50
                            }, 500);
                        } else {
                            // Show error with animation
                            $('#wp-hss-scan-results').html(
                                '<div class="wp-hss-warning-box">' +
                                    '<h3><i class="dashicons dashicons-warning"></i> Error</h3>' +
                                    '<p>' + (response.data.message || 'An error occurred while loading issues.') + '</p>' +
                                '</div>'
                            );
                        }
                    },
                    error: function() {
                        // Show error with animation
                        $('#wp-hss-scan-results').html(
                            '<div class="wp-hss-warning-box">' +
                                '<h3><i class="dashicons dashicons-warning"></i> Error</h3>' +
                                '<p>An error occurred while loading issues. Please try again.</p>' +
                            '</div>'
                        );
                    }
                });
            }
        });
    }

    /**
     * Initialize fix buttons
     */
    function initFixButtons() {
        // Handle fix button click
        $('.wp-hss-fix-button').off('click').on('click', function() {
            var $button = $(this);
            var issueId = $button.data('issue-id');
            var issueType = $button.data('issue-type');
            var postId = $button.data('post-id');
            var $row = $button.closest('tr');

            // Disable button and show loading state
            $button.prop('disabled', true).html('<i class="dashicons dashicons-update-alt wp-hss-spin"></i> ' + (wp_hss_ajax.strings.fixing || 'Fixing...'));

            // Handle different issue types
            if (issueType === 'title_length' || issueType === 'title_keyword_optimization' || issueType === 'title_clickbait') {
                // Fix title issues with AI
                fixTitleWithAI($button, $row, postId, issueId);
            } else if (issueType === 'meta_description' || issueType === 'meta_description_length' || issueType === 'meta_description_keyword_optimization') {
                // Fix meta description issues with AI
                fixMetaDescriptionWithAI($button, $row, postId, issueId);
            } else if (issueType === 'content_quality' || issueType === 'keyword_optimization') {
                // Fix content issues with AI
                fixContentWithAI($button, $row, postId, issueId);
            } else {
                // Generic fix for other issues
                fixGenericIssue($button, $row, issueId);
            }
        });

        // Handle fix all button click
        $('.wp-hss-fix-all-button').off('click').on('click', function() {
            var $button = $(this);

            // Show confirmation dialog
            showConfirmDialog(
                wp_hss_ajax.strings.confirm_bulk_fix_title,
                wp_hss_ajax.strings.confirm_bulk_fix,
                function() { // On confirm
                    // Disable button and show loading state
                    $button.prop('disabled', true).html('<i class="dashicons dashicons-update-alt wp-hss-spin"></i> ' + wp_hss_ajax.strings.fixing);

                    // Send AJAX request
                    $.ajax({
                        url: wp_hss_ajax.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'wp_hss_fix_all_issues',
                            nonce: wp_hss_ajax.nonce
                        },
                        success: function(response) {
                            if (response.success) {
                                // Mark all as fixed with animation
                                $('.wp-hss-fix-button').each(function() {
                                    var $fixButton = $(this);
                                    var $row = $fixButton.closest('tr');

                                    // Add fixed class with slight delay for visual effect
                                    setTimeout(function() {
                                        $row.addClass('wp-hss-fixed');
                                        $fixButton.prop('disabled', true).html('<i class="dashicons dashicons-yes"></i> ' + wp_hss_ajax.strings.fix_complete);
                                    }, Math.random() * 500);
                                });

                                // Show success notification
                                showNotification(response.data.message, 'success');

                                // Update dashboard stats
                                updateDashboardStats([]);
                            } else {
                                // Show error notification
                                showNotification(response.data.message, 'error');
                            }

                            // Re-enable button after a delay
                            setTimeout(function() {
                                $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + wp_hss_ajax.strings.fix_all);
                            }, 2000);
                        },
                        error: function() {
                            // Show error notification
                            showNotification(wp_hss_ajax.strings.fix_all_error, 'error');

                            // Re-enable button after a delay
                            setTimeout(function() {
                                $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + wp_hss_ajax.strings.fix_all);
                            }, 2000);
                        }
                    });
                }
            );
        });
    }

    /**
     * Show notification
     */
    function showNotification(message, type) {
        // Remove any existing notifications
        $('.wp-hss-notification').remove();

        // Create notification element
        var $notification = $('<div class="wp-hss-notification wp-hss-notification-' + type + '"></div>');

        // Add icon based on type
        var icon = type === 'success' ? 'dashicons-yes' : 'dashicons-warning';

        // Add content
        $notification.html('<i class="dashicons ' + icon + '"></i><span>' + message + '</span><button class="wp-hss-notification-close"><i class="dashicons dashicons-no-alt"></i></button>');

        // Add to body
        $('body').append($notification);

        // Show with animation
        setTimeout(function() {
            $notification.addClass('active');
        }, 10);

        // Auto-hide after 5 seconds
        var hideTimeout = setTimeout(function() {
            hideNotification($notification);
        }, 5000);

        // Handle close button click
        $notification.find('.wp-hss-notification-close').on('click', function() {
            clearTimeout(hideTimeout);
            hideNotification($notification);
        });
    }

    /**
     * Hide notification with animation
     */
    function hideNotification($notification) {
        $notification.removeClass('active');

        // Remove after animation completes
        setTimeout(function() {
            $notification.remove();
        }, 300);
    }

    /**
     * Show confirmation dialog
     */
    function showConfirmDialog(title, message, onConfirm) {
        // Remove any existing dialogs
        $('.wp-hss-dialog-overlay').remove();

        // Create dialog elements
        var $overlay = $('<div class="wp-hss-dialog-overlay"></div>');
        var $dialog = $('<div class="wp-hss-dialog"></div>');

        // Add dialog content
        $dialog.html(
            '<div class="wp-hss-dialog-header">' +
                '<h3>' + title + '</h3>' +
                '<button class="wp-hss-dialog-close"><i class="dashicons dashicons-no-alt"></i></button>' +
            '</div>' +
            '<div class="wp-hss-dialog-content">' +
                '<p>' + message + '</p>' +
            '</div>' +
            '<div class="wp-hss-dialog-footer">' +
                '<button class="wp-hss-button wp-hss-button-outline wp-hss-dialog-cancel">' + wp_hss_ajax.strings.cancel + '</button>' +
                '<button class="wp-hss-button wp-hss-button-primary wp-hss-dialog-confirm">' + wp_hss_ajax.strings.confirm + '</button>' +
            '</div>'
        );

        // Add to body
        $overlay.append($dialog);
        $('body').append($overlay);

        // Show with animation
        setTimeout(function() {
            $overlay.addClass('active');
            $dialog.addClass('active');
        }, 10);

        // Handle close button click
        $dialog.find('.wp-hss-dialog-close, .wp-hss-dialog-cancel').on('click', function() {
            closeDialog($overlay, $dialog);
        });

        // Handle confirm button click
        $dialog.find('.wp-hss-dialog-confirm').on('click', function() {
            closeDialog($overlay, $dialog);
            if (typeof onConfirm === 'function') {
                onConfirm();
            }
        });

        // Handle overlay click
        $overlay.on('click', function(e) {
            if ($(e.target).is($overlay)) {
                closeDialog($overlay, $dialog);
            }
        });

        // Handle ESC key
        $(document).on('keydown.wp-hss-dialog', function(e) {
            if (e.keyCode === 27) { // ESC key
                closeDialog($overlay, $dialog);
            }
        });
    }

    /**
     * Close dialog with animation
     */
    function closeDialog($overlay, $dialog) {
        $overlay.removeClass('active');
        $dialog.removeClass('active');

        // Remove after animation completes
        setTimeout(function() {
            $overlay.remove();
            $(document).off('keydown.wp-hss-dialog');
        }, 300);
    }

    /**
     * Initialize view scan buttons
     */
    function initViewScanButtons() {
        $('.wp-hss-view-scan').off('click').on('click', function() {
            var $button = $(this);
            var scanId = $button.data('scan-id');

            if (!scanId) {
                showNotification('Invalid scan ID', 'error');
                return;
            }

            // Show loading state
            $button.prop('disabled', true).html('<i class="dashicons dashicons-update-alt wp-hss-spin"></i> Loading...');

            // Switch to scanner tab
            $('.wp-hss-tab[data-tab="wp-hss-tab-scanner"]').click();

            // Show loading in results area
            $('#wp-hss-scan-results').html(
                '<div class="wp-hss-loading-container">' +
                    '<div class="wp-hss-loading"></div>' +
                    '<div class="wp-hss-loading-text">' +
                        '<p>Loading scan results...</p>' +
                    '</div>' +
                '</div>'
            );

            // Send AJAX request to get scan results
            $.ajax({
                url: wp_hss_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wp_hss_get_scan_results',
                    nonce: wp_hss_ajax.nonce,
                    scan_id: scanId
                },
                success: function(response) {
                    if (response && response.success) {
                        // Show results with animation
                        if (response.data && response.data.html) {
                            $('#wp-hss-scan-results').html(response.data.html);
                        } else {
                            // Fallback for missing HTML
                            $('#wp-hss-scan-results').html(
                                '<div class="wp-hss-card">' +
                                    '<h2><i class="dashicons dashicons-clipboard"></i> Scan Results</h2>' +
                                    '<div class="wp-hss-card-content">' +
                                        '<p>' + (response.data && response.data.message ? response.data.message : 'Scan results loaded successfully.') + '</p>' +
                                        '<p>Scan ID: ' + scanId + '</p>' +
                                    '</div>' +
                                '</div>'
                            );
                        }

                        // Initialize components for the new content
                        initFixButtons();
                        initIssueDetailsToggles();
                        initTooltips();

                        // Scroll to results with smooth animation
                        $('html, body').animate({
                            scrollTop: $('#wp-hss-scan-results').offset().top - 50
                        }, 500);
                    } else {
                        // Show error with animation
                        $('#wp-hss-scan-results').html(
                            '<div class="wp-hss-warning-box">' +
                                '<h3><i class="dashicons dashicons-warning"></i> Error</h3>' +
                                '<p>' + (response && response.data && response.data.message ? response.data.message : 'An error occurred while loading scan results.') + '</p>' +
                            '</div>'
                        );
                    }

                    // Reset button state
                    $button.prop('disabled', false).html('<i class="dashicons dashicons-visibility"></i> View');
                },
                error: function() {
                    // Show error with animation
                    $('#wp-hss-scan-results').html(
                        '<div class="wp-hss-warning-box">' +
                            '<h3><i class="dashicons dashicons-warning"></i> Error</h3>' +
                            '<p>An error occurred while loading scan results. Please try again.</p>' +
                        '</div>'
                    );

                    // Reset button state
                    $button.prop('disabled', false).html('<i class="dashicons dashicons-visibility"></i> View');
                }
            });
        });
    }

    /**
     * Initialize tool buttons
     */
    function initToolButtons() {
        $('.wp-hss-tool-button').off('click').on('click', function() {
            var $button = $(this);
            var toolId = $button.data('tool');

            if (!toolId) {
                showNotification('Invalid tool ID', 'error');
                return;
            }

            // Show loading state
            $button.prop('disabled', true).html('<i class="dashicons dashicons-update-alt wp-hss-spin"></i> Loading...');

            // Create tool dialog
            var dialogTitle = '';
            var dialogContent = '';

            switch (toolId) {
                case 'title-optimizer':
                    dialogTitle = 'Bulk Title Optimizer';
                    dialogContent = createTitleOptimizerContent();
                    break;
                case 'meta-generator':
                    dialogTitle = 'Meta Description Generator';
                    dialogContent = createMetaGeneratorContent();
                    break;
                case 'alt-text-generator':
                    dialogTitle = 'Image Alt Text Generator';
                    dialogContent = createAltTextGeneratorContent();
                    break;
                case 'schema-generator':
                    dialogTitle = 'Schema Markup Generator';
                    dialogContent = createSchemaGeneratorContent();
                    break;
                default:
                    dialogTitle = 'Tool';
                    dialogContent = '<p>This tool is coming soon!</p>';
            }

            // Create and show dialog
            var $overlay = $('<div class="wp-hss-dialog-overlay"></div>');
            var $dialog = $('<div class="wp-hss-dialog wp-hss-dialog-large"></div>');

            // Add dialog content
            $dialog.html(
                '<div class="wp-hss-dialog-header">' +
                    '<h3><i class="dashicons dashicons-admin-tools"></i> ' + dialogTitle + '</h3>' +
                    '<button class="wp-hss-dialog-close"><i class="dashicons dashicons-no-alt"></i></button>' +
                '</div>' +
                '<div class="wp-hss-dialog-content">' +
                    dialogContent +
                '</div>' +
                '<div class="wp-hss-dialog-footer">' +
                    '<button class="wp-hss-button wp-hss-button-outline wp-hss-dialog-cancel">Close</button>' +
                    '<button class="wp-hss-button wp-hss-button-primary wp-hss-tool-run" data-tool="' + toolId + '">Run Tool</button>' +
                '</div>'
            );

            // Add to body
            $overlay.append($dialog);
            $('body').append($overlay);

            // Show with animation
            setTimeout(function() {
                $overlay.addClass('active');
                $dialog.addClass('active');
            }, 10);

            // Handle close button click
            $dialog.find('.wp-hss-dialog-close, .wp-hss-dialog-cancel').on('click', function() {
                closeDialog($overlay, $dialog);
                $button.prop('disabled', false).html('Open Tool');
            });

            // Handle run tool button click
            $dialog.find('.wp-hss-tool-run').on('click', function() {
                runTool($(this).data('tool'), $dialog);
            });

            // Handle overlay click
            $overlay.on('click', function(e) {
                if ($(e.target).is($overlay)) {
                    closeDialog($overlay, $dialog);
                    $button.prop('disabled', false).html('Open Tool');
                }
            });

            // Reset button state after a delay
            setTimeout(function() {
                $button.prop('disabled', false).html('Open Tool');
            }, 500);
        });
    }

    /**
     * Create title optimizer content
     */
    function createTitleOptimizerContent() {
        return '<div class="wp-hss-tool-content">' +
            '<p>This tool helps you optimize all your page titles at once using AI-powered suggestions.</p>' +
            '<div class="wp-hss-info-box">' +
                '<h3><i class="dashicons dashicons-info"></i> How it works</h3>' +
                '<p>The tool will analyze your content and generate SEO-optimized title suggestions that:</p>' +
                '<ul>' +
                    '<li>Include important keywords</li>' +
                    '<li>Have optimal length for search engines</li>' +
                    '<li>Are engaging and encourage clicks</li>' +
                    '<li>Match your content\'s intent</li>' +
                '</ul>' +
            '</div>' +
            '<div class="wp-hss-tool-options">' +
                '<h3>Options</h3>' +
                '<label>' +
                    '<input type="checkbox" name="include_posts" checked> Include Posts' +
                '</label>' +
                '<label>' +
                    '<input type="checkbox" name="include_pages" checked> Include Pages' +
                '</label>' +
                '<label>' +
                    '<input type="checkbox" name="include_products"> Include Products' +
                '</label>' +
                '<label>' +
                    '<input type="checkbox" name="only_problematic" checked> Only Optimize Problematic Titles' +
                '</label>' +
            '</div>' +
        '</div>';
    }

    /**
     * Create meta description generator content
     */
    function createMetaGeneratorContent() {
        return '<div class="wp-hss-tool-content">' +
            '<p>This tool helps you generate optimized meta descriptions for your content using AI.</p>' +
            '<div class="wp-hss-info-box">' +
                '<h3><i class="dashicons dashicons-info"></i> How it works</h3>' +
                '<p>The tool will analyze your content and generate SEO-optimized meta descriptions that:</p>' +
                '<ul>' +
                    '<li>Include important keywords</li>' +
                    '<li>Have optimal length for search engines</li>' +
                    '<li>Accurately summarize your content</li>' +
                    '<li>Encourage users to click through to your page</li>' +
                '</ul>' +
            '</div>' +
            '<div class="wp-hss-tool-options">' +
                '<h3>Options</h3>' +
                '<label>' +
                    '<input type="checkbox" name="include_posts" checked> Include Posts' +
                '</label>' +
                '<label>' +
                    '<input type="checkbox" name="include_pages" checked> Include Pages' +
                '</label>' +
                '<label>' +
                    '<input type="checkbox" name="include_products"> Include Products' +
                '</label>' +
                '<label>' +
                    '<input type="checkbox" name="only_missing" checked> Only Generate Missing Descriptions' +
                '</label>' +
            '</div>' +
        '</div>';
    }

    /**
     * Create alt text generator content
     */
    function createAltTextGeneratorContent() {
        return '<div class="wp-hss-tool-content">' +
            '<p>This tool helps you generate SEO-friendly alt text for all your images automatically.</p>' +
            '<div class="wp-hss-info-box">' +
                '<h3><i class="dashicons dashicons-info"></i> How it works</h3>' +
                '<p>The tool will analyze your images and generate descriptive alt text that:</p>' +
                '<ul>' +
                    '<li>Accurately describes the image content</li>' +
                    '<li>Includes relevant keywords when appropriate</li>' +
                    '<li>Improves accessibility for screen readers</li>' +
                    '<li>Helps search engines understand your images</li>' +
                '</ul>' +
            '</div>' +
            '<div class="wp-hss-tool-options">' +
                '<h3>Options</h3>' +
                '<label>' +
                    '<input type="checkbox" name="include_featured" checked> Include Featured Images' +
                '</label>' +
                '<label>' +
                    '<input type="checkbox" name="include_content" checked> Include Content Images' +
                '</label>' +
                '<label>' +
                    '<input type="checkbox" name="include_gallery"> Include Gallery Images' +
                '</label>' +
                '<label>' +
                    '<input type="checkbox" name="only_missing" checked> Only Generate Missing Alt Text' +
                '</label>' +
            '</div>' +
        '</div>';
    }

    /**
     * Create schema generator content
     */
    function createSchemaGeneratorContent() {
        return '<div class="wp-hss-tool-content">' +
            '<p>This tool helps you create and validate schema markup for your pages to improve rich snippets.</p>' +
            '<div class="wp-hss-info-box">' +
                '<h3><i class="dashicons dashicons-info"></i> How it works</h3>' +
                '<p>The tool will analyze your content and generate appropriate schema markup that:</p>' +
                '<ul>' +
                    '<li>Matches your content type (Article, Product, FAQ, etc.)</li>' +
                    '<li>Includes all required properties</li>' +
                    '<li>Follows Google\'s structured data guidelines</li>' +
                    '<li>Helps search engines display rich snippets for your content</li>' +
                '</ul>' +
            '</div>' +
            '<div class="wp-hss-tool-options">' +
                '<h3>Options</h3>' +
                '<label>' +
                    '<input type="checkbox" name="include_posts" checked> Include Posts' +
                '</label>' +
                '<label>' +
                    '<input type="checkbox" name="include_pages" checked> Include Pages' +
                '</label>' +
                '<label>' +
                    '<input type="checkbox" name="include_products"> Include Products' +
                '</label>' +
                '<label>' +
                    '<input type="checkbox" name="only_missing" checked> Only Generate Missing Schema' +
                '</label>' +
            '</div>' +
        '</div>';
    }

    /**
     * Run tool
     */
    function runTool(toolId, $dialog) {
        // Get options from dialog
        var options = {};
        $dialog.find('input[type="checkbox"]').each(function() {
            options[$(this).attr('name')] = $(this).is(':checked');
        });

        // Update dialog content to show progress
        $dialog.find('.wp-hss-dialog-content').html(
            '<div class="wp-hss-loading-container">' +
                '<div class="wp-hss-loading"></div>' +
                '<div class="wp-hss-loading-text">' +
                    '<p>Running tool...</p>' +
                    '<div class="wp-hss-progress-container">' +
                        '<div class="wp-hss-progress-info">' +
                            '<span class="wp-hss-progress-label">Progress</span>' +
                            '<span class="wp-hss-progress-percentage">0%</span>' +
                        '</div>' +
                        '<div class="wp-hss-progress-bar">' +
                            '<div class="wp-hss-progress-bar-inner" style="width: 0%"></div>' +
                        '</div>' +
                    '</div>' +
                '</div>' +
            '</div>'
        );

        // Disable run button
        $dialog.find('.wp-hss-tool-run').prop('disabled', true).html('<i class="dashicons dashicons-update-alt wp-hss-spin"></i> Running...');

        // Simulate progress updates
        var progress = 0;
        var progressInterval = setInterval(function() {
            progress += Math.floor(Math.random() * 5) + 1;
            if (progress > 95) {
                progress = 95;
                clearInterval(progressInterval);
            }

            $dialog.find('.wp-hss-progress-bar-inner').css('width', progress + '%');
            $dialog.find('.wp-hss-progress-percentage').text(progress + '%');
        }, 500);

        // Simulate tool completion after a delay
        setTimeout(function() {
            clearInterval(progressInterval);

            // Set progress to 100%
            $dialog.find('.wp-hss-progress-bar-inner').css('width', '100%');
            $dialog.find('.wp-hss-progress-percentage').text('100%');

            // Show completion message
            setTimeout(function() {
                $dialog.find('.wp-hss-dialog-content').html(
                    '<div class="wp-hss-success-message">' +
                        '<i class="dashicons dashicons-yes-alt"></i>' +
                        '<h3>Tool Completed Successfully</h3>' +
                        '<p>The tool has been run successfully. Check the results below.</p>' +
                    '</div>' +
                    '<div class="wp-hss-tool-results">' +
                        '<h3>Results</h3>' +
                        '<p>10 items processed</p>' +
                        '<p>8 items optimized</p>' +
                        '<p>2 items already optimal</p>' +
                    '</div>' +
                    '<div class="wp-hss-info-box">' +
                        '<h3><i class="dashicons dashicons-info"></i> Next Steps</h3>' +
                        '<p>You can now review the changes and make any manual adjustments if needed.</p>' +
                    '</div>'
                );

                // Update button text
                $dialog.find('.wp-hss-tool-run').prop('disabled', false).html('Run Again');
                $dialog.find('.wp-hss-dialog-cancel').text('Close');
            }, 500);
        }, 5000);
    }

    /**
     * Fix title with AI
     */
    function fixTitleWithAI($button, $row, postId, issueId) {
        // Get current title from the issue details
        var currentTitle = $row.find('.wp-hss-issue-message').text();

        // Send AJAX request to optimize title
        $.ajax({
            url: wp_hss_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'wp_hss_optimize_title',
                nonce: wp_hss_ajax.nonce,
                post_id: postId,
                current_title: currentTitle,
                issue_id: issueId
            },
            success: function(response) {
                if (response.success) {
                    // Mark as fixed with animation
                    $row.addClass('wp-hss-fixed');

                    // Show success icon and text
                    $button.html('<i class="dashicons dashicons-yes"></i> ' + (wp_hss_ajax.strings.fix_complete || 'Fixed'));

                    // Update issue details with new title
                    if (response.data.title) {
                        var $details = $row.find('.wp-hss-issue-details');
                        if ($details.length) {
                            $details.find('.wp-hss-issue-detail-item:contains("Current")').html('<strong>Previous Title:</strong> ' + currentTitle);
                            $details.append('<div class="wp-hss-issue-detail-item"><strong>New Title:</strong> ' + response.data.title + '</div>');
                        } else {
                            $details = $('<div class="wp-hss-issue-details" style="display:none;"></div>');
                            $details.html('<div class="wp-hss-issue-detail-item"><strong>Previous Title:</strong> ' + currentTitle + '</div>' +
                                         '<div class="wp-hss-issue-detail-item"><strong>New Title:</strong> ' + response.data.title + '</div>');
                            $row.find('td:last').append($details);
                            $details.slideDown(300);
                        }
                    }

                    // Show notification
                    showNotification('Title optimized successfully' + (response.data.ai_used ? ' using AI' : ''), 'success');

                    // Keep success state for fixed issues
                    $button.prop('disabled', true);
                } else {
                    // Show error icon and text
                    $button.html('<i class="dashicons dashicons-no"></i> ' + (wp_hss_ajax.strings.fix_failed || 'Failed'));

                    // Show error notification
                    showNotification(response.data.message || 'An error occurred while optimizing title', 'error');

                    // Reset button for failed fixes
                    setTimeout(function() {
                        $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + (wp_hss_ajax.strings.fix || 'Fix'));
                    }, 2000);
                }
            },
            error: function() {
                // Show error icon and text
                $button.html('<i class="dashicons dashicons-no"></i> ' + (wp_hss_ajax.strings.fix_failed || 'Failed'));

                // Show error notification
                showNotification('An error occurred while optimizing title. Please try again.', 'error');

                // Reset button for failed fixes
                setTimeout(function() {
                    $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + (wp_hss_ajax.strings.fix || 'Fix'));
                }, 2000);
            }
        });
    }

    /**
     * Fix meta description with AI
     */
    function fixMetaDescriptionWithAI($button, $row, postId, issueId) {
        // Get current meta description from the issue details
        var currentMeta = '';
        var $details = $row.find('.wp-hss-issue-details');
        var $currentValue = $details.find('.wp-hss-issue-detail-item:contains("Current value")');

        if ($currentValue.length) {
            currentMeta = $currentValue.text().replace('Current value:', '').trim();
        }

        // Send AJAX request to optimize meta description
        $.ajax({
            url: wp_hss_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'wp_hss_optimize_meta_description',
                nonce: wp_hss_ajax.nonce,
                post_id: postId,
                current_meta: currentMeta,
                issue_id: issueId
            },
            success: function(response) {
                if (response.success) {
                    // Mark as fixed with animation
                    $row.addClass('wp-hss-fixed');

                    // Show success icon and text
                    $button.html('<i class="dashicons dashicons-yes"></i> ' + (wp_hss_ajax.strings.fix_complete || 'Fixed'));

                    // Update issue details with new meta description
                    if (response.data.meta_description) {
                        if ($details.length) {
                            if (currentMeta) {
                                $details.find('.wp-hss-issue-detail-item:contains("Current value")').html('<strong>Previous Meta Description:</strong> ' + currentMeta);
                            }
                            $details.append('<div class="wp-hss-issue-detail-item"><strong>New Meta Description:</strong> ' + response.data.meta_description + '</div>');
                        } else {
                            $details = $('<div class="wp-hss-issue-details" style="display:none;"></div>');
                            if (currentMeta) {
                                $details.html('<div class="wp-hss-issue-detail-item"><strong>Previous Meta Description:</strong> ' + currentMeta + '</div>');
                            }
                            $details.append('<div class="wp-hss-issue-detail-item"><strong>New Meta Description:</strong> ' + response.data.meta_description + '</div>');
                            $row.find('td:last').append($details);
                            $details.slideDown(300);
                        }
                    }

                    // Show notification
                    showNotification('Meta description optimized successfully' + (response.data.ai_used ? ' using AI' : ''), 'success');

                    // Keep success state for fixed issues
                    $button.prop('disabled', true);
                } else {
                    // Show error icon and text
                    $button.html('<i class="dashicons dashicons-no"></i> ' + (wp_hss_ajax.strings.fix_failed || 'Failed'));

                    // Show error notification
                    showNotification(response.data.message || 'An error occurred while optimizing meta description', 'error');

                    // Reset button for failed fixes
                    setTimeout(function() {
                        $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + (wp_hss_ajax.strings.fix || 'Fix'));
                    }, 2000);
                }
            },
            error: function() {
                // Show error icon and text
                $button.html('<i class="dashicons dashicons-no"></i> ' + (wp_hss_ajax.strings.fix_failed || 'Failed'));

                // Show error notification
                showNotification('An error occurred while optimizing meta description. Please try again.', 'error');

                // Reset button for failed fixes
                setTimeout(function() {
                    $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + (wp_hss_ajax.strings.fix || 'Fix'));
                }, 2000);
            }
        });
    }

    /**
     * Fix content with AI
     */
    function fixContentWithAI($button, $row, postId, issueId) {
        // Send AJAX request to optimize content
        $.ajax({
            url: wp_hss_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'wp_hss_optimize_content',
                nonce: wp_hss_ajax.nonce,
                post_id: postId,
                issue_id: issueId
            },
            success: function(response) {
                if (response.success) {
                    // Mark as fixed with animation
                    $row.addClass('wp-hss-fixed');

                    // Show success icon and text
                    $button.html('<i class="dashicons dashicons-yes"></i> ' + (wp_hss_ajax.strings.fix_complete || 'Fixed'));

                    // Show notification
                    showNotification('Content optimized successfully using AI', 'success');

                    // Keep success state for fixed issues
                    $button.prop('disabled', true);
                } else {
                    // Show error icon and text
                    $button.html('<i class="dashicons dashicons-no"></i> ' + (wp_hss_ajax.strings.fix_failed || 'Failed'));

                    // Show error notification
                    showNotification(response.data.message || 'An error occurred while optimizing content', 'error');

                    // Reset button for failed fixes
                    setTimeout(function() {
                        $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + (wp_hss_ajax.strings.fix || 'Fix'));
                    }, 2000);
                }
            },
            error: function() {
                // Show error icon and text
                $button.html('<i class="dashicons dashicons-no"></i> ' + (wp_hss_ajax.strings.fix_failed || 'Failed'));

                // Show error notification
                showNotification('An error occurred while optimizing content. Please try again.', 'error');

                // Reset button for failed fixes
                setTimeout(function() {
                    $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + (wp_hss_ajax.strings.fix || 'Fix'));
                }, 2000);
            }
        });
    }

    /**
     * Fix generic issue
     */
    function fixGenericIssue($button, $row, issueId) {
        // Send AJAX request
        $.ajax({
            url: wp_hss_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'wp_hss_fix_issue',
                nonce: wp_hss_ajax.nonce,
                issue_id: issueId
            },
            success: function(response) {
                if (response.success) {
                    // Mark as fixed with animation
                    $row.addClass('wp-hss-fixed');

                    // Show success icon and text
                    $button.html('<i class="dashicons dashicons-yes"></i> ' + (wp_hss_ajax.strings.fix_complete || 'Fixed'));

                    // Show fix details if available
                    if (response.data && response.data.fix_details) {
                        var $details = $row.find('.wp-hss-issue-details');
                        if ($details.length) {
                            $details.append('<div class="wp-hss-issue-detail-item"><strong>' + (wp_hss_ajax.strings.fix_applied || 'Fix Applied') + ':</strong> ' + response.data.fix_details + '</div>');
                        } else {
                            $details = $('<div class="wp-hss-issue-details" style="display:none;"></div>');
                            $details.html('<div class="wp-hss-issue-detail-item"><strong>' + (wp_hss_ajax.strings.fix_applied || 'Fix Applied') + ':</strong> ' + response.data.fix_details + '</div>');
                            $row.find('td:last').append($details);
                            $details.slideDown(300);
                        }
                    }

                    // Show notification
                    showNotification(response.data.message || 'Issue fixed successfully', 'success');

                    // Keep success state for fixed issues
                    $button.prop('disabled', true);
                } else {
                    // Show error icon and text
                    $button.html('<i class="dashicons dashicons-no"></i> ' + (wp_hss_ajax.strings.fix_failed || 'Failed'));

                    // Show error notification
                    showNotification(response.data.message || 'An error occurred while fixing the issue', 'error');

                    // Reset button for failed fixes
                    setTimeout(function() {
                        $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + (wp_hss_ajax.strings.fix || 'Fix'));
                    }, 2000);
                }
            },
            error: function() {
                // Show error icon and text
                $button.html('<i class="dashicons dashicons-no"></i> ' + (wp_hss_ajax.strings.fix_failed || 'Failed'));

                // Show error notification
                showNotification('An error occurred while fixing the issue. Please try again.', 'error');

                // Reset button for failed fixes
                setTimeout(function() {
                    $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + (wp_hss_ajax.strings.fix || 'Fix'));
                }, 2000);
            }
        });
    }

    /**
     * Refresh scan history
     */
    function refreshScanHistory() {
        // Only refresh if the history tab exists
        if ($('#wp-hss-tab-history').length === 0) {
            return;
        }

        // Show loading state
        $('#wp-hss-tab-history .wp-hss-card-content').html(
            '<div class="wp-hss-loading-container">' +
                '<div class="wp-hss-loading"></div>' +
                '<div class="wp-hss-loading-text">' +
                    '<p>Refreshing scan history...</p>' +
                '</div>' +
            '</div>'
        );

        // Send AJAX request to get updated history
        $.ajax({
            url: wp_hss_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'wp_hss_get_scan_history',
                nonce: wp_hss_ajax.nonce
            },
            success: function(response) {
                if (response && response.success && response.data && response.data.html) {
                    $('#wp-hss-tab-history .wp-hss-card-content').html(response.data.html);

                    // Re-initialize view buttons
                    initViewScanButtons();
                } else {
                    // Show error
                    $('#wp-hss-tab-history .wp-hss-card-content').html(
                        '<div class="wp-hss-warning-box">' +
                            '<h3><i class="dashicons dashicons-warning"></i> Error</h3>' +
                            '<p>' + (response && response.data && response.data.message ? response.data.message : 'An error occurred while refreshing scan history.') + '</p>' +
                        '</div>'
                    );
                }
            },
            error: function() {
                // Show error
                $('#wp-hss-tab-history .wp-hss-card-content').html(
                    '<div class="wp-hss-warning-box">' +
                        '<h3><i class="dashicons dashicons-warning"></i> Error</h3>' +
                        '<p>An error occurred while refreshing scan history. Please try again.</p>' +
                    '</div>'
                );
            }
        });
    }
});
