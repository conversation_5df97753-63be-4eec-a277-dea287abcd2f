/**
 * Admin JavaScript for WP Health & SEO Sentinel
 */
jQuery(document).ready(function($) {
    // Tab navigation
    $('.wp-hss-tab').on('click', function() {
        var tabId = $(this).data('tab');
        
        // Update active tab
        $('.wp-hss-tab').removeClass('active');
        $(this).addClass('active');
        
        // Show active tab content
        $('.wp-hss-tab-content').removeClass('active');
        $('#' + tabId).addClass('active');
    });
    
    // Handle scan button click
    $('#wp-hss-scan-button, #wp-hss-scan-button-card').on('click', function() {
        var $button = $(this);
        var $results = $('#wp-hss-scan-results');
        
        // Disable button and show loading state
        $button.prop('disabled', true).html('<i class="dashicons dashicons-update wp-hss-spin"></i> ' + wp_hss_ajax.strings.scanning);
        
        // Send AJAX request
        $.ajax({
            url: wp_hss_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'wp_hss_start_scan',
                nonce: wp_hss_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Show results
                    $results.html(response.data.html);
                    
                    // Update stats
                    updateStats(response.data.count);
                    
                    // Initialize fix buttons
                    initFixButtons();
                } else {
                    // Show error
                    $results.html('<div class="wp-hss-info-box" style="border-left-color: #d63638;"><h3><i class="dashicons dashicons-warning"></i> Error</h3><p>' + response.data.message + '</p></div>');
                }
                
                // Re-enable button
                $button.prop('disabled', false).html('<i class="dashicons dashicons-search"></i> ' + ($(this).attr('id') === 'wp-hss-scan-button-card' ? 'Start Comprehensive Scan' : 'Start Scan'));
                
                // Scroll to results
                $('html, body').animate({
                    scrollTop: $results.offset().top - 50
                }, 500);
            },
            error: function() {
                // Show error
                $results.html('<div class="wp-hss-info-box" style="border-left-color: #d63638;"><h3><i class="dashicons dashicons-warning"></i> Error</h3><p>An error occurred while scanning. Please try again.</p></div>');
                
                // Re-enable button
                $button.prop('disabled', false).html('<i class="dashicons dashicons-search"></i> ' + ($(this).attr('id') === 'wp-hss-scan-button-card' ? 'Start Comprehensive Scan' : 'Start Scan'));
            }
        });
    });
    
    // Initialize fix buttons
    function initFixButtons() {
        // Handle fix button click
        $('.wp-hss-fix-button').on('click', function() {
            var $button = $(this);
            var issueId = $button.data('issue-id');
            var issueType = $button.data('issue-type');
            var postId = $button.data('post-id');
            
            // Disable button and show loading state
            $button.prop('disabled', true).html('<i class="dashicons dashicons-update wp-hss-spin"></i> ' + wp_hss_ajax.strings.fixing);
            
            // Send AJAX request
            $.ajax({
                url: wp_hss_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wp_hss_fix_issue',
                    nonce: wp_hss_ajax.nonce,
                    issue_id: issueId,
                    issue_type: issueType,
                    post_id: postId
                },
                success: function(response) {
                    if (response.success) {
                        // Show success icon and text
                        $button.html('<i class="dashicons dashicons-yes"></i> ' + wp_hss_ajax.strings.fix_complete);
                        
                        // Add success message
                        var $message = $('<div class="wp-hss-fix-message" style="margin-top: 10px; color: #00a32a;"><i class="dashicons dashicons-yes"></i> ' + response.data.message + '</div>');
                        $button.parent().append($message);
                        
                        // Fade out message after 3 seconds
                        setTimeout(function() {
                            $message.fadeOut(500, function() {
                                $(this).remove();
                            });
                        }, 3000);
                        
                        // Update issue count
                        updateIssueCount(issueType);
                    } else {
                        // Show error icon and text
                        $button.html('<i class="dashicons dashicons-no"></i> ' + wp_hss_ajax.strings.fix_failed);
                        
                        // Add error message
                        var $message = $('<div class="wp-hss-fix-message" style="margin-top: 10px; color: #d63638;"><i class="dashicons dashicons-warning"></i> ' + response.data.message + '</div>');
                        $button.parent().append($message);
                        
                        // Reset button for failed fixes after a delay
                        setTimeout(function() {
                            $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + wp_hss_ajax.strings.fix);
                            
                            // Fade out message
                            $message.fadeOut(500, function() {
                                $(this).remove();
                            });
                        }, 3000);
                    }
                },
                error: function() {
                    // Show error icon and text
                    $button.html('<i class="dashicons dashicons-no"></i> ' + wp_hss_ajax.strings.fix_failed);
                    
                    // Add error message
                    var $message = $('<div class="wp-hss-fix-message" style="margin-top: 10px; color: #d63638;"><i class="dashicons dashicons-warning"></i> ' + wp_hss_ajax.strings.fix_error + '</div>');
                    $button.parent().append($message);
                    
                    // Reset button for failed fixes after a delay
                    setTimeout(function() {
                        $button.prop('disabled', false).html('<i class="dashicons dashicons-admin-tools"></i> ' + wp_hss_ajax.strings.fix);
                        
                        // Fade out message
                        $message.fadeOut(500, function() {
                            $(this).remove();
                        });
                    }, 3000);
                }
            });
        });
        
        // Handle fix all button
        $('#wp-hss-fix-all-button').on('click', function() {
            var $button = $(this);
            var $fixButtons = $('.wp-hss-fix-button:not(:disabled)');
            
            // If no issues to fix, do nothing
            if ($fixButtons.length === 0) {
                return;
            }
            
            // Disable button and show loading state
            $button.prop('disabled', true).text('Fixing All Issues...');
            
            // Disable all fix buttons
            $fixButtons.prop('disabled', true);
            
            // Send AJAX request
            $.ajax({
                url: wp_hss_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'wp_hss_fix_all_issues',
                    nonce: wp_hss_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Update all fix buttons to show fixed state
                        $fixButtons.each(function() {
                            $(this).html('<i class="dashicons dashicons-yes"></i> ' + wp_hss_ajax.strings.fix_complete);
                        });
                        
                        // Update fix all button
                        $button.text('All Issues Fixed');
                        
                        // Add success message
                        var $message = $('<div class="wp-hss-fix-message" style="margin-top: 10px; color: #00a32a;"><i class="dashicons dashicons-yes"></i> ' + response.data.message + '</div>');
                        $button.parent().append($message);
                        
                        // Update stats
                        updateStats({
                            critical: 0,
                            warning: 0,
                            info: 0
                        });
                    } else {
                        // Reset fix buttons
                        $fixButtons.prop('disabled', false);
                        
                        // Reset fix all button
                        $button.prop('disabled', false).text('Fix All Issues');
                        
                        // Add error message
                        var $message = $('<div class="wp-hss-fix-message" style="margin-top: 10px; color: #d63638;"><i class="dashicons dashicons-warning"></i> ' + response.data.message + '</div>');
                        $button.parent().append($message);
                    }
                },
                error: function() {
                    // Reset fix buttons
                    $fixButtons.prop('disabled', false);
                    
                    // Reset fix all button
                    $button.prop('disabled', false).text('Fix All Issues');
                    
                    // Add error message
                    var $message = $('<div class="wp-hss-fix-message" style="margin-top: 10px; color: #d63638;"><i class="dashicons dashicons-warning"></i> An error occurred while fixing issues. Please try again.</div>');
                    $button.parent().append($message);
                }
            });
        });
    }
    
    // Update stats
    function updateStats(count) {
        $('#wp-hss-stat-critical').text(count.critical);
        $('#wp-hss-stat-warning').text(count.warning);
        $('#wp-hss-stat-info').text(count.info);
        
        // Calculate health score
        var total = count.critical + count.warning + count.info;
        var healthScore = total > 0 ? Math.max(0, 100 - (count.critical * 10) - (count.warning * 5) - (count.info * 2)) : 100;
        
        // Update health score
        $('#wp-hss-health-score').text(healthScore);
        
        // Update health score color
        if (healthScore >= 80) {
            $('#wp-hss-health-score').css('color', 'var(--wp-hss-success)');
        } else if (healthScore >= 50) {
            $('#wp-hss-health-score').css('color', 'var(--wp-hss-warning)');
        } else {
            $('#wp-hss-health-score').css('color', 'var(--wp-hss-danger)');
        }
    }
    
    // Update issue count
    function updateIssueCount(issueType) {
        // Get severity based on issue type
        var severity = 'info';
        if (issueType === 'ssl_issues' || issueType === 'sitemap_robots' || issueType === 'title_length') {
            severity = 'critical';
        } else if (issueType === 'meta_description' || issueType === 'heading_tags' || issueType === 'image_alt_text') {
            severity = 'warning';
        }
        
        // Get current count
        var count = parseInt($('#wp-hss-stat-' + severity).text());
        
        // Update count
        if (count > 0) {
            $('#wp-hss-stat-' + severity).text(count - 1);
        }
        
        // Update health score
        var critical = parseInt($('#wp-hss-stat-critical').text());
        var warning = parseInt($('#wp-hss-stat-warning').text());
        var info = parseInt($('#wp-hss-stat-info').text());
        
        updateStats({
            critical: critical,
            warning: warning,
            info: info
        });
    }
    
    // Initialize tooltips
    $('.wp-hss-tooltip').hover(
        function() {
            var $tooltip = $(this);
            var tooltipText = $tooltip.data('tooltip');
            
            $tooltip.append('<span class="wp-hss-tooltip-text">' + tooltipText + '</span>');
        },
        function() {
            $(this).find('.wp-hss-tooltip-text').remove();
        }
    );
    
    // Initialize on page load
    initFixButtons();
});
