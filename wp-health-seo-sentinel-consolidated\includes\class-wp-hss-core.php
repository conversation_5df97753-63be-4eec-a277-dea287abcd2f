<?php
/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 *
 * @since      1.0.0
 */
class WP_HSS_Core {

    protected $plugin_name;
    protected $version;
    protected $admin_ui;
    protected $scanner;
    protected $fixer;

    /**
     * Initialize the class and set its properties.
     *
     * @since    1.0.0
     */
    public function __construct() {
        $this->plugin_name = 'wp-health-seo-sentinel';
        $this->version = WP_HSS_VERSION;

        // Load only essential dependencies
        $this->load_essential_dependencies();
    }

    /**
     * Load only the essential dependencies for this plugin.
     *
     * @since    1.0.0
     */
    private function load_essential_dependencies() {
        try {
            // Load AJAX handler class
            $ajax_handler_file = WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-ajax-handler.php';
            if (file_exists($ajax_handler_file)) {
                require_once $ajax_handler_file;
                // AJAX handler is initialized automatically in its constructor
            }

            // Load AI optimizer class
            $ai_optimizer_file = WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-ai-optimizer.php';
            if (file_exists($ai_optimizer_file)) {
                require_once $ai_optimizer_file;
                // AI optimizer is initialized automatically in its constructor
            }

            // Load admin UI class
            $admin_ui_file = WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-admin-ui.php';
            if (file_exists($admin_ui_file)) {
                require_once $admin_ui_file;
                // Initialize admin UI
                $this->admin_ui = new WP_HSS_Admin_UI($this->get_plugin_name(), $this->get_version());
            } else {
                // Fallback to basic admin UI if file doesn't exist
                $this->admin_ui = null;
                error_log('WP HSS: Admin UI file not found: ' . $admin_ui_file);
            }

            // Load scanner class if it exists
            $scanner_file = WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-scanner.php';
            if (file_exists($scanner_file)) {
                require_once $scanner_file;
                $this->scanner = new WP_HSS_Scanner();
            } else {
                $this->scanner = null;
                error_log('WP HSS: Scanner file not found: ' . $scanner_file);
            }

            // Load fixer class if it exists
            $fixer_file = WP_HSS_PLUGIN_DIR . 'includes/class-wp-hss-fixer.php';
            if (file_exists($fixer_file)) {
                require_once $fixer_file;
                $this->fixer = new WP_HSS_Fixer();
            } else {
                $this->fixer = null;
                error_log('WP HSS: Fixer file not found: ' . $fixer_file);
            }
        } catch (Exception $e) {
            error_log('WP HSS: Error loading dependencies: ' . $e->getMessage());
        }
    }

    /**
     * Run the plugin with error handling.
     *
     * @since    1.0.0
     */
    public function run() {
        try {
            // Register admin menu
            add_action('admin_menu', array($this, 'add_admin_menu'));

            // Register admin styles and scripts
            add_action('admin_enqueue_scripts', array($this, 'enqueue_styles'));
            add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));

            // Register AJAX handlers if scanner exists
            if ($this->scanner !== null) {
                add_action('wp_ajax_wp_hss_start_scan', array($this->scanner, 'ajax_start_scan'));
                add_action('wp_ajax_wp_hss_get_scan_status', array($this->scanner, 'ajax_get_scan_status'));
                add_action('wp_ajax_wp_hss_cancel_scan', array($this->scanner, 'ajax_cancel_scan'));
            }

            // Register AJAX handlers if fixer exists
            if ($this->fixer !== null) {
                add_action('wp_ajax_wp_hss_fix_issue', array($this->fixer, 'ajax_fix_issue'));
                add_action('wp_ajax_wp_hss_fix_all_issues', array($this->fixer, 'ajax_fix_all_issues'));
            }

            // Register AJAX handler for API key validation
            add_action('wp_ajax_wp_hss_validate_api_key', array($this, 'ajax_validate_api_key'));

            // Log successful initialization
            if (function_exists('error_log')) {
                error_log('WP Health SEO Sentinel: Core run method executed successfully');
            }
        } catch (Exception $e) {
            // Log error but don't cause fatal error
            if (function_exists('error_log')) {
                error_log('WP Health SEO Sentinel Error: ' . $e->getMessage());
            }

            // Display admin notice about error
            add_action('admin_notices', array($this, 'display_error_notice'));
        }
    }

    /**
     * Add admin menu.
     *
     * @since    1.0.0
     */
    public function add_admin_menu() {
        try {
            // Use admin UI if available
            if ($this->admin_ui !== null && method_exists($this->admin_ui, 'add_admin_menu')) {
                $this->admin_ui->add_admin_menu();
                return;
            }

            // Fallback to basic menu
            add_menu_page(
                'WP Health & SEO Sentinel',
                'SEO Sentinel',
                'manage_options',
                'wp-hss',
                array($this, 'render_admin_page'),
                'dashicons-chart-area',
                100
            );

            // Add settings submenu
            add_submenu_page(
                'wp-hss',
                'Settings',
                'Settings',
                'manage_options',
                'wp-hss-settings',
                array($this, 'render_settings_page')
            );
        } catch (Exception $e) {
            error_log('WP HSS: Error adding admin menu: ' . $e->getMessage());
        }
    }

    /**
     * Render the admin page.
     *
     * @since    1.0.0
     */
    public function render_admin_page() {
        ?>
        <div class="wrap">
            <h1>WP Health & SEO Sentinel</h1>
            <div class="notice notice-success is-dismissible">
                <p>Plugin is working correctly!</p>
            </div>
            <div class="card">
                <h2>SEO Scanner</h2>
                <p>Click the button below to scan your site for SEO issues.</p>
                <button id="wp-hss-scan-button" class="button button-primary">Start Scan</button>
                <div id="wp-hss-scan-results" style="margin-top: 15px;"></div>
            </div>
        </div>
        <?php
    }

    /**
     * Render the settings page.
     *
     * @since    1.0.0
     */
    public function render_settings_page() {
        // Get current settings
        $settings = get_option('wp_hss_settings', array());

        ?>
        <div class="wrap">
            <h1>WP Health & SEO Sentinel Settings</h1>
            <form method="post" action="options.php">
                <?php settings_fields('wp_hss_settings_group'); ?>
                <table class="form-table">
                    <tr>
                        <th scope="row">Enable Scanning</th>
                        <td>
                            <input type="checkbox" name="wp_hss_settings[enable_scanning]" value="1" <?php checked(isset($settings['enable_scanning']) ? $settings['enable_scanning'] : true); ?>>
                            <p class="description">Enable or disable the scanning functionality.</p>
                        </td>
                    </tr>
                </table>
                <?php submit_button(); ?>
            </form>
        </div>
        <?php
    }

    /**
     * Register the stylesheets for the admin area.
     *
     * @since    1.0.0
     */
    public function enqueue_styles() {
        wp_enqueue_style(
            $this->plugin_name,
            WP_HSS_PLUGIN_URL . 'admin/css/wp-hss-admin.css',
            array(),
            $this->version,
            'all'
        );
    }

    /**
     * Register the JavaScript for the admin area.
     *
     * @since    1.0.0
     */
    public function enqueue_scripts() {
        wp_enqueue_script(
            $this->plugin_name,
            WP_HSS_PLUGIN_URL . 'admin/js/wp-hss-admin.js',
            array('jquery'),
            $this->version,
            false
        );

        // Localize script with data
        wp_localize_script(
            $this->plugin_name,
            'wp_hss_ajax',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('wp_hss_ajax_nonce'),
                'strings' => array(
                    'scanning' => __('Scanning...', 'wp-hss'),
                    'scan_complete' => __('Scan complete!', 'wp-hss'),
                    'scan_failed' => __('Scan failed. Please try again.', 'wp-hss'),
                    'fixing' => __('Fixing...', 'wp-hss'),
                    'fix_complete' => __('Fix complete!', 'wp-hss'),
                    'fix_failed' => __('Fix failed. Please try again.', 'wp-hss'),
                    'confirm_bulk_fix' => __('Are you sure you want to fix all selected issues? This action cannot be undone.', 'wp-hss')
                )
            )
        );
    }

    /**
     * Display error notice.
     *
     * @since    1.0.0
     */
    public function display_error_notice() {
        ?>
        <div class="notice notice-error is-dismissible">
            <p><strong>WP Health & SEO Sentinel Error:</strong> An error occurred while initializing the plugin.</p>
            <p>Please check the error logs for more information.</p>
        </div>
        <?php
    }

    /**
     * The name of the plugin used to uniquely identify it within the context of
     * WordPress and to define internationalization functionality.
     *
     * @since     1.0.0
     * @return    string    The name of the plugin.
     */
    public function get_plugin_name() {
        return $this->plugin_name;
    }

    /**
     * Retrieve the version number of the plugin.
     *
     * @since     1.0.0
     * @return    string    The version number of the plugin.
     */
    public function get_version() {
        return $this->version;
    }

    /**
     * AJAX handler for validating API key.
     *
     * @since    1.0.0
     */
    public function ajax_validate_api_key() {
        try {
            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_validate_api_key')) {
                wp_send_json_error(['message' => 'Security check failed.']);
            }

            // Check user capabilities
            if (!current_user_can('manage_options')) {
                wp_send_json_error(['message' => 'You do not have permission to perform this action.']);
            }

            // Get API key and provider
            $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';
            $provider = isset($_POST['provider']) ? sanitize_text_field($_POST['provider']) : 'openai';

            if (empty($api_key)) {
                wp_send_json_error(['message' => 'API key cannot be empty.']);
            }

            // Validate API key
            $result = $this->validate_ai_api_key($api_key, $provider);

            if ($result['success']) {
                // Update settings to mark API key as valid
                $settings = get_option('wp_hss_settings', []);
                $settings['ai_api_key_valid'] = true;
                update_option('wp_hss_settings', $settings);

                wp_send_json_success(['message' => $result['message']]);
            } else {
                wp_send_json_error(['message' => $result['message']]);
            }
        } catch (Exception $e) {
            error_log('WP HSS: Error in ajax_validate_api_key: ' . $e->getMessage());
            wp_send_json_error(['message' => 'An error occurred: ' . $e->getMessage()]);
        }
    }

    /**
     * Validate AI API key.
     *
     * @since    1.0.0
     * @param    string    $api_key     API key to validate.
     * @param    string    $provider    AI provider.
     * @return   array                  Validation result.
     */
    private function validate_ai_api_key($api_key, $provider = 'openai') {
        try {
            // Basic validation
            if (empty($api_key)) {
                return [
                    'success' => false,
                    'message' => 'API key cannot be empty.'
                ];
            }

            // Check key format based on provider
            if ($provider === 'openai' && strpos($api_key, 'sk-') !== 0) {
                return [
                    'success' => false,
                    'message' => 'Invalid OpenAI API key format. Should start with "sk-".'
                ];
            } else if ($provider === 'anthropic' && strpos($api_key, 'sk-ant-') !== 0) {
                return [
                    'success' => false,
                    'message' => 'Invalid Anthropic API key format. Should start with "sk-ant-".'
                ];
            }

            // Make a test API call to validate the key
            $is_valid = false;
            $error_message = '';

            if ($provider === 'openai') {
                $result = $this->test_openai_api_key($api_key);
                $is_valid = $result['success'];
                $error_message = $result['message'];
            } else if ($provider === 'anthropic') {
                $result = $this->test_anthropic_api_key($api_key);
                $is_valid = $result['success'];
                $error_message = $result['message'];
            } else if ($provider === 'google') {
                $result = $this->test_google_ai_api_key($api_key);
                $is_valid = $result['success'];
                $error_message = $result['message'];
            }

            if ($is_valid) {
                return [
                    'success' => true,
                    'message' => 'API key validated successfully.'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'API key validation failed: ' . $error_message
                ];
            }
        } catch (Exception $e) {
            error_log('WP HSS: Error validating API key: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Test OpenAI API key.
     *
     * @since    1.0.0
     * @param    string    $api_key    OpenAI API key.
     * @return   array                 Test result.
     */
    private function test_openai_api_key($api_key) {
        try {
            // API endpoint for models list (lightweight call)
            $url = 'https://api.openai.com/v1/models';

            // Request headers
            $headers = [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $api_key
            ];

            // Initialize cURL
            $ch = curl_init();

            // Set cURL options
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            // Execute cURL request
            $response = curl_exec($ch);

            // Get HTTP status code
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            // Check for errors
            if (curl_errno($ch)) {
                $error = curl_error($ch);
                curl_close($ch);
                return [
                    'success' => false,
                    'message' => 'cURL error: ' . $error
                ];
            }

            // Close cURL
            curl_close($ch);

            // Check HTTP status code
            if ($http_code === 200) {
                return [
                    'success' => true,
                    'message' => 'OpenAI API key is valid.'
                ];
            } else {
                // Decode response to get error message
                $response_data = json_decode($response, true);
                $error_message = isset($response_data['error']['message']) ? $response_data['error']['message'] : 'Unknown error';

                return [
                    'success' => false,
                    'message' => 'API error: ' . $error_message
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Test Anthropic API key.
     *
     * @since    1.0.0
     * @param    string    $api_key    Anthropic API key.
     * @return   array                 Test result.
     */
    private function test_anthropic_api_key($api_key) {
        try {
            // For now, just do a basic validation
            // In a real implementation, we would make an actual API call

            return [
                'success' => true,
                'message' => 'Anthropic API key format is valid. (Note: Full validation requires an actual API call)'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Test Google AI API key.
     *
     * @since    1.0.0
     * @param    string    $api_key    Google AI API key.
     * @return   array                 Test result.
     */
    private function test_google_ai_api_key($api_key) {
        try {
            // For now, just do a basic validation
            // In a real implementation, we would make an actual API call

            return [
                'success' => true,
                'message' => 'Google AI API key format is valid. (Note: Full validation requires an actual API call)'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Exception: ' . $e->getMessage()
            ];
        }
    }
}
