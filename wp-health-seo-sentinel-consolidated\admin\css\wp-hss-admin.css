/**
 * Admin styles for WP Health & SEO Sentinel
 * Modern, professional UI/UX design
 */

:root {
    --wp-hss-primary: #4361ee;
    --wp-hss-primary-dark: #3a56d4;
    --wp-hss-secondary: #4cc9f0;
    --wp-hss-success: #06d6a0;
    --wp-hss-warning: #ffd166;
    --wp-hss-danger: #ef476f;
    --wp-hss-info: #118ab2;
    --wp-hss-light: #f8f9fa;
    --wp-hss-dark: #212529;
    --wp-hss-gray: #6c757d;
    --wp-hss-gray-light: #e9ecef;
    --wp-hss-border-radius: 8px;
    --wp-hss-box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
    --wp-hss-transition: all 0.2s ease-in-out;
}

/* Main container */
.wp-hss-container {
    padding: 30px;
    background: #fff;
    border-radius: var(--wp-hss-border-radius);
    box-shadow: var(--wp-hss-box-shadow);
    margin-top: 20px;
    max-width: 1200px;
}

/* Dashboard header */
.wp-hss-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--wp-hss-gray-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.wp-hss-header-content {
    flex: 1;
}

.wp-hss-logo {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.wp-hss-logo img {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.wp-hss-logo h1 {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    color: var(--wp-hss-dark);
}

.wp-hss-header p {
    font-size: 16px;
    color: var(--wp-hss-gray);
    margin-bottom: 0;
}

.wp-hss-header-actions {
    display: flex;
    gap: 10px;
}

/* Cards */
.wp-hss-card {
    background: #fff;
    border-radius: var(--wp-hss-border-radius);
    box-shadow: var(--wp-hss-box-shadow);
    padding: 25px;
    margin-bottom: 30px;
    transition: var(--wp-hss-transition);
    border-top: 4px solid var(--wp-hss-primary);
}

.wp-hss-card:hover {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05);
}

.wp-hss-card h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
    color: var(--wp-hss-dark);
    display: flex;
    align-items: center;
}

.wp-hss-card h2 i {
    margin-right: 10px;
    color: var(--wp-hss-primary);
}

.wp-hss-card-content {
    margin-bottom: 20px;
}

.wp-hss-card-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding-top: 15px;
    border-top: 1px solid var(--wp-hss-gray-light);
}

/* Dashboard grid */
.wp-hss-dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.wp-hss-stat-card {
    background: #fff;
    border-radius: var(--wp-hss-border-radius);
    box-shadow: var(--wp-hss-box-shadow);
    padding: 20px;
    display: flex;
    flex-direction: column;
    transition: var(--wp-hss-transition);
}

.wp-hss-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05);
}

.wp-hss-stat-card.critical {
    border-left: 4px solid var(--wp-hss-danger);
}

.wp-hss-stat-card.warning {
    border-left: 4px solid var(--wp-hss-warning);
}

.wp-hss-stat-card.good {
    border-left: 4px solid var(--wp-hss-success);
}

.wp-hss-stat-card.info {
    border-left: 4px solid var(--wp-hss-info);
}

.wp-hss-stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.wp-hss-stat-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--wp-hss-gray);
    margin: 0;
}

.wp-hss-stat-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.wp-hss-stat-icon.critical {
    background-color: var(--wp-hss-danger);
}

.wp-hss-stat-icon.warning {
    background-color: var(--wp-hss-warning);
}

.wp-hss-stat-icon.good {
    background-color: var(--wp-hss-success);
}

.wp-hss-stat-icon.info {
    background-color: var(--wp-hss-info);
}

.wp-hss-stat-value {
    font-size: 28px;
    font-weight: 700;
    margin: 0;
    color: var(--wp-hss-dark);
}

.wp-hss-stat-description {
    font-size: 14px;
    color: var(--wp-hss-gray);
    margin-top: 5px;
}

/* Results table */
.wp-hss-results-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-top: 20px;
    border-radius: var(--wp-hss-border-radius);
    overflow: hidden;
    box-shadow: var(--wp-hss-box-shadow);
}

.wp-hss-results-table thead th {
    background-color: var(--wp-hss-primary);
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.wp-hss-results-table thead th:first-child {
    border-top-left-radius: var(--wp-hss-border-radius);
}

.wp-hss-results-table thead th:last-child {
    border-top-right-radius: var(--wp-hss-border-radius);
}

.wp-hss-results-table tbody tr {
    background-color: white;
    transition: var(--wp-hss-transition);
}

.wp-hss-results-table tbody tr:hover {
    background-color: var(--wp-hss-light);
}

.wp-hss-results-table tbody tr.wp-hss-fixed {
    background-color: rgba(6, 214, 160, 0.1);
}

.wp-hss-results-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--wp-hss-gray-light);
    vertical-align: middle;
}

.wp-hss-results-table tr:last-child td {
    border-bottom: none;
}

.wp-hss-results-table tr:last-child td:first-child {
    border-bottom-left-radius: var(--wp-hss-border-radius);
}

.wp-hss-results-table tr:last-child td:last-child {
    border-bottom-right-radius: var(--wp-hss-border-radius);
}

/* Severity badges */
.wp-hss-severity {
    display: inline-flex;
    align-items: center;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.wp-hss-severity i {
    margin-right: 5px;
    font-size: 14px;
}

.wp-hss-severity-critical {
    background-color: var(--wp-hss-danger);
    color: white;
}

.wp-hss-severity-warning {
    background-color: var(--wp-hss-warning);
    color: var(--wp-hss-dark);
}

.wp-hss-severity-info {
    background-color: var(--wp-hss-info);
    color: white;
}

.wp-hss-severity-good {
    background-color: var(--wp-hss-success);
    color: white;
}

/* Progress bar */
.wp-hss-progress-container {
    margin: 20px 0;
}

.wp-hss-progress-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.wp-hss-progress-label {
    font-size: 14px;
    font-weight: 600;
    color: var(--wp-hss-dark);
}

.wp-hss-progress-percentage {
    font-size: 14px;
    color: var(--wp-hss-gray);
}

.wp-hss-progress-bar {
    height: 8px;
    background-color: var(--wp-hss-gray-light);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.wp-hss-progress-bar-inner {
    height: 100%;
    background-color: var(--wp-hss-primary);
    border-radius: 4px;
    transition: width 0.5s ease;
    position: absolute;
    top: 0;
    left: 0;
}

/* Settings sections */
.wp-hss-settings-section {
    background: white;
    border-radius: var(--wp-hss-border-radius);
    box-shadow: var(--wp-hss-box-shadow);
    padding: 25px;
    margin-bottom: 30px;
}

.wp-hss-settings-section h2 {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--wp-hss-gray-light);
    font-size: 20px;
    font-weight: 600;
    color: var(--wp-hss-dark);
    display: flex;
    align-items: center;
}

.wp-hss-settings-section h2 i {
    margin-right: 10px;
    color: var(--wp-hss-primary);
}

/* Info boxes */
.wp-hss-info-box {
    background-color: rgba(17, 138, 178, 0.1);
    border-left: 4px solid var(--wp-hss-info);
    border-radius: 0 var(--wp-hss-border-radius) var(--wp-hss-border-radius) 0;
    padding: 20px;
    margin: 20px 0;
}

.wp-hss-info-box h3 {
    margin-top: 0;
    color: var(--wp-hss-info);
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.wp-hss-info-box h3 i {
    margin-right: 8px;
}

.wp-hss-info-box ul {
    margin-left: 20px;
    margin-bottom: 0;
}

.wp-hss-info-box p:last-child {
    margin-bottom: 0;
}

.wp-hss-warning-box {
    background-color: rgba(255, 209, 102, 0.1);
    border-left: 4px solid var(--wp-hss-warning);
    border-radius: 0 var(--wp-hss-border-radius) var(--wp-hss-border-radius) 0;
    padding: 20px;
    margin: 20px 0;
}

.wp-hss-warning-box h3 {
    margin-top: 0;
    color: var(--wp-hss-dark);
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.wp-hss-warning-box h3 i {
    margin-right: 8px;
    color: var(--wp-hss-warning);
}

/* Buttons */
.wp-hss-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--wp-hss-transition);
    text-decoration: none;
    border: none;
}

.wp-hss-button i {
    margin-right: 8px;
}

.wp-hss-button-primary {
    background-color: var(--wp-hss-primary);
    color: white;
}

.wp-hss-button-primary:hover, .wp-hss-button-primary:focus {
    background-color: var(--wp-hss-primary-dark);
    color: white;
}

.wp-hss-button-success {
    background-color: var(--wp-hss-success);
    color: white;
}

.wp-hss-button-success:hover, .wp-hss-button-success:focus {
    background-color: #05b485;
    color: white;
}

.wp-hss-button-warning {
    background-color: var(--wp-hss-warning);
    color: var(--wp-hss-dark);
}

.wp-hss-button-warning:hover, .wp-hss-button-warning:focus {
    background-color: #ffc233;
    color: var(--wp-hss-dark);
}

.wp-hss-button-danger {
    background-color: var(--wp-hss-danger);
    color: white;
}

.wp-hss-button-danger:hover, .wp-hss-button-danger:focus {
    background-color: #e63e66;
    color: white;
}

.wp-hss-button-outline {
    background-color: transparent;
    border: 1px solid var(--wp-hss-primary);
    color: var(--wp-hss-primary);
}

.wp-hss-button-outline:hover, .wp-hss-button-outline:focus {
    background-color: var(--wp-hss-primary);
    color: white;
}

.wp-hss-button-sm {
    padding: 6px 12px;
    font-size: 12px;
}

.wp-hss-button-lg {
    padding: 12px 24px;
    font-size: 16px;
}

.wp-hss-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.wp-hss-fix-button {
    background-color: var(--wp-hss-success);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--wp-hss-transition);
    display: inline-flex;
    align-items: center;
}

.wp-hss-fix-button i {
    margin-right: 5px;
}

.wp-hss-fix-button:hover {
    background-color: #05b485;
}

.wp-hss-fix-all-button {
    margin-top: 20px;
}

/* Loading spinner */
.wp-hss-loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.wp-hss-loading {
    display: inline-block;
    width: 50px;
    height: 50px;
    border: 3px solid rgba(67, 97, 238, 0.2);
    border-radius: 50%;
    border-top-color: var(--wp-hss-primary);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 20px;
}

.wp-hss-loading-text {
    font-size: 16px;
    color: var(--wp-hss-gray);
    text-align: center;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* API key validation */
.wp-hss-api-status {
    display: inline-flex;
    align-items: center;
    padding: 4px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    margin-left: 10px;
}

.wp-hss-api-valid {
    background-color: rgba(6, 214, 160, 0.1);
    color: var(--wp-hss-success);
}

.wp-hss-api-valid::before {
    content: "✓";
    margin-right: 5px;
}

.wp-hss-api-invalid {
    background-color: rgba(239, 71, 111, 0.1);
    color: var(--wp-hss-danger);
}

.wp-hss-api-invalid::before {
    content: "✕";
    margin-right: 5px;
}

/* Issue details */
.wp-hss-issue-details {
    background-color: var(--wp-hss-light);
    border-radius: var(--wp-hss-border-radius);
    padding: 15px;
    margin-top: 10px;
    font-size: 14px;
}

.wp-hss-issue-details p:last-child {
    margin-bottom: 0;
}

.wp-hss-issue-details-toggle {
    color: var(--wp-hss-primary);
    cursor: pointer;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    margin-top: 5px;
}

.wp-hss-issue-details-toggle i {
    margin-right: 5px;
    transition: transform 0.2s ease;
}

.wp-hss-issue-details-toggle.open i {
    transform: rotate(90deg);
}

/* Tabs */
.wp-hss-tabs {
    display: flex;
    border-bottom: 1px solid var(--wp-hss-gray-light);
    margin-bottom: 20px;
}

.wp-hss-tab {
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    color: var(--wp-hss-gray);
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: var(--wp-hss-transition);
}

.wp-hss-tab:hover {
    color: var(--wp-hss-primary);
}

.wp-hss-tab.active {
    color: var(--wp-hss-primary);
    border-bottom-color: var(--wp-hss-primary);
}

.wp-hss-tab-content {
    display: none;
}

.wp-hss-tab-content.active {
    display: block;
}

/* Tooltips */
.wp-hss-tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.wp-hss-tooltip-content {
    position: absolute;
    z-index: 100;
    background-color: var(--wp-hss-dark);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    pointer-events: none;
}

.wp-hss-tooltip-content::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 5px 5px 0;
    border-style: solid;
    border-color: var(--wp-hss-dark) transparent transparent transparent;
}

.wp-hss-tooltip-content.active {
    opacity: 1;
    visibility: visible;
}

/* Notifications */
.wp-hss-notification {
    position: fixed;
    top: 32px;
    right: 20px;
    z-index: 9999;
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-radius: var(--wp-hss-border-radius);
    box-shadow: var(--wp-hss-box-shadow);
    font-size: 14px;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    max-width: 400px;
}

.wp-hss-notification.active {
    transform: translateX(0);
}

.wp-hss-notification i {
    margin-right: 10px;
    font-size: 20px;
}

.wp-hss-notification span {
    flex: 1;
}

.wp-hss-notification-close {
    background: transparent;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0;
    margin-left: 10px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.wp-hss-notification-close:hover {
    opacity: 1;
}

.wp-hss-notification-success {
    background-color: var(--wp-hss-success);
    color: white;
}

.wp-hss-notification-error {
    background-color: var(--wp-hss-danger);
    color: white;
}

.wp-hss-notification-warning {
    background-color: var(--wp-hss-warning);
    color: var(--wp-hss-dark);
}

.wp-hss-notification-info {
    background-color: var(--wp-hss-info);
    color: white;
}

/* Dialog */
.wp-hss-dialog-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.wp-hss-dialog-overlay.active {
    opacity: 1;
    visibility: visible;
}

.wp-hss-dialog {
    background-color: white;
    border-radius: var(--wp-hss-border-radius);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 500px;
    transform: translateY(20px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.wp-hss-dialog.wp-hss-dialog-large {
    max-width: 700px;
}

.wp-hss-dialog.active {
    transform: translateY(0);
    opacity: 1;
}

/* Tool content styles */
.wp-hss-tool-content {
    margin-bottom: 20px;
}

.wp-hss-tool-options {
    margin-top: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.wp-hss-tool-options label {
    display: flex;
    align-items: center;
    font-size: 14px;
    cursor: pointer;
}

.wp-hss-tool-options input[type="checkbox"] {
    margin-right: 8px;
}

.wp-hss-success-message {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background-color: rgba(6, 214, 160, 0.1);
    border-radius: var(--wp-hss-border-radius);
}

.wp-hss-success-message i {
    font-size: 48px;
    color: var(--wp-hss-success);
    margin-bottom: 15px;
}

.wp-hss-success-message h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--wp-hss-dark);
}

.wp-hss-tool-results {
    margin-bottom: 20px;
    padding: 20px;
    background-color: var(--wp-hss-light);
    border-radius: var(--wp-hss-border-radius);
}

.wp-hss-tool-results h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--wp-hss-dark);
}

.wp-hss-tool-results p {
    margin-bottom: 8px;
    font-size: 14px;
}

.wp-hss-dialog-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--wp-hss-gray-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.wp-hss-dialog-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--wp-hss-dark);
}

.wp-hss-dialog-close {
    background: transparent;
    border: none;
    cursor: pointer;
    color: var(--wp-hss-gray);
    padding: 0;
    transition: color 0.2s ease;
}

.wp-hss-dialog-close:hover {
    color: var(--wp-hss-dark);
}

.wp-hss-dialog-content {
    padding: 20px;
}

.wp-hss-dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--wp-hss-gray-light);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Animations */
.wp-hss-spin {
    animation: spin 1s linear infinite;
}

.wp-hss-fade-in {
    animation: fadeIn 0.3s ease forwards;
}

.wp-hss-fade-out {
    animation: fadeOut 0.3s ease forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Dashboard overview */
.wp-hss-overview {
    margin-bottom: 30px;
}

.wp-hss-health-score {
    text-align: center;
    padding: 30px;
    background: white;
    border-radius: var(--wp-hss-border-radius);
    box-shadow: var(--wp-hss-box-shadow);
    margin-bottom: 20px;
}

.wp-hss-health-score-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--wp-hss-gray);
    margin-bottom: 15px;
}

.wp-hss-health-score-value {
    font-size: 60px;
    font-weight: 700;
    color: var(--wp-hss-primary);
    line-height: 1;
    margin-bottom: 10px;
}

.wp-hss-health-score-label {
    font-size: 14px;
    color: var(--wp-hss-gray);
}

.wp-hss-health-score-chart {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto 20px;
}

.wp-hss-health-score-chart svg {
    transform: rotate(-90deg);
}

.wp-hss-health-score-chart-bg {
    fill: none;
    stroke: var(--wp-hss-gray-light);
    stroke-width: 10;
}

.wp-hss-health-score-chart-value {
    fill: none;
    stroke: var(--wp-hss-primary);
    stroke-width: 10;
    stroke-linecap: round;
    transition: stroke-dasharray 1s ease;
}

.wp-hss-health-score-chart-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 30px;
    font-weight: 700;
    color: var(--wp-hss-primary);
}

/* Responsive design */
@media (max-width: 782px) {
    .wp-hss-container {
        padding: 20px;
    }

    .wp-hss-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .wp-hss-header-actions {
        margin-top: 15px;
    }

    .wp-hss-dashboard-grid {
        grid-template-columns: 1fr;
    }

    .wp-hss-results-table {
        display: block;
        overflow-x: auto;
    }

    .wp-hss-card-footer {
        flex-direction: column;
        align-items: stretch;
    }

    .wp-hss-card-footer .wp-hss-button {
        margin-bottom: 10px;
    }

    .wp-hss-notification {
        max-width: calc(100% - 40px);
        right: 10px;
    }

    .wp-hss-dialog {
        max-width: calc(100% - 40px);
        margin: 0 20px;
    }

    .wp-hss-scan-summary {
        flex-direction: column;
    }
}

/* Issue group header */
.wp-hss-issue-group-header {
    background-color: var(--wp-hss-light);
}

.wp-hss-issue-group-header td {
    font-weight: 600;
    color: var(--wp-hss-dark);
    padding: 10px 15px;
}

/* Issue row */
.wp-hss-issue-row td {
    vertical-align: top;
}

.wp-hss-issue-message {
    font-weight: 500;
}

.wp-hss-issue-page {
    color: var(--wp-hss-gray);
    font-size: 0.9em;
}

.wp-hss-issue-actions {
    white-space: nowrap;
    text-align: right;
}

/* Issue details */
.wp-hss-issue-details {
    margin-top: 10px;
    padding: 10px;
    background-color: var(--wp-hss-light);
    border-radius: var(--wp-hss-border-radius);
    font-size: 0.9em;
}

.wp-hss-issue-detail-item {
    margin-bottom: 8px;
}

.wp-hss-issue-detail-item:last-child {
    margin-bottom: 0;
}

/* Scan summary */
.wp-hss-scan-summary {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    gap: 15px;
}

.wp-hss-scan-summary-item {
    flex: 1;
    padding: 15px;
    border-radius: var(--wp-hss-border-radius);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.wp-hss-scan-summary-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.wp-hss-scan-summary-item.critical {
    background-color: rgba(239, 71, 111, 0.1);
    border: 1px solid var(--wp-hss-danger);
}

.wp-hss-scan-summary-item.warning {
    background-color: rgba(255, 209, 102, 0.1);
    border: 1px solid var(--wp-hss-warning);
}

.wp-hss-scan-summary-item.info {
    background-color: rgba(17, 138, 178, 0.1);
    border: 1px solid var(--wp-hss-info);
}

.wp-hss-scan-summary-count {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 5px;
}

.wp-hss-scan-summary-item.critical .wp-hss-scan-summary-count {
    color: var(--wp-hss-danger);
}

.wp-hss-scan-summary-item.warning .wp-hss-scan-summary-count {
    color: var(--wp-hss-warning);
}

.wp-hss-scan-summary-item.info .wp-hss-scan-summary-count {
    color: var(--wp-hss-info);
}

.wp-hss-scan-summary-label {
    font-size: 14px;
    font-weight: 500;
}

/* Filter options */
.wp-hss-filter-options {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.wp-hss-severity-filter {
    padding: 8px 12px;
    border-radius: var(--wp-hss-border-radius);
    border: 1px solid var(--wp-hss-gray-light);
    background-color: white;
}

/* Issues section headers */
.wp-hss-issues-section-header {
    margin-top: 25px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--wp-hss-gray-light);
    display: flex;
    align-items: center;
    gap: 8px;
}

.wp-hss-issues-section-header.critical {
    color: var(--wp-hss-danger);
}

.wp-hss-issues-section-header.warning {
    color: var(--wp-hss-warning);
}

.wp-hss-issues-section-header.info {
    color: var(--wp-hss-info);
}

/* Fix all container */
.wp-hss-fix-all-container {
    margin-top: 30px;
    text-align: center;
}
