<?php
/**
 * Simple Plugin Test
 * 
 * This file tests if the plugin can be loaded without fatal errors
 */

// Simulate WordPress environment
if (!defined('ABSPATH')) {
    define('ABSPATH', '/fake/wordpress/path/');
}

if (!defined('WP_PLUGIN_URL')) {
    define('WP_PLUGIN_URL', 'http://example.com/wp-content/plugins');
}

if (!defined('WP_CONTENT_DIR')) {
    define('WP_CONTENT_DIR', '/fake/wp-content');
}

// Mock WordPress functions that the plugin uses
function add_action($hook, $callback, $priority = 10, $args = 1) {
    echo "add_action called: $hook\n";
    return true;
}

function add_filter($hook, $callback, $priority = 10, $args = 1) {
    echo "add_filter called: $hook\n";
    return true;
}

function register_activation_hook($file, $callback) {
    echo "register_activation_hook called\n";
    return true;
}

function register_deactivation_hook($file, $callback) {
    echo "register_deactivation_hook called\n";
    return true;
}

function plugin_basename($file) {
    return basename(dirname($file)) . '/' . basename($file);
}

function plugins_url($path = '', $plugin = '') {
    return 'http://example.com/wp-content/plugins/' . $path;
}

function plugin_dir_path($file) {
    return dirname($file) . '/';
}

function add_menu_page($page_title, $menu_title, $capability, $menu_slug, $function = '', $icon_url = '', $position = null) {
    echo "add_menu_page called: $menu_title\n";
    return true;
}

function add_submenu_page($parent_slug, $page_title, $menu_title, $capability, $menu_slug, $function = '') {
    echo "add_submenu_page called: $menu_title\n";
    return true;
}

function current_user_can($capability) {
    return true;
}

function wp_die($message) {
    die($message);
}

function get_option($option, $default = false) {
    return $default;
}

function update_option($option, $value) {
    return true;
}

function sanitize_text_field($str) {
    return trim($str);
}

function sanitize_email($email) {
    return filter_var($email, FILTER_SANITIZE_EMAIL);
}

function esc_attr($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

function esc_html($text) {
    return htmlspecialchars($text, ENT_QUOTES, 'UTF-8');
}

function checked($checked, $current = true, $echo = true) {
    $result = checked_selected_helper($checked, $current, $checked, 'checked');
    if ($echo) {
        echo $result;
    }
    return $result;
}

function checked_selected_helper($helper, $current, $echo, $type) {
    if ((string) $helper === (string) $current) {
        return " $type='$type'";
    }
    return '';
}

echo "Starting plugin test...\n";

try {
    // Include the main plugin file
    include 'wp-health-seo-sentinel.php';
    
    echo "✓ Plugin loaded successfully!\n";
    echo "✓ No fatal errors detected\n";
    
    // Test if the main class exists
    if (class_exists('WP_Health_SEO_Sentinel')) {
        echo "✓ Main plugin class exists\n";
        
        // Try to instantiate the class
        $plugin = new WP_Health_SEO_Sentinel();
        echo "✓ Plugin class instantiated successfully\n";
        
        // Test if key methods exist
        $methods = ['init', 'admin_menu', 'render_dashboard_page'];
        foreach ($methods as $method) {
            if (method_exists($plugin, $method)) {
                echo "✓ Method '$method' exists\n";
            } else {
                echo "✗ Method '$method' missing\n";
            }
        }
        
    } else {
        echo "✗ Main plugin class not found\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
} catch (ParseError $e) {
    echo "✗ Parse Error: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "✗ Fatal Error: " . $e->getMessage() . "\n";
}

echo "\nPlugin test completed.\n";
