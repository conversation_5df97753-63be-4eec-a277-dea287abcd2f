# WP Health SEO Sentinel - Minimal Version

A comprehensive WordPress SEO plugin that scans your website for SEO issues and provides AI-powered optimization suggestions.

## Features

### Core SEO Scanning
- **Meta Tags Analysis**: Checks title tags, meta descriptions, and their optimal lengths
- **Content Analysis**: Analyzes content quality, readability, and keyword usage
- **Technical SEO**: Checks SSL certificates, sitemaps, robots.txt, and mobile responsiveness
- **Image Optimization**: Identifies images without alt text and oversized images
- **Social Media**: Verifies Open Graph and Twitter Card tags
- **Schema Markup**: Detects structured data implementation
- **Broken Links**: Identifies broken internal and external links
- **Heading Structure**: Analyzes H1-H6 tag hierarchy
- **Permalink Structure**: Checks URL structure optimization

### AI Integration
- **Multiple AI Providers**: Supports OpenAI, Anthropic Claude, Google AI, Mistral, and custom providers
- **Smart Optimization**: AI-powered title and meta description optimization
- **Content Analysis**: Intelligent content quality assessment
- **Keyword Suggestions**: AI-generated keyword recommendations

### Dashboard & Reporting
- **Health Score**: Overall SEO health percentage
- **Issue Categorization**: Critical, Warning, and Info level issues
- **Detailed Reports**: Comprehensive scan results with actionable insights
- **Quick Actions**: One-click access to common SEO tasks

### Settings & Configuration
- **Flexible Settings**: Configurable scan frequency, timeouts, and preferences
- **AI Configuration**: Easy setup for multiple AI providers
- **User-Friendly Interface**: Clean, modern admin interface
- **Settings Test Page**: Simple interface to test and verify settings

## Installation

1. Upload the `wp-health-seo-sentinel-minimal` folder to your `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Navigate to 'SEO Health' in your WordPress admin menu
4. Configure your settings and run your first SEO scan

## Configuration

### Basic Setup
1. Go to **SEO Health > Settings Test** for a simple settings interface
2. Configure your preferred AI provider (optional but recommended)
3. Set your scan frequency and notification preferences

### AI Integration Setup
1. Choose your preferred AI provider (OpenAI, Anthropic, etc.)
2. Enter your API key
3. Configure model settings and temperature
4. Enable specific AI features like title optimization and content analysis

## Usage

### Running SEO Scans
1. Go to **SEO Health > Scanner**
2. Click "Run Full SEO Scan"
3. Review the results categorized by severity
4. Use the "Fix" buttons for automated solutions
5. Follow manual fix instructions for complex issues

### Viewing Reports
1. Access **SEO Health > Dashboard** for an overview
2. Check your overall health score
3. Review recent content and quick actions
4. Monitor issue trends over time

### Using AI Features
1. Ensure AI provider is configured
2. AI suggestions appear automatically in scan results
3. Use AI-optimized titles and meta descriptions
4. Get intelligent keyword recommendations

## File Structure

```
wp-health-seo-sentinel-minimal/
├── wp-health-seo-sentinel.php    # Main plugin file
├── settings-test.php             # Simple settings test page
├── test-plugin.php              # Plugin functionality test
├── css/
│   └── admin.css                # Admin interface styles
├── js/
│   └── admin.js                 # Admin interface JavaScript
└── README.md                    # This file
```

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- cURL extension for AI API calls
- SSL certificate (recommended)

## AI Provider Setup

### OpenAI
1. Get API key from https://platform.openai.com/
2. Enter key in Settings Test page
3. Choose model (gpt-4, gpt-3.5-turbo, etc.)

### Anthropic Claude
1. Get API key from https://console.anthropic.com/
2. Enter key in Settings Test page
3. Choose model (claude-3-sonnet, claude-3-haiku, etc.)

## Support

For support and feature requests, please refer to the plugin documentation or contact the development team.

## Version

Current Version: 1.0.0
Last Updated: January 2025

## License

This plugin is licensed under the GPL v2 or later.
