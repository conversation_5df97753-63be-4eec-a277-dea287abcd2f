<?php
/**
 * Scanner class for WP Health & SEO Sentinel Pro
 * Performs real-time scanning without database operations
 */
class WP_HSS_Pro_Scanner {
    /**
     * Initialize the scanner
     */
    public function init() {
        // Nothing to initialize
    }

    /**
     * AJAX handler for scanning the site
     */
    public function ajax_scan_site() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_pro_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed.'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
        }

        // Get scan type
        $scan_type = isset($_POST['scan_type']) ? sanitize_text_field($_POST['scan_type']) : 'full';

        // Get scan results
        $results = $this->scan_site($scan_type);

        // Send response
        wp_send_json_success(array(
            'results' => $results,
            'counts' => array(
                'critical' => count($results['critical']),
                'warning' => count($results['warning']),
                'info' => count($results['info']),
                'total' => count($results['critical']) + count($results['warning']) + count($results['info'])
            ),
            'health_score' => $this->calculate_health_score($results)
        ));
    }

    /**
     * Scan the site for SEO issues
     */
    public function scan_site($scan_type = 'full') {
        $results = array(
            'critical' => array(),
            'warning' => array(),
            'info' => array()
        );

        // Perform different scans based on scan type
        switch ($scan_type) {
            case 'quick':
                $this->scan_ssl($results);
                $this->scan_sitemap($results);
                $this->scan_robots_txt($results);
                $this->scan_homepage($results);
                break;

            case 'content':
                $this->scan_content($results);
                break;

            case 'technical':
                $this->scan_ssl($results);
                $this->scan_sitemap($results);
                $this->scan_robots_txt($results);
                $this->scan_performance($results);
                break;

            case 'full':
            default:
                $this->scan_ssl($results);
                $this->scan_sitemap($results);
                $this->scan_robots_txt($results);
                $this->scan_homepage($results);
                $this->scan_content($results);
                $this->scan_performance($results);
                $this->scan_mobile($results);
                $this->scan_schema($results);
                $this->scan_social($results);
                break;
        }

        return $results;
    }

    /**
     * Calculate health score based on issues
     */
    private function calculate_health_score($results) {
        $critical_count = count($results['critical']);
        $warning_count = count($results['warning']);
        $info_count = count($results['info']);

        // Calculate score (100 - deductions)
        $score = 100;

        // Deduct for critical issues (10 points each)
        $score -= $critical_count * 10;

        // Deduct for warnings (5 points each)
        $score -= $warning_count * 5;

        // Deduct for info issues (1 point each)
        $score -= $info_count * 1;

        // Ensure score is between 0 and 100
        $score = max(0, min(100, $score));

        return $score;
    }

    /**
     * Scan SSL configuration
     */
    private function scan_ssl(&$results) {
        // Check if site is using HTTPS
        $is_ssl = is_ssl();

        if (!$is_ssl) {
            $results['critical'][] = array(
                'id' => 'ssl_missing',
                'type' => 'ssl',
                'title' => 'Missing SSL Certificate',
                'description' => 'Your site is not using HTTPS. This is a critical SEO and security issue.',
                'details' => 'Search engines prefer secure websites. Not having SSL may negatively impact your rankings and show insecure warnings to visitors.',
                'priority' => 1,
                'fixable' => true,
                'fix_type' => 'manual',
                'fix_instructions' => 'Contact your hosting provider to install an SSL certificate, then update your WordPress site URL to use https://'
            );
        }
    }

    /**
     * Scan sitemap configuration
     */
    private function scan_sitemap(&$results) {
        $home_url = home_url();
        $sitemap_url = $home_url . '/sitemap.xml';
        $sitemap_index_url = $home_url . '/sitemap_index.xml';

        // Check if Yoast SEO is active
        $has_yoast = defined('WPSEO_VERSION');

        // Check if Rank Math is active
        $has_rank_math = defined('RANK_MATH_VERSION');

        // Check if All in One SEO is active
        $has_aioseo = defined('AIOSEO_VERSION');

        if (!$has_yoast && !$has_rank_math && !$has_aioseo) {
            // Try to fetch sitemap
            $response = wp_remote_get($sitemap_url, array('timeout' => 5));
            $sitemap_exists = !is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200;

            if (!$sitemap_exists) {
                // Try sitemap index
                $response = wp_remote_get($sitemap_index_url, array('timeout' => 5));
                $sitemap_index_exists = !is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200;

                if (!$sitemap_index_exists) {
                    $results['critical'][] = array(
                        'id' => 'sitemap_missing',
                        'type' => 'sitemap',
                        'title' => 'XML Sitemap Missing',
                        'description' => 'No XML sitemap detected. A sitemap helps search engines discover and index your content.',
                        'details' => 'XML sitemaps are essential for SEO as they help search engines find and index your content more efficiently.',
                        'priority' => 2,
                        'fixable' => true,
                        'fix_type' => 'plugin',
                        'fix_instructions' => 'Install and configure an SEO plugin like Yoast SEO, Rank Math, or All in One SEO to generate a sitemap.'
                    );
                }
            }
        }
    }

    /**
     * Scan robots.txt configuration
     */
    private function scan_robots_txt(&$results) {
        $home_url = home_url();
        $robots_url = $home_url . '/robots.txt';

        // Try to fetch robots.txt
        $response = wp_remote_get($robots_url, array('timeout' => 5));

        if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
            $results['warning'][] = array(
                'id' => 'robots_txt_missing',
                'type' => 'robots_txt',
                'title' => 'Robots.txt Missing',
                'description' => 'No robots.txt file found or it\'s not accessible.',
                'details' => 'A robots.txt file helps control how search engines crawl your site. Without it, search engines may index pages you don\'t want indexed.',
                'priority' => 3,
                'fixable' => true,
                'fix_type' => 'automatic',
                'fix_instructions' => 'We can generate a basic robots.txt file for your site.'
            );
        } else {
            // Check robots.txt content
            $robots_content = wp_remote_retrieve_body($response);

            // Check if sitemap is referenced in robots.txt
            if (strpos($robots_content, 'Sitemap:') === false) {
                $results['warning'][] = array(
                    'id' => 'robots_txt_no_sitemap',
                    'type' => 'robots_txt',
                    'title' => 'Sitemap Not Referenced in Robots.txt',
                    'description' => 'Your robots.txt file does not reference your XML sitemap.',
                    'details' => 'Adding a Sitemap directive to your robots.txt helps search engines find your sitemap more easily.',
                    'priority' => 4,
                    'fixable' => true,
                    'fix_type' => 'automatic',
                    'fix_instructions' => 'We can add a Sitemap directive to your robots.txt file.'
                );
            }
        }
    }

    /**
     * Scan homepage SEO
     */
    private function scan_homepage(&$results) {
        // Get homepage
        $homepage_id = get_option('page_on_front');
        if (!$homepage_id) {
            // If no static homepage, use latest posts
            $homepage_id = 0;
        }

        // Get homepage URL
        $homepage_url = home_url();

        // Fetch homepage content
        $response = wp_remote_get($homepage_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            // Create DOMDocument for parsing
            $dom = new DOMDocument();
            @$dom->loadHTML($homepage_content);

            // Check title
            $title_tags = $dom->getElementsByTagName('title');
            if ($title_tags->length > 0) {
                $title = $title_tags->item(0)->nodeValue;
                $title_length = mb_strlen($title);

                if ($title_length < 30) {
                    $results['warning'][] = array(
                        'id' => 'homepage_title_short',
                        'type' => 'title',
                        'title' => 'Homepage Title Too Short',
                        'description' => "Your homepage title is only $title_length characters. Recommended minimum is 30 characters.",
                        'details' => 'Short titles may not provide enough information for search engines and users. Consider making it more descriptive.',
                        'priority' => 5,
                        'fixable' => true,
                        'fix_type' => 'automatic',
                        'fix_instructions' => 'We can generate an optimized title for your homepage.',
                        'post_id' => $homepage_id,
                        'current_value' => $title
                    );
                } elseif ($title_length > 60) {
                    $results['warning'][] = array(
                        'id' => 'homepage_title_long',
                        'type' => 'title',
                        'title' => 'Homepage Title Too Long',
                        'description' => "Your homepage title is $title_length characters. Recommended maximum is 60 characters.",
                        'details' => 'Long titles may be truncated in search results. Consider making it more concise.',
                        'priority' => 6,
                        'fixable' => true,
                        'fix_type' => 'automatic',
                        'fix_instructions' => 'We can generate an optimized title for your homepage.',
                        'post_id' => $homepage_id,
                        'current_value' => $title
                    );
                }
            } else {
                $results['critical'][] = array(
                    'id' => 'homepage_title_missing',
                    'type' => 'title',
                    'title' => 'Homepage Title Missing',
                    'description' => 'Your homepage does not have a title tag.',
                    'details' => 'The title tag is one of the most important SEO elements. It tells search engines what your page is about.',
                    'priority' => 7,
                    'fixable' => true,
                    'fix_type' => 'automatic',
                    'fix_instructions' => 'We can generate an optimized title for your homepage.',
                    'post_id' => $homepage_id
                );
            }

            // Check meta description
            $meta_tags = $dom->getElementsByTagName('meta');
            $has_description = false;
            $description = '';

            foreach ($meta_tags as $meta_tag) {
                if ($meta_tag->getAttribute('name') === 'description') {
                    $has_description = true;
                    $description = $meta_tag->getAttribute('content');
                    break;
                }
            }

            if (!$has_description) {
                $results['warning'][] = array(
                    'id' => 'homepage_meta_description_missing',
                    'type' => 'meta_description',
                    'title' => 'Homepage Meta Description Missing',
                    'description' => 'Your homepage does not have a meta description.',
                    'details' => 'Meta descriptions provide a summary of your page for search engine results. Without one, search engines will create their own snippet.',
                    'priority' => 8,
                    'fixable' => true,
                    'fix_type' => 'automatic',
                    'fix_instructions' => 'We can generate an optimized meta description for your homepage.',
                    'post_id' => $homepage_id
                );
            } elseif (mb_strlen($description) < 120) {
                $results['warning'][] = array(
                    'id' => 'homepage_meta_description_short',
                    'type' => 'meta_description',
                    'title' => 'Homepage Meta Description Too Short',
                    'description' => 'Your homepage meta description is only ' . mb_strlen($description) . ' characters. Recommended minimum is 120 characters.',
                    'details' => 'Short meta descriptions may not provide enough information for users to click on your result in search engines.',
                    'priority' => 9,
                    'fixable' => true,
                    'fix_type' => 'automatic',
                    'fix_instructions' => 'We can generate an optimized meta description for your homepage.',
                    'post_id' => $homepage_id,
                    'current_value' => $description
                );
            } elseif (mb_strlen($description) > 160) {
                $results['warning'][] = array(
                    'id' => 'homepage_meta_description_long',
                    'type' => 'meta_description',
                    'title' => 'Homepage Meta Description Too Long',
                    'description' => 'Your homepage meta description is ' . mb_strlen($description) . ' characters. Recommended maximum is 160 characters.',
                    'details' => 'Long meta descriptions may be truncated in search results. Consider making it more concise.',
                    'priority' => 10,
                    'fixable' => true,
                    'fix_type' => 'automatic',
                    'fix_instructions' => 'We can generate an optimized meta description for your homepage.',
                    'post_id' => $homepage_id,
                    'current_value' => $description
                );
            }
        }
    }

    /**
     * Scan content SEO
     */
    private function scan_content(&$results) {
        // Get recent posts
        $recent_posts = get_posts(array(
            'post_type' => 'post',
            'posts_per_page' => 5,
            'post_status' => 'publish'
        ));

        if (empty($recent_posts)) {
            return;
        }

        $posts_without_images = array();
        $posts_with_short_content = array();
        $posts_with_keyword_issues = array();

        foreach ($recent_posts as $post) {
            // Check for featured image
            if (!has_post_thumbnail($post->ID)) {
                $posts_without_images[] = array(
                    'id' => $post->ID,
                    'title' => $post->post_title
                );
            }

            // Check content length
            $content = strip_tags($post->post_content);
            $word_count = str_word_count($content);

            if ($word_count < 300) {
                $posts_with_short_content[] = array(
                    'id' => $post->ID,
                    'title' => $post->post_title,
                    'word_count' => $word_count
                );
            }

            // Check keyword density (simplified)
            // In a real implementation, this would be more sophisticated
            $title_words = explode(' ', strtolower($post->post_title));
            $content_words = explode(' ', strtolower($content));

            $potential_keywords = array();
            foreach ($title_words as $word) {
                if (strlen($word) > 3) {
                    $count = 0;
                    foreach ($content_words as $content_word) {
                        if ($content_word === $word) {
                            $count++;
                        }
                    }

                    $density = $count / count($content_words) * 100;

                    if ($density < 0.5 || $density > 3) {
                        $potential_keywords[$word] = $density;
                    }
                }
            }

            if (!empty($potential_keywords)) {
                $posts_with_keyword_issues[] = array(
                    'id' => $post->ID,
                    'title' => $post->post_title,
                    'keywords' => $potential_keywords
                );
            }
        }

        // Add issues for posts without images
        if (!empty($posts_without_images)) {
            $post_list = '';
            foreach ($posts_without_images as $index => $post) {
                if ($index > 0) {
                    $post_list .= ', ';
                }
                $post_list .= '"' . $post['title'] . '"';
            }

            $results['warning'][] = array(
                'id' => 'posts_without_images',
                'type' => 'content',
                'title' => 'Posts Without Featured Images',
                'description' => count($posts_without_images) . ' recent posts do not have featured images: ' . $post_list,
                'details' => 'Featured images are important for social sharing and visual appeal. They can increase click-through rates from search results and social media.',
                'priority' => 11,
                'fixable' => false,
                'fix_type' => 'manual',
                'fix_instructions' => 'Add featured images to these posts.'
            );
        }

        // Add issues for posts with short content
        if (!empty($posts_with_short_content)) {
            $post_list = '';
            foreach ($posts_with_short_content as $index => $post) {
                if ($index > 0) {
                    $post_list .= ', ';
                }
                $post_list .= '"' . $post['title'] . '" (' . $post['word_count'] . ' words)';
            }

            $results['warning'][] = array(
                'id' => 'posts_with_short_content',
                'type' => 'content',
                'title' => 'Posts With Short Content',
                'description' => count($posts_with_short_content) . ' recent posts have less than 300 words: ' . $post_list,
                'details' => 'Longer content tends to rank better in search engines. Aim for at least 300 words for most posts, and 1000+ words for comprehensive guides.',
                'priority' => 12,
                'fixable' => false,
                'fix_type' => 'manual',
                'fix_instructions' => 'Expand the content of these posts to provide more value to readers and search engines.'
            );
        }

        // Add issues for posts with keyword issues
        if (!empty($posts_with_keyword_issues)) {
            $post_list = '';
            foreach ($posts_with_keyword_issues as $index => $post) {
                if ($index > 0) {
                    $post_list .= ', ';
                }
                $post_list .= '"' . $post['title'] . '"';
            }

            $results['info'][] = array(
                'id' => 'posts_with_keyword_issues',
                'type' => 'content',
                'title' => 'Posts With Keyword Optimization Issues',
                'description' => count($posts_with_keyword_issues) . ' recent posts have potential keyword optimization issues: ' . $post_list,
                'details' => 'Proper keyword usage is important for SEO. Keywords should appear in your content at a natural density (usually 0.5% to 3%).',
                'priority' => 13,
                'fixable' => false,
                'fix_type' => 'manual',
                'fix_instructions' => 'Review these posts and optimize keyword usage. Ensure keywords appear in titles, headings, and throughout the content at a natural density.'
            );
        }
    }

    /**
     * Scan performance
     */
    private function scan_performance(&$results) {
        // Check for caching plugin
        $caching_plugins = array(
            'wp-super-cache/wp-cache.php',
            'w3-total-cache/w3-total-cache.php',
            'wp-fastest-cache/wpFastestCache.php',
            'litespeed-cache/litespeed-cache.php',
            'autoptimize/autoptimize.php',
            'cache-enabler/cache-enabler.php',
            'wp-rocket/wp-rocket.php'
        );

        $active_caching_plugin = false;
        foreach ($caching_plugins as $plugin) {
            if (is_plugin_active($plugin)) {
                $active_caching_plugin = true;
                break;
            }
        }

        if (!$active_caching_plugin) {
            $results['warning'][] = array(
                'id' => 'no_caching_plugin',
                'type' => 'performance',
                'title' => 'No Caching Plugin Detected',
                'description' => 'Your site does not appear to be using a caching plugin.',
                'details' => 'Caching can significantly improve your site\'s loading speed, which is a ranking factor for search engines.',
                'priority' => 14,
                'fixable' => false,
                'fix_type' => 'manual',
                'fix_instructions' => 'Install and configure a caching plugin like WP Super Cache, W3 Total Cache, or LiteSpeed Cache.'
            );
        }

        // Check for image optimization plugin
        $image_optimization_plugins = array(
            'wp-smushit/wp-smush.php',
            'imagify/imagify.php',
            'ewww-image-optimizer/ewww-image-optimizer.php',
            'shortpixel-image-optimiser/wp-shortpixel.php',
            'tiny-compress-images/tiny-compress-images.php'
        );

        $active_image_optimization_plugin = false;
        foreach ($image_optimization_plugins as $plugin) {
            if (is_plugin_active($plugin)) {
                $active_image_optimization_plugin = true;
                break;
            }
        }

        if (!$active_image_optimization_plugin) {
            $results['info'][] = array(
                'id' => 'no_image_optimization_plugin',
                'type' => 'performance',
                'title' => 'No Image Optimization Plugin Detected',
                'description' => 'Your site does not appear to be using an image optimization plugin.',
                'details' => 'Optimizing images can reduce page size and improve loading times, which is important for both user experience and SEO.',
                'priority' => 15,
                'fixable' => false,
                'fix_type' => 'manual',
                'fix_instructions' => 'Install and configure an image optimization plugin like Smush, Imagify, or ShortPixel.'
            );
        }
    }

    /**
     * Scan mobile-friendliness
     */
    private function scan_mobile(&$results) {
        // Check for responsive theme
        $theme = wp_get_theme();
        $theme_tags = $theme->get('Tags');

        $is_responsive = false;
        if (is_array($theme_tags)) {
            $is_responsive = in_array('responsive-layout', $theme_tags) ||
                            in_array('responsive', $theme_tags) ||
                            in_array('mobile', $theme_tags);
        }

        if (!$is_responsive) {
            $results['warning'][] = array(
                'id' => 'theme_not_responsive',
                'type' => 'mobile',
                'title' => 'Theme May Not Be Mobile-Friendly',
                'description' => 'Your theme "' . $theme->get('Name') . '" does not explicitly state that it is responsive or mobile-friendly.',
                'details' => 'Mobile-friendliness is a ranking factor for search engines. Ensure your site displays properly on all devices.',
                'priority' => 16,
                'fixable' => false,
                'fix_type' => 'manual',
                'fix_instructions' => 'Consider switching to a responsive theme or test your site using Google\'s Mobile-Friendly Test.'
            );
        }

        // Check viewport meta tag
        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            if (strpos($homepage_content, 'viewport') === false) {
                $results['critical'][] = array(
                    'id' => 'viewport_meta_missing',
                    'type' => 'mobile',
                    'title' => 'Viewport Meta Tag Missing',
                    'description' => 'Your site is missing the viewport meta tag, which is essential for mobile responsiveness.',
                    'details' => 'The viewport meta tag tells browsers how to adjust the page dimensions and scaling to suit the device.',
                    'priority' => 17,
                    'fixable' => true,
                    'fix_type' => 'code',
                    'fix_instructions' => 'Add the following code to your theme\'s header: <meta name="viewport" content="width=device-width, initial-scale=1">'
                );
            }
        }
    }

    /**
     * Scan schema markup
     */
    private function scan_schema(&$results) {
        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            // Check for schema markup
            $has_schema = strpos($homepage_content, 'schema.org') !== false ||
                         strpos($homepage_content, 'application/ld+json') !== false;

            if (!$has_schema) {
                $results['info'][] = array(
                    'id' => 'schema_markup_missing',
                    'type' => 'schema',
                    'title' => 'Schema Markup Missing',
                    'description' => 'Your site does not appear to be using schema markup.',
                    'details' => 'Schema markup helps search engines understand your content better and can result in rich snippets in search results.',
                    'priority' => 18,
                    'fixable' => false,
                    'fix_type' => 'manual',
                    'fix_instructions' => 'Add appropriate schema markup to your site. You can use plugins like Yoast SEO or Schema Pro, or manually add the markup.'
                );
            }
        }
    }

    /**
     * Scan social media integration
     */
    private function scan_social(&$results) {
        $home_url = home_url();
        $response = wp_remote_get($home_url, array('timeout' => 5));

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $homepage_content = wp_remote_retrieve_body($response);

            // Check for Open Graph tags
            $has_og = strpos($homepage_content, 'og:') !== false;

            if (!$has_og) {
                $results['info'][] = array(
                    'id' => 'open_graph_missing',
                    'type' => 'social',
                    'title' => 'Open Graph Tags Missing',
                    'description' => 'Your site does not appear to be using Open Graph tags.',
                    'details' => 'Open Graph tags control how your content appears when shared on social media platforms like Facebook.',
                    'priority' => 19,
                    'fixable' => true,
                    'fix_type' => 'plugin',
                    'fix_instructions' => 'Install and configure an SEO plugin like Yoast SEO or Rank Math that adds Open Graph tags.'
                );
            }

            // Check for Twitter Card tags
            $has_twitter = strpos($homepage_content, 'twitter:') !== false;

            if (!$has_twitter) {
                $results['info'][] = array(
                    'id' => 'twitter_card_missing',
                    'type' => 'social',
                    'title' => 'Twitter Card Tags Missing',
                    'description' => 'Your site does not appear to be using Twitter Card tags.',
                    'details' => 'Twitter Card tags control how your content appears when shared on Twitter.',
                    'priority' => 20,
                    'fixable' => true,
                    'fix_type' => 'plugin',
                    'fix_instructions' => 'Install and configure an SEO plugin like Yoast SEO or Rank Math that adds Twitter Card tags.'
                );
            }
        }
    }