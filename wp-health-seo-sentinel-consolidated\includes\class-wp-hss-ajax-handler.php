<?php
/**
 * AJAX Handler for WP Health & SEO Sentinel
 *
 * Handles all AJAX requests for the plugin.
 *
 * @since      1.0.0
 */
class WP_HSS_AJAX_Handler {

    /**
     * Initialize the class.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // Register AJAX handlers
        add_action('wp_ajax_wp_hss_get_issues', array($this, 'ajax_get_issues'));
        add_action('wp_ajax_wp_hss_get_scan_results', array($this, 'ajax_get_scan_results'));
        add_action('wp_ajax_wp_hss_get_scan_history', array($this, 'ajax_get_scan_history'));
    }

    /**
     * AJAX handler for getting issues by severity.
     *
     * @since    1.0.0
     */
    public function ajax_get_issues() {
        try {
            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_ajax_nonce')) {
                wp_send_json_error(array('message' => 'Security check failed.'));
                return;
            }

            // Check user capabilities
            if (!current_user_can('manage_options')) {
                wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
                return;
            }

            // Get severity filter
            $severity = isset($_POST['severity']) ? sanitize_text_field($_POST['severity']) : '';

            // Get issues from database
            $issues = $this->get_issues_by_severity($severity);

            // Format issues for display
            $html = $this->format_issues_html($issues, $severity);

            wp_send_json_success(array(
                'html' => $html,
                'count' => count($issues)
            ));
        } catch (Exception $e) {
            error_log('WP HSS: Error in ajax_get_issues: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'An error occurred: ' . $e->getMessage()));
        }
    }

    /**
     * AJAX handler for getting scan results.
     *
     * @since    1.0.0
     */
    public function ajax_get_scan_results() {
        try {
            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_ajax_nonce')) {
                wp_send_json_error(array('message' => 'Security check failed.'));
                return;
            }

            // Check user capabilities
            if (!current_user_can('manage_options')) {
                wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
                return;
            }

            // Get scan ID
            $scan_id = isset($_POST['scan_id']) ? sanitize_text_field($_POST['scan_id']) : '';

            if (empty($scan_id)) {
                wp_send_json_error(array('message' => 'Invalid scan ID.'));
                return;
            }

            // Get scan results from database
            $issues = $this->get_scan_results($scan_id);

            // Format issues for display
            $html = $this->format_issues_html($issues);

            wp_send_json_success(array(
                'html' => $html,
                'count' => count($issues)
            ));
        } catch (Exception $e) {
            error_log('WP HSS: Error in ajax_get_scan_results: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'An error occurred: ' . $e->getMessage()));
        }
    }

    /**
     * AJAX handler for getting scan history.
     *
     * @since    1.0.0
     */
    public function ajax_get_scan_history() {
        try {
            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_ajax_nonce')) {
                wp_send_json_error(array('message' => 'Security check failed.'));
                return;
            }

            // Check user capabilities
            if (!current_user_can('manage_options')) {
                wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
                return;
            }

            // Get scan history
            $history = $this->get_scan_history();

            // Format history for display
            $html = $this->format_history_html($history);

            wp_send_json_success(array(
                'html' => $html,
                'count' => count($history)
            ));
        } catch (Exception $e) {
            error_log('WP HSS: Error in ajax_get_scan_history: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'An error occurred: ' . $e->getMessage()));
        }
    }

    /**
     * Get issues by severity.
     *
     * @since    1.0.0
     * @param    string    $severity    Severity filter.
     * @return   array                  Issues.
     */
    private function get_issues_by_severity($severity) {
        // Sample issues data for demonstration
        $sample_issues = $this->get_sample_issues();
        
        // Filter by severity if specified
        if (!empty($severity)) {
            $filtered_issues = array();
            foreach ($sample_issues as $issue) {
                if ($issue['severity'] === $severity) {
                    $filtered_issues[] = $issue;
                }
            }
            return $filtered_issues;
        }
        
        return $sample_issues;
    }

    /**
     * Get scan results.
     *
     * @since    1.0.0
     * @param    string    $scan_id    Scan ID.
     * @return   array                 Issues.
     */
    private function get_scan_results($scan_id) {
        // Sample issues data for demonstration
        return $this->get_sample_issues();
    }

    /**
     * Get scan history.
     *
     * @since    1.0.0
     * @return   array    Scan history.
     */
    private function get_scan_history() {
        // Sample history data for demonstration
        return array(
            array(
                'id' => 'sample1',
                'date' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'issues_count' => 16,
                'health_score' => 78
            ),
            array(
                'id' => 'sample2',
                'date' => date('Y-m-d H:i:s', strtotime('-3 days')),
                'issues_count' => 22,
                'health_score' => 65
            ),
            array(
                'id' => 'sample3',
                'date' => date('Y-m-d H:i:s', strtotime('-7 days')),
                'issues_count' => 35,
                'health_score' => 52
            )
        );
    }

    /**
     * Format issues HTML.
     *
     * @since    1.0.0
     * @param    array     $issues      Issues.
     * @param    string    $severity    Severity filter.
     * @return   string                 HTML.
     */
    private function format_issues_html($issues, $severity = '') {
        $html = '';
        
        if (empty($issues)) {
            $html .= '<div class="wp-hss-info-box">';
            $html .= '<h3><i class="dashicons dashicons-info"></i> No Issues Found</h3>';
            
            if (!empty($severity)) {
                $html .= '<p>No ' . esc_html($severity) . ' issues were found.</p>';
            } else {
                $html .= '<p>No issues were found in the scan.</p>';
            }
            
            $html .= '</div>';
            return $html;
        }
        
        // Group issues by type for better organization
        $grouped_issues = array();
        foreach ($issues as $issue) {
            $type = $issue['issue_type'];
            if (!isset($grouped_issues[$type])) {
                $grouped_issues[$type] = array();
            }
            $grouped_issues[$type][] = $issue;
        }
        
        // Sort groups by priority
        $priority_order = array(
            'title_length' => 10,
            'meta_description' => 9,
            'heading_tags' => 8,
            'content_quality' => 7,
            'keyword_optimization' => 6,
            'image_alt_text' => 5,
            'internal_links' => 4,
            'schema_markup' => 3,
            'mobile_friendly' => 2,
            'page_speed' => 1
        );
        
        // Sort groups by priority
        uksort($grouped_issues, function($a, $b) use ($priority_order) {
            $a_priority = isset($priority_order[$a]) ? $priority_order[$a] : 0;
            $b_priority = isset($priority_order[$b]) ? $priority_order[$b] : 0;
            return $b_priority - $a_priority;
        });
        
        // Build HTML
        $html .= '<div class="wp-hss-card">';
        $html .= '<h2><i class="dashicons dashicons-list-view"></i> SEO Issues Found</h2>';
        $html .= '<div class="wp-hss-card-content">';
        
        // Add filter options
        $html .= '<div class="wp-hss-filter-options">';
        $html .= '<label><strong>Filter by severity:</strong></label>';
        $html .= '<select class="wp-hss-severity-filter">';
        $html .= '<option value="">All Issues</option>';
        $html .= '<option value="critical"' . selected($severity, 'critical', false) . '>Critical Issues</option>';
        $html .= '<option value="warning"' . selected($severity, 'warning', false) . '>Warnings</option>';
        $html .= '<option value="info"' . selected($severity, 'info', false) . '>Suggestions</option>';
        $html .= '</select>';
        $html .= '</div>';
        
        // Add issues table
        $html .= '<table class="wp-hss-results-table">';
        $html .= '<thead>';
        $html .= '<tr>';
        $html .= '<th>Issue</th>';
        $html .= '<th>Severity</th>';
        $html .= '<th>Page</th>';
        $html .= '<th>Actions</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody>';
        
        foreach ($grouped_issues as $type => $type_issues) {
            // Add group header
            $html .= '<tr class="wp-hss-issue-group-header">';
            $html .= '<td colspan="4"><strong>' . $this->get_issue_type_label($type) . '</strong> (' . count($type_issues) . ' issues)</td>';
            $html .= '</tr>';
            
            foreach ($type_issues as $issue) {
                $html .= '<tr>';
                $html .= '<td>' . esc_html($issue['message']) . '</td>';
                $html .= '<td><span class="wp-hss-severity wp-hss-severity-' . esc_attr($issue['severity']) . '">' . ucfirst(esc_html($issue['severity'])) . '</span></td>';
                $html .= '<td>' . esc_html($issue['post_title']) . '</td>';
                $html .= '<td>';
                $html .= '<button class="wp-hss-fix-button" data-issue-id="' . esc_attr($issue['id']) . '"><i class="dashicons dashicons-admin-tools"></i> Fix</button>';
                $html .= '<div class="wp-hss-issue-details-toggle"><i class="dashicons dashicons-arrow-right"></i> Show Details</div>';
                $html .= '<div class="wp-hss-issue-details" style="display:none;">';
                
                // Add issue details
                if (isset($issue['details'])) {
                    foreach ($issue['details'] as $key => $value) {
                        if ($key === 'message') continue; // Skip message as it's already displayed
                        
                        $html .= '<p><strong>' . ucfirst(str_replace('_', ' ', esc_html($key))) . ':</strong> ';
                        
                        if (is_array($value)) {
                            $html .= '<br>' . implode('<br>', array_map('esc_html', $value));
                        } else {
                            $html .= esc_html($value);
                        }
                        
                        $html .= '</p>';
                    }
                }
                
                $html .= '</div>';
                $html .= '</td>';
                $html .= '</tr>';
            }
        }
        
        $html .= '</tbody>';
        $html .= '</table>';
        
        // Add fix all button
        $html .= '<div class="wp-hss-fix-all-button">';
        $html .= '<button class="wp-hss-button wp-hss-button-success"><i class="dashicons dashicons-admin-tools"></i> Fix All Issues</button>';
        $html .= '</div>';
        
        $html .= '</div>'; // Close card content
        $html .= '</div>'; // Close card
        
        return $html;
    }

    /**
     * Format history HTML.
     *
     * @since    1.0.0
     * @param    array    $history    Scan history.
     * @return   string               HTML.
     */
    private function format_history_html($history) {
        $html = '';
        
        if (empty($history)) {
            $html .= '<div class="wp-hss-info-box">';
            $html .= '<h3><i class="dashicons dashicons-info"></i> No Scan History</h3>';
            $html .= '<p>You haven\'t performed any scans yet. Run your first scan to see results here.</p>';
            $html .= '</div>';
            return $html;
        }
        
        $html .= '<table class="wp-hss-results-table">';
        $html .= '<thead>';
        $html .= '<tr>';
        $html .= '<th>Date</th>';
        $html .= '<th>Issues Found</th>';
        $html .= '<th>Health Score</th>';
        $html .= '<th>Actions</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody>';
        
        foreach ($history as $scan) {
            $html .= '<tr>';
            $html .= '<td>' . date('M j, Y g:i a', strtotime($scan['date'])) . '</td>';
            $html .= '<td>' . esc_html($scan['issues_count']) . '</td>';
            $html .= '<td>' . esc_html($scan['health_score']) . '</td>';
            $html .= '<td>';
            $html .= '<button class="wp-hss-button wp-hss-button-sm wp-hss-button-outline wp-hss-view-scan" data-scan-id="' . esc_attr($scan['id']) . '">';
            $html .= '<i class="dashicons dashicons-visibility"></i> View';
            $html .= '</button>';
            $html .= '</td>';
            $html .= '</tr>';
        }
        
        $html .= '</tbody>';
        $html .= '</table>';
        
        return $html;
    }

    /**
     * Get issue type label.
     *
     * @since    1.0.0
     * @param    string    $type    Issue type.
     * @return   string             Label.
     */
    private function get_issue_type_label($type) {
        $labels = array(
            'title_length' => 'Title Length Issues',
            'meta_description' => 'Meta Description Issues',
            'heading_tags' => 'Heading Structure Issues',
            'content_quality' => 'Content Quality Issues',
            'keyword_optimization' => 'Keyword Optimization Issues',
            'image_alt_text' => 'Image Alt Text Issues',
            'internal_links' => 'Internal Linking Issues',
            'schema_markup' => 'Schema Markup Issues',
            'mobile_friendly' => 'Mobile-Friendliness Issues',
            'page_speed' => 'Page Speed Issues',
            'sitemap_robots' => 'Sitemap & Robots.txt Issues',
            'ssl_issues' => 'SSL Issues',
            'canonical_tag' => 'Canonical Tag Issues'
        );
        
        return isset($labels[$type]) ? $labels[$type] : ucfirst(str_replace('_', ' ', $type));
    }

    /**
     * Get sample issues.
     *
     * @since    1.0.0
     * @return   array    Sample issues.
     */
    private function get_sample_issues() {
        return array(
            // Critical issues
            array(
                'id' => 'sample1',
                'post_id' => 1,
                'post_title' => 'Home Page',
                'issue_type' => 'ssl_issues',
                'severity' => 'critical',
                'message' => 'Site is not using HTTPS. Switch to HTTPS to improve security and SEO.',
                'details' => array(
                    'message' => 'Site is not using HTTPS. Switch to HTTPS to improve security and SEO.',
                    'current_url' => 'http://example.com',
                    'recommended_action' => 'Install SSL certificate and configure WordPress to use HTTPS.'
                )
            ),
            array(
                'id' => 'sample2',
                'post_id' => 0,
                'post_title' => 'Site-wide',
                'issue_type' => 'sitemap_robots',
                'severity' => 'critical',
                'message' => 'No XML sitemap found. Create a sitemap to help search engines discover your content.',
                'details' => array(
                    'message' => 'No XML sitemap found. Create a sitemap to help search engines discover your content.',
                    'recommended_action' => 'Enable XML sitemap generation in your SEO plugin or create a sitemap manually.'
                )
            ),
            array(
                'id' => 'sample3',
                'post_id' => 2,
                'post_title' => 'About Us',
                'issue_type' => 'title_length',
                'severity' => 'critical',
                'message' => 'Page title is too long (75 characters). Recommended length is 30-60 characters.',
                'details' => array(
                    'message' => 'Page title is too long (75 characters). Recommended length is 30-60 characters.',
                    'current_title' => 'About Us - Learn About Our Company History, Mission, Values, and Team Members',
                    'current_length' => 75,
                    'recommended_length' => '30-60',
                    'recommended_action' => 'Shorten the title to focus on the most important keywords.'
                )
            ),
            
            // Warning issues
            array(
                'id' => 'sample4',
                'post_id' => 3,
                'post_title' => 'Services',
                'issue_type' => 'meta_description',
                'severity' => 'warning',
                'message' => 'Meta description is missing.',
                'details' => array(
                    'message' => 'Meta description is missing.',
                    'current_value' => '',
                    'recommended_action' => 'Add a meta description that summarizes the page content and includes important keywords.'
                )
            ),
            array(
                'id' => 'sample5',
                'post_id' => 4,
                'post_title' => 'Blog',
                'issue_type' => 'heading_tags',
                'severity' => 'warning',
                'message' => 'Multiple H1 tags found. There should be only one H1 tag per page.',
                'details' => array(
                    'message' => 'Multiple H1 tags found. There should be only one H1 tag per page.',
                    'h1_count' => 3,
                    'h1_tags' => array(
                        'Our Blog',
                        'Latest Articles',
                        'Featured Posts'
                    ),
                    'recommended_action' => 'Keep only one H1 tag and convert others to H2 or H3 tags.'
                )
            ),
            array(
                'id' => 'sample6',
                'post_id' => 5,
                'post_title' => 'Contact',
                'issue_type' => 'image_alt_text',
                'severity' => 'warning',
                'message' => '3 images in content do not have alt text.',
                'details' => array(
                    'message' => '3 images in content do not have alt text.',
                    'images' => array(
                        '/wp-content/uploads/2023/01/contact-map.jpg',
                        '/wp-content/uploads/2023/01/office-building.jpg',
                        '/wp-content/uploads/2023/01/team-photo.jpg'
                    ),
                    'recommended_action' => 'Add descriptive alt text to all images for better accessibility and SEO.'
                )
            ),
            array(
                'id' => 'sample7',
                'post_id' => 6,
                'post_title' => 'Products',
                'issue_type' => 'internal_links',
                'severity' => 'warning',
                'message' => 'No internal links found in content. Consider adding internal links for better SEO.',
                'details' => array(
                    'message' => 'No internal links found in content. Consider adding internal links for better SEO.',
                    'content_length' => 1500,
                    'recommended_action' => 'Add links to related pages or products to improve internal linking structure.'
                )
            ),
            array(
                'id' => 'sample8',
                'post_id' => 7,
                'post_title' => 'FAQ',
                'issue_type' => 'schema_markup',
                'severity' => 'warning',
                'message' => 'No schema markup found. Consider adding schema markup for better search visibility.',
                'details' => array(
                    'message' => 'No schema markup found. Consider adding schema markup for better search visibility.',
                    'recommended_schema' => 'FAQPage',
                    'recommended_action' => 'Add FAQ schema markup to improve rich snippet opportunities in search results.'
                )
            ),
            
            // Info/Suggestion issues
            array(
                'id' => 'sample9',
                'post_id' => 8,
                'post_title' => 'Testimonials',
                'issue_type' => 'content_quality',
                'severity' => 'info',
                'message' => 'Content is too short (250 words). Aim for at least 300 words for better SEO.',
                'details' => array(
                    'message' => 'Content is too short (250 words). Aim for at least 300 words for better SEO.',
                    'word_count' => 250,
                    'recommended_min' => 300,
                    'recommended_action' => 'Expand the content with more valuable information for users and search engines.'
                )
            ),
            array(
                'id' => 'sample10',
                'post_id' => 9,
                'post_title' => 'Portfolio',
                'issue_type' => 'keyword_optimization',
                'severity' => 'info',
                'message' => 'Primary keyword "portfolio" has low density (0.4%). Aim for 1-2% keyword density.',
                'details' => array(
                    'message' => 'Primary keyword "portfolio" has low density (0.4%). Aim for 1-2% keyword density.',
                    'keyword' => 'portfolio',
                    'current_density' => '0.4%',
                    'recommended_density' => '1-2%',
                    'recommended_action' => 'Naturally incorporate the keyword more frequently in the content.'
                )
            ),
            array(
                'id' => 'sample11',
                'post_id' => 10,
                'post_title' => 'Privacy Policy',
                'issue_type' => 'page_speed',
                'severity' => 'info',
                'message' => 'Slow load time (3200ms). Aim for under 2000ms for better user experience.',
                'details' => array(
                    'message' => 'Slow load time (3200ms). Aim for under 2000ms for better user experience.',
                    'load_time' => '3200ms',
                    'recommended_max' => '2000ms',
                    'issues' => array(
                        'Large images without dimensions',
                        'Render-blocking JavaScript',
                        'No browser caching'
                    ),
                    'recommended_action' => 'Optimize images, defer JavaScript loading, and enable browser caching.'
                )
            ),
            array(
                'id' => 'sample12',
                'post_id' => 11,
                'post_title' => 'Terms of Service',
                'issue_type' => 'mobile_friendly',
                'severity' => 'info',
                'message' => 'Mobile-friendliness issues: Small font sizes detected.',
                'details' => array(
                    'message' => 'Mobile-friendliness issues: Small font sizes detected.',
                    'issues' => array(
                        'Font size too small (10px)',
                        'Touch elements too close together'
                    ),
                    'recommended_action' => 'Increase font size to at least 16px and ensure touch elements have adequate spacing.'
                )
            )
        );
    }
}

// Initialize the AJAX handler
new WP_HSS_AJAX_Handler();
