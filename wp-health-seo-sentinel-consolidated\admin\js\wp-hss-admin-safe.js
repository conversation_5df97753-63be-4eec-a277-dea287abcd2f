/**
 * Safe mode admin JavaScript for WP Health & SEO Sentinel
 */
jQuery(document).ready(function($) {
    // Handle scan button click
    $('#wp-hss-scan-button').on('click', function() {
        var $button = $(this);
        var $results = $('#wp-hss-scan-results');
        
        // Disable button and show loading state
        $button.prop('disabled', true).text('Scanning...');
        
        // Simulate scanning process
        setTimeout(function() {
            // Show results
            $results.slideDown(300);
            
            // Re-enable button
            $button.prop('disabled', false).text('Start Scan');
            
            // Scroll to results
            $('html, body').animate({
                scrollTop: $results.offset().top - 50
            }, 500);
        }, 2000);
    });
    
    // Handle fix buttons
    $(document).on('click', '.wp-hss-issues-list button', function() {
        var $button = $(this);
        
        // Disable button and show loading state
        $button.prop('disabled', true).text('Fixing...');
        
        // Simulate fixing process
        setTimeout(function() {
            // Show success state
            $button.text('Fixed').addClass('button-disabled');
            
            // Add success icon
            $button.before('<span class="dashicons dashicons-yes" style="color: green; margin-left: 5px;"></span>');
        }, 1500);
    });
});
