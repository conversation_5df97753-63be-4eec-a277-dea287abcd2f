<?php
/**
 * Plugin Name: WP Health & SEO Sentinel Pro
 * Description: Professional SEO scanner and optimizer with no database operations
 * Version: 1.0.0
 * Author: SEO Experts
 */

// If this file is called directly, abort
if (!defined('WPINC')) {
    die;
}

// Define plugin constants
define('WP_HSS_PRO_VERSION', '1.0.0');
define('WP_HSS_PRO_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('WP_HSS_PRO_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WP_HSS_PRO_PLUGIN_FILE', __FILE__);

// Include required files
require_once WP_HSS_PRO_PLUGIN_DIR . 'includes/class-wp-hss-pro-scanner.php';
require_once WP_HSS_PRO_PLUGIN_DIR . 'includes/class-wp-hss-pro-fixer.php';
require_once WP_HSS_PRO_PLUGIN_DIR . 'includes/class-wp-hss-pro-admin.php';
require_once WP_HSS_PRO_PLUGIN_DIR . 'includes/class-wp-hss-pro-tools.php';

/**
 * Main plugin class
 */
class WP_Health_SEO_Sentinel_Pro {
    /**
     * Plugin instance
     */
    private static $instance = null;
    
    /**
     * Scanner instance
     */
    public $scanner = null;
    
    /**
     * Fixer instance
     */
    public $fixer = null;
    
    /**
     * Admin instance
     */
    public $admin = null;
    
    /**
     * Tools instance
     */
    public $tools = null;
    
    /**
     * Get plugin instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Initialize components
        $this->scanner = new WP_HSS_Pro_Scanner();
        $this->fixer = new WP_HSS_Pro_Fixer();
        $this->admin = new WP_HSS_Pro_Admin();
        $this->tools = new WP_HSS_Pro_Tools();
        
        // Register activation/deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // Initialize plugin
        add_action('plugins_loaded', array($this, 'init'));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // No database operations
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // No database operations
        flush_rewrite_rules();
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain
        load_plugin_textdomain('wp-hss-pro', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Initialize components
        $this->scanner->init();
        $this->fixer->init();
        $this->admin->init();
        $this->tools->init();
        
        // Register AJAX handlers
        add_action('wp_ajax_wp_hss_pro_scan_site', array($this->scanner, 'ajax_scan_site'));
        add_action('wp_ajax_wp_hss_pro_fix_issue', array($this->fixer, 'ajax_fix_issue'));
        add_action('wp_ajax_wp_hss_pro_fix_all_issues', array($this->fixer, 'ajax_fix_all_issues'));
        add_action('wp_ajax_wp_hss_pro_run_tool', array($this->tools, 'ajax_run_tool'));
    }
}

// Initialize plugin
function wp_health_seo_sentinel_pro() {
    return WP_Health_SEO_Sentinel_Pro::get_instance();
}

// Start the plugin
wp_health_seo_sentinel_pro();
