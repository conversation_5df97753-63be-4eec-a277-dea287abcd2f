<?php
/**
 * Tools class for WP Health & SEO Sentinel Pro
 * Provides additional SEO tools without database operations
 */
class WP_HSS_Pro_Tools {
    /**
     * Initialize the tools
     */
    public function init() {
        // Nothing to initialize
    }

    /**
     * AJAX handler for running a tool
     */
    public function ajax_run_tool() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_pro_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed.'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
        }

        // Get tool data
        $tool_id = isset($_POST['tool_id']) ? sanitize_text_field($_POST['tool_id']) : '';
        $tool_data = isset($_POST['tool_data']) ? $_POST['tool_data'] : array();

        if (empty($tool_id)) {
            wp_send_json_error(array('message' => 'Invalid tool data.'));
        }

        // Run the tool
        $result = $this->run_tool($tool_id, $tool_data);

        if ($result['success']) {
            wp_send_json_success(array(
                'message' => $result['message'],
                'data' => isset($result['data']) ? $result['data'] : array(),
                'html' => isset($result['html']) ? $result['html'] : ''
            ));
        } else {
            wp_send_json_error(array('message' => $result['message']));
        }
    }

    /**
     * Run a tool
     */
    private function run_tool($tool_id, $tool_data = array()) {
        // Run based on tool ID
        switch ($tool_id) {
            case 'bulk_title_optimizer':
                return $this->run_bulk_title_optimizer($tool_data);

            case 'meta_description_generator':
                return $this->run_meta_description_generator($tool_data);

            case 'image_alt_text_generator':
                return $this->run_image_alt_text_generator($tool_data);

            case 'schema_markup_generator':
                return $this->run_schema_markup_generator($tool_data);

            case 'content_optimizer':
                return $this->run_content_optimizer($tool_data);

            case 'internal_link_analyzer':
                return $this->run_internal_link_analyzer($tool_data);

            default:
                return array(
                    'success' => false,
                    'message' => 'Unknown tool ID.'
                );
        }
    }

    /**
     * Run bulk title optimizer
     */
    private function run_bulk_title_optimizer($tool_data) {
        // Get post types to optimize
        $post_types = isset($tool_data['post_types']) ? $tool_data['post_types'] : array('post', 'page');

        // Get posts
        $args = array(
            'post_type' => $post_types,
            'post_status' => 'publish',
            'posts_per_page' => 10,
            'orderby' => 'date',
            'order' => 'DESC'
        );

        $posts = get_posts($args);

        if (empty($posts)) {
            return array(
                'success' => false,
                'message' => 'No posts found to optimize.'
            );
        }

        // Optimize titles
        $optimized_titles = array();

        foreach ($posts as $post) {
            // Generate optimized title
            $current_title = $post->post_title;
            $new_title = $this->generate_optimized_title($post);

            // Add to results
            $optimized_titles[] = array(
                'post_id' => $post->ID,
                'post_title' => $current_title,
                'new_title' => $new_title,
                'post_type' => $post->post_type,
                'post_url' => get_permalink($post->ID)
            );
        }

        // Generate HTML
        $html = '<table class="wp-list-table widefat fixed striped">';
        $html .= '<thead><tr>';
        $html .= '<th>Post Title</th>';
        $html .= '<th>Optimized Title</th>';
        $html .= '<th>Post Type</th>';
        $html .= '<th>Actions</th>';
        $html .= '</tr></thead>';
        $html .= '<tbody>';

        foreach ($optimized_titles as $item) {
            $html .= '<tr>';
            $html .= '<td><a href="' . esc_url($item['post_url']) . '" target="_blank">' . esc_html($item['post_title']) . '</a></td>';
            $html .= '<td>' . esc_html($item['new_title']) . '</td>';
            $html .= '<td>' . esc_html(ucfirst($item['post_type'])) . '</td>';
            $html .= '<td><button class="button wp-hss-apply-title" data-post-id="' . esc_attr($item['post_id']) . '">Apply</button></td>';
            $html .= '</tr>';
        }

        $html .= '</tbody></table>';

        return array(
            'success' => true,
            'message' => 'Generated ' . count($optimized_titles) . ' optimized titles.',
            'data' => $optimized_titles,
            'html' => $html
        );
    }

    /**
     * Run meta description generator
     */
    private function run_meta_description_generator($tool_data) {
        // Get post types to optimize
        $post_types = isset($tool_data['post_types']) ? $tool_data['post_types'] : array('post', 'page');

        // Get posts
        $args = array(
            'post_type' => $post_types,
            'post_status' => 'publish',
            'posts_per_page' => 10,
            'orderby' => 'date',
            'order' => 'DESC'
        );

        $posts = get_posts($args);

        if (empty($posts)) {
            return array(
                'success' => false,
                'message' => 'No posts found to optimize.'
            );
        }

        // Generate meta descriptions
        $meta_descriptions = array();

        foreach ($posts as $post) {
            // Get current meta description
            $current_meta = get_post_meta($post->ID, '_yoast_wpseo_metadesc', true);
            if (empty($current_meta)) {
                $current_meta = '';
            }

            // Generate optimized meta description
            $new_meta = $this->generate_optimized_meta_description($post);

            // Add to results
            $meta_descriptions[] = array(
                'post_id' => $post->ID,
                'post_title' => $post->post_title,
                'current_meta' => $current_meta,
                'new_meta' => $new_meta,
                'post_type' => $post->post_type,
                'post_url' => get_permalink($post->ID)
            );
        }

        // Generate HTML
        $html = '<table class="wp-list-table widefat fixed striped">';
        $html .= '<thead><tr>';
        $html .= '<th>Post Title</th>';
        $html .= '<th>Current Meta Description</th>';
        $html .= '<th>Optimized Meta Description</th>';
        $html .= '<th>Actions</th>';
        $html .= '</tr></thead>';
        $html .= '<tbody>';

        foreach ($meta_descriptions as $item) {
            $html .= '<tr>';
            $html .= '<td><a href="' . esc_url($item['post_url']) . '" target="_blank">' . esc_html($item['post_title']) . '</a></td>';
            $html .= '<td>' . (empty($item['current_meta']) ? '<em>None</em>' : esc_html($item['current_meta'])) . '</td>';
            $html .= '<td>' . esc_html($item['new_meta']) . '</td>';
            $html .= '<td><button class="button wp-hss-apply-meta" data-post-id="' . esc_attr($item['post_id']) . '">Apply</button></td>';
            $html .= '</tr>';
        }

        $html .= '</tbody></table>';

        return array(
            'success' => true,
            'message' => 'Generated ' . count($meta_descriptions) . ' optimized meta descriptions.',
            'data' => $meta_descriptions,
            'html' => $html
        );
    }

    /**
     * Run image alt text generator
     */
    private function run_image_alt_text_generator($tool_data) {
        // Get images without alt text
        $args = array(
            'post_type' => 'attachment',
            'post_mime_type' => 'image',
            'post_status' => 'inherit',
            'posts_per_page' => 10,
            'meta_query' => array(
                array(
                    'key' => '_wp_attachment_image_alt',
                    'compare' => 'NOT EXISTS'
                )
            )
        );

        $images = get_posts($args);

        if (empty($images)) {
            return array(
                'success' => false,
                'message' => 'No images found without alt text.'
            );
        }

        // Generate alt text
        $alt_texts = array();

        foreach ($images as $image) {
            // Get image data
            $image_url = wp_get_attachment_url($image->ID);
            $image_title = $image->post_title;
            $image_caption = $image->post_excerpt;
            $image_description = $image->post_content;

            // Generate alt text
            $alt_text = $this->generate_image_alt_text($image);

            // Add to results
            $alt_texts[] = array(
                'image_id' => $image->ID,
                'image_title' => $image_title,
                'image_url' => $image_url,
                'alt_text' => $alt_text
            );
        }

        // Generate HTML
        $html = '<table class="wp-list-table widefat fixed striped">';
        $html .= '<thead><tr>';
        $html .= '<th>Image</th>';
        $html .= '<th>Title</th>';
        $html .= '<th>Generated Alt Text</th>';
        $html .= '<th>Actions</th>';
        $html .= '</tr></thead>';
        $html .= '<tbody>';

        foreach ($alt_texts as $item) {
            $html .= '<tr>';
            $html .= '<td><img src="' . esc_url($item['image_url']) . '" style="max-width: 100px; max-height: 100px;"></td>';
            $html .= '<td>' . esc_html($item['image_title']) . '</td>';
            $html .= '<td>' . esc_html($item['alt_text']) . '</td>';
            $html .= '<td><button class="button wp-hss-apply-alt" data-image-id="' . esc_attr($item['image_id']) . '">Apply</button></td>';
            $html .= '</tr>';
        }

        $html .= '</tbody></table>';

        return array(
            'success' => true,
            'message' => 'Generated alt text for ' . count($alt_texts) . ' images.',
            'data' => $alt_texts,
            'html' => $html
        );
    }

    /**
     * Run schema markup generator
     */
    private function run_schema_markup_generator($tool_data) {
        // Get schema type
        $schema_type = isset($tool_data['schema_type']) ? $tool_data['schema_type'] : 'Article';

        // Generate schema markup
        $schema_markup = $this->generate_schema_markup($schema_type);

        // Generate HTML
        $html = '<div class="wp-hss-schema-result">';
        $html .= '<h3>Generated Schema Markup</h3>';
        $html .= '<p>Copy and paste this code into your theme\'s header.php file or use a plugin to add it to your site.</p>';
        $html .= '<textarea style="width: 100%; height: 200px; font-family: monospace;">' . esc_textarea($schema_markup) . '</textarea>';
        $html .= '<p><button class="button button-primary wp-hss-copy-schema">Copy to Clipboard</button></p>';
        $html .= '</div>';

        return array(
            'success' => true,
            'message' => 'Generated ' . $schema_type . ' schema markup.',
            'data' => array(
                'schema_type' => $schema_type,
                'schema_markup' => $schema_markup
            ),
            'html' => $html
        );
    }

    /**
     * Run content optimizer
     */
    private function run_content_optimizer($tool_data) {
        // Get post ID
        $post_id = isset($tool_data['post_id']) ? intval($tool_data['post_id']) : 0;

        if (!$post_id) {
            return array(
                'success' => false,
                'message' => 'No post selected to optimize.'
            );
        }

        // Get post
        $post = get_post($post_id);

        if (!$post) {
            return array(
                'success' => false,
                'message' => 'Post not found.'
            );
        }

        // Analyze content
        $analysis = $this->analyze_content($post);

        // Generate HTML
        $html = '<div class="wp-hss-content-analysis">';
        $html .= '<h3>Content Analysis for: ' . esc_html($post->post_title) . '</h3>';

        // Content length
        $html .= '<div class="wp-hss-analysis-item">';
        $html .= '<h4>Content Length</h4>';
        $html .= '<p>' . $analysis['word_count'] . ' words</p>';
        $html .= '<div class="wp-hss-progress-bar">';
        $html .= '<div class="wp-hss-progress" style="width: ' . min(100, ($analysis['word_count'] / 10)) . '%; background-color: ' . ($analysis['word_count'] < 300 ? '#dba617' : '#00a32a') . ';"></div>';
        $html .= '</div>';
        $html .= '<p class="wp-hss-recommendation">' . $analysis['length_recommendation'] . '</p>';
        $html .= '</div>';

        // Readability
        $html .= '<div class="wp-hss-analysis-item">';
        $html .= '<h4>Readability</h4>';
        $html .= '<p>Score: ' . $analysis['readability_score'] . '/100</p>';
        $html .= '<div class="wp-hss-progress-bar">';
        $html .= '<div class="wp-hss-progress" style="width: ' . $analysis['readability_score'] . '%; background-color: ' . ($analysis['readability_score'] < 60 ? '#dba617' : '#00a32a') . ';"></div>';
        $html .= '</div>';
        $html .= '<p class="wp-hss-recommendation">' . $analysis['readability_recommendation'] . '</p>';
        $html .= '</div>';

        // Keyword usage
        $html .= '<div class="wp-hss-analysis-item">';
        $html .= '<h4>Keyword Usage</h4>';
        $html .= '<p>Primary keyword: <strong>' . esc_html($analysis['primary_keyword']) . '</strong> (Density: ' . $analysis['keyword_density'] . '%)</p>';
        $html .= '<div class="wp-hss-progress-bar">';
        $html .= '<div class="wp-hss-progress" style="width: ' . min(100, ($analysis['keyword_density'] * 33)) . '%; background-color: ' . (($analysis['keyword_density'] < 0.5 || $analysis['keyword_density'] > 3) ? '#dba617' : '#00a32a') . ';"></div>';
        $html .= '</div>';
        $html .= '<p class="wp-hss-recommendation">' . $analysis['keyword_recommendation'] . '</p>';
        $html .= '</div>';

        // Headings
        $html .= '<div class="wp-hss-analysis-item">';
        $html .= '<h4>Heading Structure</h4>';
        $html .= '<p>' . $analysis['headings_count'] . ' headings found</p>';
        $html .= '<ul>';
        foreach ($analysis['headings'] as $heading_type => $count) {
            $html .= '<li>' . $heading_type . ': ' . $count . '</li>';
        }
        $html .= '</ul>';
        $html .= '<p class="wp-hss-recommendation">' . $analysis['headings_recommendation'] . '</p>';
        $html .= '</div>';

        // Images
        $html .= '<div class="wp-hss-analysis-item">';
        $html .= '<h4>Images</h4>';
        $html .= '<p>' . $analysis['images_count'] . ' images found</p>';
        $html .= '<p>' . $analysis['images_with_alt'] . ' images have alt text</p>';
        $html .= '<p class="wp-hss-recommendation">' . $analysis['images_recommendation'] . '</p>';
        $html .= '</div>';

        // Links
        $html .= '<div class="wp-hss-analysis-item">';
        $html .= '<h4>Links</h4>';
        $html .= '<p>Internal links: ' . $analysis['internal_links_count'] . '</p>';
        $html .= '<p>External links: ' . $analysis['external_links_count'] . '</p>';
        $html .= '<p class="wp-hss-recommendation">' . $analysis['links_recommendation'] . '</p>';
        $html .= '</div>';

        $html .= '</div>';

        return array(
            'success' => true,
            'message' => 'Content analysis complete.',
            'data' => $analysis,
            'html' => $html
        );
    }

    /**
     * Run internal link analyzer
     */
    private function run_internal_link_analyzer($tool_data) {
        // Get posts
        $args = array(
            'post_type' => array('post', 'page'),
            'post_status' => 'publish',
            'posts_per_page' => 20,
            'orderby' => 'date',
            'order' => 'DESC'
        );

        $posts = get_posts($args);

        if (empty($posts)) {
            return array(
                'success' => false,
                'message' => 'No posts found to analyze.'
            );
        }

        // Analyze internal links
        $link_data = array();
        $site_url = get_site_url();

        foreach ($posts as $post) {
            // Get post content
            $content = $post->post_content;

            // Count internal links
            preg_match_all('/<a[^>]+href=([\'"])' . preg_quote($site_url, '/') . '[^>]+>/i', $content, $internal_links);
            $internal_links_count = count($internal_links[0]);

            // Count external links
            preg_match_all('/<a[^>]+href=([\'"])(https?:\/\/(?!' . preg_quote(parse_url($site_url, PHP_URL_HOST), '/') . ')[^>]+)/i', $content, $external_links);
            $external_links_count = count($external_links[0]);

            // Count incoming links
            $incoming_links_count = 0;
            foreach ($posts as $other_post) {
                if ($other_post->ID === $post->ID) {
                    continue;
                }

                if (strpos($other_post->post_content, get_permalink($post->ID)) !== false) {
                    $incoming_links_count++;
                }
            }

            // Add to results
            $link_data[] = array(
                'post_id' => $post->ID,
                'post_title' => $post->post_title,
                'post_type' => $post->post_type,
                'post_url' => get_permalink($post->ID),
                'internal_links_count' => $internal_links_count,
                'external_links_count' => $external_links_count,
                'incoming_links_count' => $incoming_links_count,
                'total_links' => $internal_links_count + $external_links_count,
                'needs_improvement' => ($internal_links_count < 2 || $incoming_links_count < 1)
            );
        }

        // Sort by needs improvement and then by incoming links
        usort($link_data, function($a, $b) {
            if ($a['needs_improvement'] !== $b['needs_improvement']) {
                return $b['needs_improvement'] - $a['needs_improvement'];
            }
            return $a['incoming_links_count'] - $b['incoming_links_count'];
        });

        // Generate HTML
        $html = '<table class="wp-list-table widefat fixed striped">';
        $html .= '<thead><tr>';
        $html .= '<th>Post Title</th>';
        $html .= '<th>Internal Links (Outgoing)</th>';
        $html .= '<th>External Links</th>';
        $html .= '<th>Incoming Links</th>';
        $html .= '<th>Status</th>';
        $html .= '</tr></thead>';
        $html .= '<tbody>';

        foreach ($link_data as $item) {
            $status_class = $item['needs_improvement'] ? 'wp-hss-status-warning' : 'wp-hss-status-good';
            $status_text = $item['needs_improvement'] ? 'Needs Improvement' : 'Good';

            $html .= '<tr>';
            $html .= '<td><a href="' . esc_url($item['post_url']) . '" target="_blank">' . esc_html($item['post_title']) . '</a></td>';
            $html .= '<td>' . $item['internal_links_count'] . '</td>';
            $html .= '<td>' . $item['external_links_count'] . '</td>';
            $html .= '<td>' . $item['incoming_links_count'] . '</td>';
            $html .= '<td><span class="' . $status_class . '">' . $status_text . '</span></td>';
            $html .= '</tr>';
        }

        $html .= '</tbody></table>';

        // Add recommendations
        $html .= '<div class="wp-hss-link-recommendations">';
        $html .= '<h3>Recommendations</h3>';
        $html .= '<ul>';
        $html .= '<li>Pages with fewer than 2 internal links need more internal linking.</li>';
        $html .= '<li>Pages with no incoming links are orphaned and should be linked to from other pages.</li>';
        $html .= '<li>Important pages should have more incoming links to distribute page authority.</li>';
        $html .= '</ul>';
        $html .= '</div>';

        return array(
            'success' => true,
            'message' => 'Internal link analysis complete.',
            'data' => $link_data,
            'html' => $html
        );
    }

    /**
     * Generate optimized title
     */
    private function generate_optimized_title($post) {
        // Get post data
        $post_title = $post->post_title;
        $post_type = $post->post_type;

        // Get site name
        $site_name = get_bloginfo('name');

        // Generate title based on post type
        switch ($post_type) {
            case 'post':
                // For blog posts, use the format: "Post Title | Site Name"
                $new_title = $post_title . ' | ' . $site_name;

                // Ensure title is not too long
                if (mb_strlen($new_title) > 60) {
                    $new_title = mb_substr($post_title, 0, 50) . '... | ' . $site_name;
                }
                break;

            case 'page':
                // For pages, use the format: "Page Title - Site Name"
                $new_title = $post_title . ' - ' . $site_name;

                // Ensure title is not too long
                if (mb_strlen($new_title) > 60) {
                    $new_title = mb_substr($post_title, 0, 50) . '... - ' . $site_name;
                }
                break;

            case 'product':
                // For products, use the format: "Product Name - Buy Online | Site Name"
                $new_title = $post_title . ' - Buy Online | ' . $site_name;

                // Ensure title is not too long
                if (mb_strlen($new_title) > 60) {
                    $new_title = mb_substr($post_title, 0, 40) . '... - Buy Online | ' . $site_name;
                }
                break;

            default:
                // For other post types, use the format: "Title | Site Name"
                $new_title = $post_title . ' | ' . $site_name;

                // Ensure title is not too long
                if (mb_strlen($new_title) > 60) {
                    $new_title = mb_substr($post_title, 0, 50) . '... | ' . $site_name;
                }
                break;
        }

        return $new_title;
    }

    /**
     * Generate optimized meta description
     */
    private function generate_optimized_meta_description($post) {
        // Get post data
        $post_title = $post->post_title;
        $post_content = $post->post_content;
        $post_excerpt = $post->post_excerpt;
        $post_type = $post->post_type;

        // Use excerpt if available, otherwise use content
        $content = !empty($post_excerpt) ? $post_excerpt : $post_content;

        // Strip HTML tags and shortcodes
        $content = strip_tags($content);
        $content = strip_shortcodes($content);

        // Remove extra whitespace
        $content = preg_replace('/\s+/', ' ', $content);
        $content = trim($content);

        // Generate description based on post type
        switch ($post_type) {
            case 'post':
                // For blog posts, use the first 150 characters of content
                $description = mb_substr($content, 0, 150);

                // Add ellipsis if truncated
                if (mb_strlen($content) > 150) {
                    $description .= '...';
                }

                // If description is too short, add a generic description
                if (mb_strlen($description) < 120) {
                    $description = 'Read our article about ' . $post_title . ' to learn more about this topic. We provide detailed information and insights to help you understand ' . mb_strtolower($post_title) . ' better.';
                }
                break;

            case 'page':
                // For pages, use the first 150 characters of content
                $description = mb_substr($content, 0, 150);

                // Add ellipsis if truncated
                if (mb_strlen($content) > 150) {
                    $description .= '...';
                }

                // If description is too short, add a generic description
                if (mb_strlen($description) < 120) {
                    $description = 'Learn more about ' . $post_title . ' on our website. We provide comprehensive information about ' . mb_strtolower($post_title) . ' to help you make informed decisions.';
                }
                break;

            case 'product':
                // For products, create a sales-oriented description
                $description = 'Shop for ' . $post_title . ' online. We offer competitive prices, fast shipping, and excellent customer service. Buy ' . mb_strtolower($post_title) . ' today!';
                break;

            default:
                // For other post types, use the first 150 characters of content
                $description = mb_substr($content, 0, 150);

                // Add ellipsis if truncated
                if (mb_strlen($content) > 150) {
                    $description .= '...';
                }

                // If description is too short, add a generic description
                if (mb_strlen($description) < 120) {
                    $description = 'Learn more about ' . $post_title . ' on our website. We provide detailed information about ' . mb_strtolower($post_title) . ' to help you understand this topic better.';
                }
                break;
        }

        return $description;
    }

    /**
     * Generate image alt text
     */
    private function generate_image_alt_text($image) {
        // Get image data
        $image_title = $image->post_title;
        $image_caption = $image->post_excerpt;
        $image_description = $image->post_content;

        // Use caption if available
        if (!empty($image_caption)) {
            return $image_caption;
        }

        // Use title if available
        if (!empty($image_title)) {
            return $image_title;
        }

        // Use description if available
        if (!empty($image_description)) {
            // Use first sentence of description
            $first_sentence = preg_replace('/^(.*?[.!?]).*$/s', '$1', $image_description);
            return $first_sentence;
        }

        // Use generic alt text
        return 'Image for ' . get_bloginfo('name');
    }

    /**
     * Generate schema markup
     */
    private function generate_schema_markup($schema_type) {
        // Get site data
        $site_name = get_bloginfo('name');
        $site_description = get_bloginfo('description');
        $site_url = home_url();
        $site_logo = '';

        // Try to get site logo
        $custom_logo_id = get_theme_mod('custom_logo');
        if ($custom_logo_id) {
            $logo_image = wp_get_attachment_image_src($custom_logo_id, 'full');
            if ($logo_image) {
                $site_logo = $logo_image[0];
            }
        }

        // Generate schema based on type
        switch ($schema_type) {
            case 'Article':
                $schema = array(
                    '@context' => 'https://schema.org',
                    '@type' => 'Article',
                    'headline' => 'ARTICLE_TITLE',
                    'description' => 'ARTICLE_DESCRIPTION',
                    'image' => 'ARTICLE_FEATURED_IMAGE',
                    'datePublished' => 'ARTICLE_PUBLISH_DATE',
                    'dateModified' => 'ARTICLE_MODIFIED_DATE',
                    'author' => array(
                        '@type' => 'Person',
                        'name' => 'AUTHOR_NAME'
                    ),
                    'publisher' => array(
                        '@type' => 'Organization',
                        'name' => $site_name,
                        'logo' => array(
                            '@type' => 'ImageObject',
                            'url' => $site_logo
                        )
                    ),
                    'mainEntityOfPage' => array(
                        '@type' => 'WebPage',
                        '@id' => 'ARTICLE_URL'
                    )
                );
                break;

            case 'Product':
                $schema = array(
                    '@context' => 'https://schema.org',
                    '@type' => 'Product',
                    'name' => 'PRODUCT_NAME',
                    'description' => 'PRODUCT_DESCRIPTION',
                    'image' => 'PRODUCT_IMAGE',
                    'sku' => 'PRODUCT_SKU',
                    'brand' => array(
                        '@type' => 'Brand',
                        'name' => 'BRAND_NAME'
                    ),
                    'offers' => array(
                        '@type' => 'Offer',
                        'price' => 'PRODUCT_PRICE',
                        'priceCurrency' => 'USD',
                        'availability' => 'https://schema.org/InStock',
                        'url' => 'PRODUCT_URL'
                    ),
                    'aggregateRating' => array(
                        '@type' => 'AggregateRating',
                        'ratingValue' => 'RATING_VALUE',
                        'reviewCount' => 'REVIEW_COUNT'
                    )
                );
                break;

            case 'LocalBusiness':
                $schema = array(
                    '@context' => 'https://schema.org',
                    '@type' => 'LocalBusiness',
                    'name' => $site_name,
                    'description' => $site_description,
                    'url' => $site_url,
                    'logo' => $site_logo,
                    'image' => $site_logo,
                    'address' => array(
                        '@type' => 'PostalAddress',
                        'streetAddress' => 'STREET_ADDRESS',
                        'addressLocality' => 'CITY',
                        'addressRegion' => 'STATE',
                        'postalCode' => 'ZIP_CODE',
                        'addressCountry' => 'COUNTRY'
                    ),
                    'telephone' => 'PHONE_NUMBER',
                    'openingHours' => 'Mo-Fr 09:00-17:00',
                    'priceRange' => '$$'
                );
                break;

            default:
                $schema = array(
                    '@context' => 'https://schema.org',
                    '@type' => 'WebSite',
                    'name' => $site_name,
                    'description' => $site_description,
                    'url' => $site_url
                );
                break;
        }

        // Convert to JSON
        $schema_json = json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);

        // Add script tags
        return '<script type="application/ld+json">' . "\n" . $schema_json . "\n" . '</script>';
    }

    /**
     * Analyze content
     */
    private function analyze_content($post) {
        // Get post data
        $post_title = $post->post_title;
        $post_content = $post->post_content;
        $post_excerpt = $post->post_excerpt;

        // Strip HTML tags and shortcodes for text analysis
        $content_text = strip_tags($post_content);
        $content_text = strip_shortcodes($content_text);

        // Word count
        $word_count = str_word_count($content_text);

        // Readability score (simplified)
        $sentences = preg_split('/[.!?]+/', $content_text, -1, PREG_SPLIT_NO_EMPTY);
        $sentence_count = count($sentences);

        $avg_sentence_length = $sentence_count > 0 ? $word_count / $sentence_count : 0;

        // Simple readability score (0-100)
        $readability_score = 100;

        // Deduct for long sentences
        if ($avg_sentence_length > 20) {
            $readability_score -= min(30, ($avg_sentence_length - 20) * 3);
        }

        // Deduct for very short content
        if ($word_count < 300) {
            $readability_score -= min(30, (300 - $word_count) / 10);
        }

        // Ensure score is between 0 and 100
        $readability_score = max(0, min(100, round($readability_score)));

        // Keyword analysis
        $title_words = explode(' ', strtolower($post_title));
        $content_words = explode(' ', strtolower($content_text));

        // Find potential primary keyword
        $primary_keyword = '';
        $max_count = 0;

        foreach ($title_words as $word) {
            if (strlen($word) > 3) {
                $count = 0;
                foreach ($content_words as $content_word) {
                    if ($content_word === $word) {
                        $count++;
                    }
                }

                if ($count > $max_count) {
                    $max_count = $count;
                    $primary_keyword = $word;
                }
            }
        }

        // Calculate keyword density
        $keyword_density = $word_count > 0 ? ($max_count / $word_count) * 100 : 0;

        // Heading analysis
        preg_match_all('/<h([1-6])[^>]*>(.*?)<\/h\1>/i', $post_content, $headings);

        $headings_count = count($headings[0]);
        $headings_by_type = array();

        for ($i = 0; $i < $headings_count; $i++) {
            $heading_level = $headings[1][$i];
            $heading_type = 'H' . $heading_level;

            if (!isset($headings_by_type[$heading_type])) {
                $headings_by_type[$heading_type] = 0;
            }

            $headings_by_type[$heading_type]++;
        }

        // Image analysis
        preg_match_all('/<img[^>]+>/i', $post_content, $images);
        $images_count = count($images[0]);

        // Count images with alt text
        $images_with_alt = 0;
        foreach ($images[0] as $img) {
            if (preg_match('/alt=([\'"])(.*?)\1/i', $img)) {
                $images_with_alt++;
            }
        }

        // Link analysis
        $site_url = get_site_url();

        preg_match_all('/<a[^>]+href=([\'"])' . preg_quote($site_url, '/') . '[^>]+>/i', $post_content, $internal_links);
        $internal_links_count = count($internal_links[0]);

        preg_match_all('/<a[^>]+href=([\'"])(https?:\/\/(?!' . preg_quote(parse_url($site_url, PHP_URL_HOST), '/') . ')[^>]+)/i', $post_content, $external_links);
        $external_links_count = count($external_links[0]);

        // Generate recommendations
        $length_recommendation = '';
        if ($word_count < 300) {
            $length_recommendation = 'Your content is too short. Consider adding more content to improve SEO. Aim for at least 300 words.';
        } elseif ($word_count < 600) {
            $length_recommendation = 'Your content length is acceptable, but adding more comprehensive information could improve rankings. Aim for 600+ words for competitive topics.';
        } else {
            $length_recommendation = 'Good content length. Comprehensive content tends to rank better in search engines.';
        }

        $readability_recommendation = '';
        if ($readability_score < 60) {
            $readability_recommendation = 'Your content is difficult to read. Consider using shorter sentences and simpler language.';
        } elseif ($readability_score < 80) {
            $readability_recommendation = 'Your content readability is acceptable, but could be improved. Try to vary sentence length and structure.';
        } else {
            $readability_recommendation = 'Good readability score. Your content is easy to read and understand.';
        }

        $keyword_recommendation = '';
        if (empty($primary_keyword)) {
            $keyword_recommendation = 'No clear primary keyword detected. Consider optimizing your content around a specific keyword.';
        } elseif ($keyword_density < 0.5) {
            $keyword_recommendation = 'Your primary keyword density is too low. Consider using the keyword more frequently in your content.';
        } elseif ($keyword_density > 3) {
            $keyword_recommendation = 'Your primary keyword density is too high, which may appear as keyword stuffing. Use the keyword more naturally.';
        } else {
            $keyword_recommendation = 'Good keyword density. Your primary keyword appears at a natural frequency.';
        }

        $headings_recommendation = '';
        if ($headings_count === 0) {
            $headings_recommendation = 'No headings found. Add headings to structure your content and improve readability.';
        } elseif (!isset($headings_by_type['H1'])) {
            $headings_recommendation = 'No H1 heading found. Add an H1 heading that includes your primary keyword.';
        } elseif ($headings_by_type['H1'] > 1) {
            $headings_recommendation = 'Multiple H1 headings found. Use only one H1 heading per page.';
        } elseif ($headings_count < 3) {
            $headings_recommendation = 'Few headings found. Add more headings to better structure your content.';
        } else {
            $headings_recommendation = 'Good heading structure. Headings help organize your content and improve readability.';
        }

        $images_recommendation = '';
        if ($images_count === 0) {
            $images_recommendation = 'No images found. Add images to make your content more engaging and visually appealing.';
        } elseif ($images_with_alt < $images_count) {
            $images_recommendation = 'Some images are missing alt text. Add descriptive alt text to all images for better accessibility and SEO.';
        } else {
            $images_recommendation = 'Good image usage with alt text. Images with descriptive alt text improve accessibility and SEO.';
        }

        $links_recommendation = '';
        if ($internal_links_count === 0) {
            $links_recommendation = 'No internal links found. Add links to other relevant pages on your site to improve navigation and SEO.';
        } elseif ($internal_links_count < 2) {
            $links_recommendation = 'Few internal links found. Add more links to other relevant pages on your site.';
        } else {
            $links_recommendation = 'Good internal linking. Internal links help users navigate your site and distribute page authority.';
        }

        // Return analysis results
        return array(
            'word_count' => $word_count,
            'readability_score' => $readability_score,
            'primary_keyword' => $primary_keyword,
            'keyword_density' => round($keyword_density, 2),
            'headings_count' => $headings_count,
            'headings' => $headings_by_type,
            'images_count' => $images_count,
            'images_with_alt' => $images_with_alt,
            'internal_links_count' => $internal_links_count,
            'external_links_count' => $external_links_count,
            'length_recommendation' => $length_recommendation,
            'readability_recommendation' => $readability_recommendation,
            'keyword_recommendation' => $keyword_recommendation,
            'headings_recommendation' => $headings_recommendation,
            'images_recommendation' => $images_recommendation,
            'links_recommendation' => $links_recommendation
        );
    }
}