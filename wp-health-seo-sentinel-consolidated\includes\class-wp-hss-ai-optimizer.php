<?php
/**
 * AI Optimizer for WP Health & SEO Sentinel
 *
 * Handles AI-powered optimization for titles, meta descriptions, and content.
 *
 * @since      1.0.0
 */
class WP_HSS_AI_Optimizer {

    /**
     * Initialize the class.
     *
     * @since    1.0.0
     */
    public function __construct() {
        // Register AJAX handlers
        add_action('wp_ajax_wp_hss_optimize_title', array($this, 'ajax_optimize_title'));
        add_action('wp_ajax_wp_hss_optimize_meta_description', array($this, 'ajax_optimize_meta_description'));
        add_action('wp_ajax_wp_hss_optimize_content', array($this, 'ajax_optimize_content'));
    }

    /**
     * AJAX handler for optimizing title.
     *
     * @since    1.0.0
     */
    public function ajax_optimize_title() {
        try {
            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_ajax_nonce')) {
                wp_send_json_error(array('message' => 'Security check failed.'));
                return;
            }

            // Check user capabilities
            if (!current_user_can('manage_options')) {
                wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
                return;
            }

            // Get post ID and current title
            $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
            $current_title = isset($_POST['current_title']) ? sanitize_text_field($_POST['current_title']) : '';
            $issue_id = isset($_POST['issue_id']) ? sanitize_text_field($_POST['issue_id']) : '';

            if (empty($post_id) && empty($current_title)) {
                wp_send_json_error(array('message' => 'Missing required parameters.'));
                return;
            }

            // Get post data if post ID is provided
            if (!empty($post_id)) {
                $post = get_post($post_id);
                if (!$post) {
                    wp_send_json_error(array('message' => 'Post not found.'));
                    return;
                }
                $current_title = $post->post_title;
                $post_content = $post->post_content;
                $post_type = $post->post_type;
            } else {
                $post_content = '';
                $post_type = 'unknown';
            }

            // Get settings
            $settings = get_option('wp_hss_settings', array());
            $api_key = isset($settings['ai_api_key']) ? $settings['ai_api_key'] : '';
            $provider = isset($settings['ai_provider']) ? $settings['ai_provider'] : 'openai';
            $model = isset($settings['ai_model']) ? $settings['ai_model'] : 'gpt-4';
            $title_min_length = isset($settings['title_min_length']) ? intval($settings['title_min_length']) : 30;
            $title_max_length = isset($settings['title_max_length']) ? intval($settings['title_max_length']) : 60;

            // Check if AI is enabled and API key is set
            if (empty($api_key) || !isset($settings['ai_enable_features']) || !$settings['ai_enable_features']) {
                // Fallback to basic optimization if AI is not available
                $optimized_title = $this->optimize_title_basic($current_title, $title_min_length, $title_max_length);
                
                wp_send_json_success(array(
                    'title' => $optimized_title,
                    'message' => 'Title optimized using basic rules. Enable AI in settings for better results.',
                    'ai_used' => false
                ));
                return;
            }

            // Optimize title using AI
            $optimized_title = $this->optimize_title_with_ai($current_title, $post_content, $post_type, $api_key, $provider, $model, $title_min_length, $title_max_length);

            // Update post title if post ID is provided
            if (!empty($post_id)) {
                wp_update_post(array(
                    'ID' => $post_id,
                    'post_title' => $optimized_title
                ));
            }

            wp_send_json_success(array(
                'title' => $optimized_title,
                'message' => 'Title optimized successfully using AI.',
                'ai_used' => true,
                'issue_id' => $issue_id
            ));
        } catch (Exception $e) {
            error_log('WP HSS: Error in ajax_optimize_title: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'An error occurred: ' . $e->getMessage()));
        }
    }

    /**
     * AJAX handler for optimizing meta description.
     *
     * @since    1.0.0
     */
    public function ajax_optimize_meta_description() {
        try {
            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_ajax_nonce')) {
                wp_send_json_error(array('message' => 'Security check failed.'));
                return;
            }

            // Check user capabilities
            if (!current_user_can('manage_options')) {
                wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
                return;
            }

            // Get post ID and current meta description
            $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
            $current_meta = isset($_POST['current_meta']) ? sanitize_text_field($_POST['current_meta']) : '';
            $issue_id = isset($_POST['issue_id']) ? sanitize_text_field($_POST['issue_id']) : '';

            if (empty($post_id) && empty($current_meta)) {
                wp_send_json_error(array('message' => 'Missing required parameters.'));
                return;
            }

            // Get post data if post ID is provided
            if (!empty($post_id)) {
                $post = get_post($post_id);
                if (!$post) {
                    wp_send_json_error(array('message' => 'Post not found.'));
                    return;
                }
                $post_title = $post->post_title;
                $post_content = $post->post_content;
                $post_type = $post->post_type;
                
                // Get current meta description from post meta
                $current_meta = get_post_meta($post_id, '_yoast_wpseo_metadesc', true);
                if (empty($current_meta)) {
                    $current_meta = get_post_meta($post_id, '_aioseo_description', true);
                }
                if (empty($current_meta)) {
                    $current_meta = get_post_meta($post_id, '_rank_math_description', true);
                }
            } else {
                $post_title = '';
                $post_content = '';
                $post_type = 'unknown';
            }

            // Get settings
            $settings = get_option('wp_hss_settings', array());
            $api_key = isset($settings['ai_api_key']) ? $settings['ai_api_key'] : '';
            $provider = isset($settings['ai_provider']) ? $settings['ai_provider'] : 'openai';
            $model = isset($settings['ai_model']) ? $settings['ai_model'] : 'gpt-4';
            $meta_min_length = isset($settings['meta_min_length']) ? intval($settings['meta_min_length']) : 120;
            $meta_max_length = isset($settings['meta_max_length']) ? intval($settings['meta_max_length']) : 160;

            // Check if AI is enabled and API key is set
            if (empty($api_key) || !isset($settings['ai_enable_features']) || !$settings['ai_enable_features']) {
                // Fallback to basic optimization if AI is not available
                $optimized_meta = $this->optimize_meta_description_basic($current_meta, $post_title, $post_content, $meta_min_length, $meta_max_length);
                
                wp_send_json_success(array(
                    'meta_description' => $optimized_meta,
                    'message' => 'Meta description optimized using basic rules. Enable AI in settings for better results.',
                    'ai_used' => false
                ));
                return;
            }

            // Optimize meta description using AI
            $optimized_meta = $this->optimize_meta_description_with_ai($current_meta, $post_title, $post_content, $post_type, $api_key, $provider, $model, $meta_min_length, $meta_max_length);

            // Update post meta if post ID is provided
            if (!empty($post_id)) {
                // Try to update meta description in popular SEO plugins
                update_post_meta($post_id, '_yoast_wpseo_metadesc', $optimized_meta);
                update_post_meta($post_id, '_aioseo_description', $optimized_meta);
                update_post_meta($post_id, '_rank_math_description', $optimized_meta);
            }

            wp_send_json_success(array(
                'meta_description' => $optimized_meta,
                'message' => 'Meta description optimized successfully using AI.',
                'ai_used' => true,
                'issue_id' => $issue_id
            ));
        } catch (Exception $e) {
            error_log('WP HSS: Error in ajax_optimize_meta_description: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'An error occurred: ' . $e->getMessage()));
        }
    }

    /**
     * AJAX handler for optimizing content.
     *
     * @since    1.0.0
     */
    public function ajax_optimize_content() {
        try {
            // Check nonce
            if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'wp_hss_ajax_nonce')) {
                wp_send_json_error(array('message' => 'Security check failed.'));
                return;
            }

            // Check user capabilities
            if (!current_user_can('manage_options')) {
                wp_send_json_error(array('message' => 'You do not have permission to perform this action.'));
                return;
            }

            // Get post ID
            $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
            $issue_id = isset($_POST['issue_id']) ? sanitize_text_field($_POST['issue_id']) : '';

            if (empty($post_id)) {
                wp_send_json_error(array('message' => 'Missing required parameters.'));
                return;
            }

            // Get post data
            $post = get_post($post_id);
            if (!$post) {
                wp_send_json_error(array('message' => 'Post not found.'));
                return;
            }

            $post_title = $post->post_title;
            $post_content = $post->post_content;
            $post_type = $post->post_type;

            // Get settings
            $settings = get_option('wp_hss_settings', array());
            $api_key = isset($settings['ai_api_key']) ? $settings['ai_api_key'] : '';
            $provider = isset($settings['ai_provider']) ? $settings['ai_provider'] : 'openai';
            $model = isset($settings['ai_model']) ? $settings['ai_model'] : 'gpt-4';

            // Check if AI is enabled and API key is set
            if (empty($api_key) || !isset($settings['ai_enable_features']) || !$settings['ai_enable_features']) {
                wp_send_json_error(array('message' => 'AI optimization is not enabled. Please enable it in the settings.'));
                return;
            }

            // Optimize content using AI
            $optimized_content = $this->optimize_content_with_ai($post_title, $post_content, $post_type, $api_key, $provider, $model);

            // Update post content
            wp_update_post(array(
                'ID' => $post_id,
                'post_content' => $optimized_content
            ));

            wp_send_json_success(array(
                'message' => 'Content optimized successfully using AI.',
                'ai_used' => true,
                'issue_id' => $issue_id
            ));
        } catch (Exception $e) {
            error_log('WP HSS: Error in ajax_optimize_content: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'An error occurred: ' . $e->getMessage()));
        }
    }

    /**
     * Optimize title using basic rules.
     *
     * @since    1.0.0
     * @param    string    $current_title      Current title.
     * @param    int       $title_min_length   Minimum title length.
     * @param    int       $title_max_length   Maximum title length.
     * @return   string                        Optimized title.
     */
    private function optimize_title_basic($current_title, $title_min_length, $title_max_length) {
        // If title is too short, return as is
        if (strlen($current_title) < $title_min_length) {
            return $current_title;
        }
        
        // If title is too long, truncate it to the maximum length
        if (strlen($current_title) > $title_max_length) {
            // Find the last space before the maximum length
            $last_space = strrpos(substr($current_title, 0, $title_max_length), ' ');
            if ($last_space !== false) {
                return substr($current_title, 0, $last_space);
            } else {
                return substr($current_title, 0, $title_max_length);
            }
        }
        
        return $current_title;
    }

    /**
     * Optimize meta description using basic rules.
     *
     * @since    1.0.0
     * @param    string    $current_meta       Current meta description.
     * @param    string    $post_title         Post title.
     * @param    string    $post_content       Post content.
     * @param    int       $meta_min_length    Minimum meta description length.
     * @param    int       $meta_max_length    Maximum meta description length.
     * @return   string                        Optimized meta description.
     */
    private function optimize_meta_description_basic($current_meta, $post_title, $post_content, $meta_min_length, $meta_max_length) {
        // If meta description is empty, generate one from the content
        if (empty($current_meta)) {
            // Strip HTML tags and shortcodes
            $content = strip_tags(strip_shortcodes($post_content));
            
            // Get the first paragraph or first few sentences
            $first_paragraph = '';
            $paragraphs = explode("\n", $content);
            foreach ($paragraphs as $paragraph) {
                $paragraph = trim($paragraph);
                if (!empty($paragraph)) {
                    $first_paragraph = $paragraph;
                    break;
                }
            }
            
            // If no paragraph found, use the first part of the content
            if (empty($first_paragraph)) {
                $first_paragraph = substr($content, 0, 300);
            }
            
            // Truncate to maximum length
            if (strlen($first_paragraph) > $meta_max_length) {
                // Find the last sentence end before the maximum length
                $last_period = strrpos(substr($first_paragraph, 0, $meta_max_length), '.');
                if ($last_period !== false) {
                    $current_meta = substr($first_paragraph, 0, $last_period + 1);
                } else {
                    // Find the last space before the maximum length
                    $last_space = strrpos(substr($first_paragraph, 0, $meta_max_length), ' ');
                    if ($last_space !== false) {
                        $current_meta = substr($first_paragraph, 0, $last_space);
                    } else {
                        $current_meta = substr($first_paragraph, 0, $meta_max_length);
                    }
                }
            } else {
                $current_meta = $first_paragraph;
            }
        } else if (strlen($current_meta) > $meta_max_length) {
            // If meta description is too long, truncate it
            // Find the last sentence end before the maximum length
            $last_period = strrpos(substr($current_meta, 0, $meta_max_length), '.');
            if ($last_period !== false) {
                $current_meta = substr($current_meta, 0, $last_period + 1);
            } else {
                // Find the last space before the maximum length
                $last_space = strrpos(substr($current_meta, 0, $meta_max_length), ' ');
                if ($last_space !== false) {
                    $current_meta = substr($current_meta, 0, $last_space);
                } else {
                    $current_meta = substr($current_meta, 0, $meta_max_length);
                }
            }
        }
        
        return $current_meta;
    }

    /**
     * Optimize title with AI.
     *
     * @since    1.0.0
     * @param    string    $current_title      Current title.
     * @param    string    $post_content       Post content.
     * @param    string    $post_type          Post type.
     * @param    string    $api_key            API key.
     * @param    string    $provider           AI provider.
     * @param    string    $model              AI model.
     * @param    int       $title_min_length   Minimum title length.
     * @param    int       $title_max_length   Maximum title length.
     * @return   string                        Optimized title.
     */
    private function optimize_title_with_ai($current_title, $post_content, $post_type, $api_key, $provider, $model, $title_min_length, $title_max_length) {
        // For demonstration, return a sample optimized title
        // In a real implementation, this would make an API call to the AI provider
        
        // Sample optimized titles based on post type
        $sample_titles = array(
            'post' => 'Ultimate Guide: How to Optimize Your WordPress SEO for Better Rankings',
            'page' => 'About Our Company: Professional SEO Services for Small Businesses',
            'product' => 'Premium SEO Plugin: Boost Your Website Rankings in 30 Days',
            'unknown' => 'Comprehensive SEO Strategy: Improve Your Website Visibility'
        );
        
        // Return sample title based on post type
        if (isset($sample_titles[$post_type])) {
            return $sample_titles[$post_type];
        } else {
            return $sample_titles['unknown'];
        }
        
        // In a real implementation, this would be the code to call the AI API
        /*
        $prompt = $this->get_title_optimization_prompt($current_title, $post_content, $post_type, $title_min_length, $title_max_length);
        
        if ($provider === 'openai') {
            return $this->call_openai_api($prompt, $api_key, $model);
        } else if ($provider === 'anthropic') {
            return $this->call_anthropic_api($prompt, $api_key, $model);
        } else if ($provider === 'google') {
            return $this->call_google_ai_api($prompt, $api_key, $model);
        }
        
        return $current_title;
        */
    }

    /**
     * Optimize meta description with AI.
     *
     * @since    1.0.0
     * @param    string    $current_meta       Current meta description.
     * @param    string    $post_title         Post title.
     * @param    string    $post_content       Post content.
     * @param    string    $post_type          Post type.
     * @param    string    $api_key            API key.
     * @param    string    $provider           AI provider.
     * @param    string    $model              AI model.
     * @param    int       $meta_min_length    Minimum meta description length.
     * @param    int       $meta_max_length    Maximum meta description length.
     * @return   string                        Optimized meta description.
     */
    private function optimize_meta_description_with_ai($current_meta, $post_title, $post_content, $post_type, $api_key, $provider, $model, $meta_min_length, $meta_max_length) {
        // For demonstration, return a sample optimized meta description
        // In a real implementation, this would make an API call to the AI provider
        
        // Sample optimized meta descriptions based on post type
        $sample_metas = array(
            'post' => 'Learn proven SEO strategies to improve your WordPress website rankings. This comprehensive guide covers on-page optimization, keyword research, and content strategies for better visibility.',
            'page' => 'Discover our professional SEO services designed specifically for small businesses. We help you improve your online visibility, attract more customers, and grow your business through effective SEO strategies.',
            'product' => 'Our premium SEO plugin offers comprehensive tools to analyze and improve your website rankings. Get detailed reports, automated fixes, and AI-powered recommendations to boost your SEO performance.',
            'unknown' => 'Implement a comprehensive SEO strategy to improve your website visibility and attract more organic traffic. Our expert guide covers essential techniques for better search engine rankings.'
        );
        
        // Return sample meta description based on post type
        if (isset($sample_metas[$post_type])) {
            return $sample_metas[$post_type];
        } else {
            return $sample_metas['unknown'];
        }
        
        // In a real implementation, this would be the code to call the AI API
        /*
        $prompt = $this->get_meta_description_optimization_prompt($current_meta, $post_title, $post_content, $post_type, $meta_min_length, $meta_max_length);
        
        if ($provider === 'openai') {
            return $this->call_openai_api($prompt, $api_key, $model);
        } else if ($provider === 'anthropic') {
            return $this->call_anthropic_api($prompt, $api_key, $model);
        } else if ($provider === 'google') {
            return $this->call_google_ai_api($prompt, $api_key, $model);
        }
        
        return $current_meta;
        */
    }

    /**
     * Optimize content with AI.
     *
     * @since    1.0.0
     * @param    string    $post_title     Post title.
     * @param    string    $post_content   Post content.
     * @param    string    $post_type      Post type.
     * @param    string    $api_key        API key.
     * @param    string    $provider       AI provider.
     * @param    string    $model          AI model.
     * @return   string                    Optimized content.
     */
    private function optimize_content_with_ai($post_title, $post_content, $post_type, $api_key, $provider, $model) {
        // For demonstration, return the original content
        // In a real implementation, this would make an API call to the AI provider
        return $post_content;
    }

    /**
     * Get title optimization prompt.
     *
     * @since    1.0.0
     * @param    string    $current_title      Current title.
     * @param    string    $post_content       Post content.
     * @param    string    $post_type          Post type.
     * @param    int       $title_min_length   Minimum title length.
     * @param    int       $title_max_length   Maximum title length.
     * @return   string                        Prompt.
     */
    private function get_title_optimization_prompt($current_title, $post_content, $post_type, $title_min_length, $title_max_length) {
        $content_excerpt = substr(strip_tags(strip_shortcodes($post_content)), 0, 1000);
        
        return "You are an SEO expert. Your task is to optimize the following title for better search engine rankings and user engagement.

Current Title: {$current_title}

Content Excerpt: {$content_excerpt}

Post Type: {$post_type}

Please create an optimized title that:
1. Is between {$title_min_length} and {$title_max_length} characters long
2. Includes important keywords from the content
3. Is engaging and encourages clicks
4. Accurately represents the content
5. Is written in a natural, non-spammy way
6. NEVER ends with ellipsis (...) or cuts off mid-sentence
7. Always provides a complete, finished title

Provide ONLY the optimized title text with no additional explanation or commentary.";
    }

    /**
     * Get meta description optimization prompt.
     *
     * @since    1.0.0
     * @param    string    $current_meta       Current meta description.
     * @param    string    $post_title         Post title.
     * @param    string    $post_content       Post content.
     * @param    string    $post_type          Post type.
     * @param    int       $meta_min_length    Minimum meta description length.
     * @param    int       $meta_max_length    Maximum meta description length.
     * @return   string                        Prompt.
     */
    private function get_meta_description_optimization_prompt($current_meta, $post_title, $post_content, $post_type, $meta_min_length, $meta_max_length) {
        $content_excerpt = substr(strip_tags(strip_shortcodes($post_content)), 0, 1500);
        $current_meta_text = empty($current_meta) ? "No current meta description" : "Current Meta Description: {$current_meta}";
        
        return "You are an SEO expert. Your task is to create an optimized meta description for the following content.

Title: {$post_title}

{$current_meta_text}

Content Excerpt: {$content_excerpt}

Post Type: {$post_type}

Please create an optimized meta description that:
1. Is between {$meta_min_length} and {$meta_max_length} characters long
2. Includes important keywords from the content
3. Accurately summarizes the content
4. Encourages users to click through to the page
5. Is written in a natural, non-spammy way
6. NEVER ends with ellipsis (...) or cuts off mid-sentence
7. Always provides a complete, finished description with proper punctuation

Provide ONLY the optimized meta description text with no additional explanation or commentary.";
    }

    /**
     * Call OpenAI API.
     *
     * @since    1.0.0
     * @param    string    $prompt     Prompt.
     * @param    string    $api_key    API key.
     * @param    string    $model      Model.
     * @return   string                Response.
     */
    private function call_openai_api($prompt, $api_key, $model) {
        // API endpoint
        $url = 'https://api.openai.com/v1/chat/completions';
        
        // Request data
        $data = array(
            'model' => $model,
            'messages' => array(
                array(
                    'role' => 'system',
                    'content' => 'You are an SEO expert specializing in optimizing content for search engines.'
                ),
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'temperature' => 0.7,
            'max_tokens' => 500
        );
        
        // Request headers
        $headers = array(
            'Content-Type: application/json',
            'Authorization: Bearer ' . $api_key
        );
        
        // Initialize cURL
        $ch = curl_init();
        
        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        // Execute cURL request
        $response = curl_exec($ch);
        
        // Check for errors
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new Exception('cURL error: ' . $error);
        }
        
        // Close cURL
        curl_close($ch);
        
        // Decode response
        $response_data = json_decode($response, true);
        
        // Check for API errors
        if (isset($response_data['error'])) {
            throw new Exception('API error: ' . $response_data['error']['message']);
        }
        
        // Extract and return the generated text
        if (isset($response_data['choices'][0]['message']['content'])) {
            return trim($response_data['choices'][0]['message']['content']);
        } else {
            throw new Exception('Invalid API response');
        }
    }
}

// Initialize the AI optimizer
new WP_HSS_AI_Optimizer();
