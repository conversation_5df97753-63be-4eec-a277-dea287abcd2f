<?php
/**
 * Database operations for the plugin.
 *
 * @since      1.0.0
 */
class WP_HSS_DB {

    /**
     * Create custom database tables.
     *
     * @since    1.0.0
     */
    public static function create_tables() {
        // SAFETY CHECK: Don't create tables during plugin activation
        if (defined('WP_HSS_ACTIVATING') && WP_HSS_ACTIVATING) {
            error_log('WP HSS: Skipping database table creation during plugin activation');
            return;
        }

        // SAFETY CHECK: Make sure we have the required WordPress functions
        if (!function_exists('dbDelta')) {
            if (!file_exists(ABSPATH . 'wp-admin/includes/upgrade.php')) {
                error_log('WP HSS: Cannot create database tables - WordPress upgrade.php file not found');
                return;
            }
            require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        }

        // SAFETY CHECK: Make sure we have a valid wpdb object
        global $wpdb;
        if (!isset($wpdb) || !is_object($wpdb)) {
            error_log('WP HSS: Cannot create database tables - wpdb object not available');
            return;
        }

        // Get charset_collate safely
        $charset_collate = '';
        if (method_exists($wpdb, 'get_charset_collate')) {
            $charset_collate = $wpdb->get_charset_collate();
        }

        try {
            $table_issues = $wpdb->prefix . 'hss_issues';
            $table_scans = $wpdb->prefix . 'hss_scans';

            // Check if tables already exist to avoid unnecessary operations
            $issues_exists = false;
            $scans_exists = false;

            try {
                $issues_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_issues'") === $table_issues;
            } catch (Exception $e) {
                error_log('WP HSS: Error checking if issues table exists: ' . $e->getMessage());
            }

            try {
                $scans_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_scans'") === $table_scans;
            } catch (Exception $e) {
                error_log('WP HSS: Error checking if scans table exists: ' . $e->getMessage());
            }

            // Only create tables if they don't exist
            if (!$issues_exists) {
                $sql_issues = "CREATE TABLE IF NOT EXISTS $table_issues (
                    issue_id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                    post_id BIGINT(20) UNSIGNED NOT NULL,
                    issue_type VARCHAR(50) NOT NULL,
                    severity VARCHAR(10) NOT NULL,
                    details LONGTEXT NOT NULL,
                    status VARCHAR(20) NOT NULL DEFAULT 'open',
                    scan_id VARCHAR(32) NOT NULL,
                    date_found DATETIME NOT NULL,
                    last_checked DATETIME NOT NULL,
                    PRIMARY KEY  (issue_id),
                    KEY post_id (post_id),
                    KEY issue_type (issue_type),
                    KEY status (status),
                    KEY scan_id (scan_id)
                ) $charset_collate;";

                // Use try/catch for table creation
                try {
                    dbDelta($sql_issues);
                    error_log('WP HSS: Issues table created or updated successfully');
                } catch (Exception $e) {
                    error_log('WP HSS: Error creating issues table: ' . $e->getMessage());
                }
            }

            if (!$scans_exists) {
                $sql_scans = "CREATE TABLE IF NOT EXISTS $table_scans (
                    scan_id VARCHAR(32) NOT NULL,
                    scan_type VARCHAR(20) NOT NULL,
                    status VARCHAR(20) NOT NULL,
                    start_time DATETIME NOT NULL,
                    end_time DATETIME NULL,
                    items_scanned INT(11) NOT NULL DEFAULT 0,
                    items_total INT(11) NOT NULL DEFAULT 0,
                    issues_found INT(11) NOT NULL DEFAULT 0,
                    user_id BIGINT(20) UNSIGNED NOT NULL,
                    PRIMARY KEY  (scan_id)
                ) $charset_collate;";

                // Use try/catch for table creation
                try {
                    dbDelta($sql_scans);
                    error_log('WP HSS: Scans table created or updated successfully');
                } catch (Exception $e) {
                    error_log('WP HSS: Error creating scans table: ' . $e->getMessage());
                }
            }

        } catch (Exception $e) {
            error_log('WP HSS: Error creating database tables: ' . $e->getMessage());
        }
    }

    /**
     * Verify that the database tables exist and create them if they don't.
     *
     * @since    1.0.0
     * @return   bool    True if tables exist or were created, false on failure.
     */
    public static function verify_tables() {
        global $wpdb;

        // SAFETY CHECK: Make sure we have a valid wpdb object
        if (!isset($wpdb) || !is_object($wpdb)) {
            error_log('WP HSS: Cannot verify tables - wpdb object not available');
            return false;
        }

        try {
            // Load WordPress upgrade functions if not already loaded
            if (!function_exists('dbDelta')) {
                require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
            }

            $table_issues = $wpdb->prefix . 'hss_issues';
            $table_scans = $wpdb->prefix . 'hss_scans';

            // Check if tables exist
            $issues_exists = false;
            $scans_exists = false;

            // Use try-catch for each query to prevent fatal errors
            try {
                $issues_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_issues'") === $table_issues;
            } catch (Exception $e) {
                error_log('WP HSS: Error checking issues table: ' . $e->getMessage());
            }

            try {
                $scans_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_scans'") === $table_scans;
            } catch (Exception $e) {
                error_log('WP HSS: Error checking scans table: ' . $e->getMessage());
            }

            // If either table doesn't exist, create them
            if (!$issues_exists || !$scans_exists) {
                error_log("WP HSS: Database tables missing. Creating tables...");
                self::create_tables();

                // Verify again after creation
                try {
                    $issues_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_issues'") === $table_issues;
                } catch (Exception $e) {
                    error_log('WP HSS: Error verifying issues table after creation: ' . $e->getMessage());
                }

                try {
                    $scans_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_scans'") === $table_scans;
                } catch (Exception $e) {
                    error_log('WP HSS: Error verifying scans table after creation: ' . $e->getMessage());
                }

                if (!$issues_exists || !$scans_exists) {
                    error_log("WP HSS: Failed to create database tables.");
                    // Return true anyway to prevent repeated attempts that might cause errors
                    return true;
                }

                error_log("WP HSS: Database tables created successfully.");
            }

            return true;
        } catch (Exception $e) {
            error_log('WP HSS: Error verifying database tables: ' . $e->getMessage());
            // Return true to prevent repeated attempts that might cause errors
            return true;
        }
    }

    /**
     * Add a new issue to the database.
     *
     * @since    1.0.0
     * @param    array    $issue    Issue data.
     * @return   int|false          Issue ID or false on failure.
     */
    public static function add_issue($issue) {
        global $wpdb;

        try {
            $table_issues = $wpdb->prefix . 'hss_issues';

            // Verify table exists
            if ($wpdb->get_var("SHOW TABLES LIKE '$table_issues'") !== $table_issues) {
                self::verify_tables();
            }

            $result = $wpdb->insert(
                $table_issues,
                array(
                    'post_id' => $issue['post_id'],
                    'issue_type' => $issue['issue_type'],
                    'severity' => $issue['severity'],
                    'details' => json_encode($issue['details']),
                    'status' => 'open',
                    'scan_id' => $issue['scan_id'],
                    'date_found' => current_time('mysql'),
                    'last_checked' => current_time('mysql')
                ),
                array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
            );

            if ($result) {
                return $wpdb->insert_id;
            }

            return false;
        } catch (Exception $e) {
            error_log('WP HSS: Error adding issue: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get issues from the database.
     *
     * @since    1.0.0
     * @param    array    $args    Query arguments.
     * @return   array             Array of issues.
     */
    public static function get_issues($args = array()) {
        global $wpdb;

        $defaults = array(
            'post_id' => 0,
            'issue_type' => '',
            'severity' => '',
            'status' => 'open',
            'scan_id' => '',
            'orderby' => 'date_found',
            'order' => 'DESC',
            'limit' => 100,
            'offset' => 0
        );

        $args = wp_parse_args($args, $defaults);

        $table_issues = $wpdb->prefix . 'hss_issues';

        $where = array();
        $where_format = array();

        if (!empty($args['post_id'])) {
            $where[] = 'post_id = %d';
            $where_format[] = $args['post_id'];
        }

        if (!empty($args['issue_type'])) {
            $where[] = 'issue_type = %s';
            $where_format[] = $args['issue_type'];
        }

        if (!empty($args['severity'])) {
            $where[] = 'severity = %s';
            $where_format[] = $args['severity'];
        }

        if (!empty($args['status'])) {
            $where[] = 'status = %s';
            $where_format[] = $args['status'];
        }

        if (!empty($args['scan_id'])) {
            $where[] = 'scan_id = %s';
            $where_format[] = $args['scan_id'];
        }

        $where_clause = '';
        if (!empty($where)) {
            $where_clause = 'WHERE ' . implode(' AND ', $where);
        }

        $orderby = sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']);
        if (!$orderby) {
            $orderby = 'date_found DESC';
        }

        $limit = '';
        if ($args['limit'] > 0) {
            $limit = $wpdb->prepare('LIMIT %d, %d', $args['offset'], $args['limit']);
        }

        $query = "SELECT * FROM $table_issues $where_clause ORDER BY $orderby $limit";

        if (!empty($where_format)) {
            $query = $wpdb->prepare($query, $where_format);
        }

        $results = $wpdb->get_results($query, ARRAY_A);

        if ($results) {
            foreach ($results as &$result) {
                $result['details'] = json_decode($result['details'], true);
            }
        }

        return $results;
    }

    /**
     * Update issue status.
     *
     * @since    1.0.0
     * @param    int       $issue_id    Issue ID.
     * @param    string    $status      New status.
     * @return   bool                   True on success, false on failure.
     */
    public static function update_issue_status($issue_id, $status) {
        global $wpdb;

        $table_issues = $wpdb->prefix . 'hss_issues';

        $result = $wpdb->update(
            $table_issues,
            array(
                'status' => $status,
                'last_checked' => current_time('mysql')
            ),
            array('issue_id' => $issue_id),
            array('%s', '%s'),
            array('%d')
        );

        return $result !== false;
    }

    /**
     * Start a new scan record.
     *
     * @since    1.0.0
     * @param    string    $scan_type    Type of scan.
     * @param    int       $items_total  Total items to scan.
     * @return   string                  Scan ID.
     */
    public static function start_scan_record($scan_type, $items_total = 0) {
        global $wpdb;

        $table_scans = $wpdb->prefix . 'hss_scans';

        $scan_id = md5(uniqid('', true));

        $wpdb->insert(
            $table_scans,
            array(
                'scan_id' => $scan_id,
                'scan_type' => $scan_type,
                'status' => 'running',
                'start_time' => current_time('mysql'),
                'items_total' => $items_total,
                'user_id' => get_current_user_id()
            ),
            array('%s', '%s', '%s', '%s', '%d', '%d')
        );

        return $scan_id;
    }

    /**
     * End a scan record.
     *
     * @since    1.0.0
     * @param    string    $scan_id         Scan ID.
     * @param    string    $status          Final status.
     * @param    int       $items_scanned   Number of items scanned.
     * @param    int       $issues_found    Number of issues found.
     * @return   bool                       True on success, false on failure.
     */
    public static function end_scan_record($scan_id, $status, $items_scanned, $issues_found) {
        global $wpdb;

        $table_scans = $wpdb->prefix . 'hss_scans';

        $result = $wpdb->update(
            $table_scans,
            array(
                'status' => $status,
                'end_time' => current_time('mysql'),
                'items_scanned' => $items_scanned,
                'issues_found' => $issues_found
            ),
            array('scan_id' => $scan_id),
            array('%s', '%s', '%d', '%d'),
            array('%s')
        );

        return $result !== false;
    }
}
